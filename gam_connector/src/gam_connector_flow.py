from typing import Any, Optional

from utils.extract import plain_extract
from utils.flow_manager import FlowManager
from utils.schemas.lambdas import EventSchema
from utils.task_handlers import (
    TaskDeserializeData,
    TaskExtractData,
    TaskSendMessageToSQS,
)


def flow(event: EventSchema, global_variables: Optional[dict[str, Any]]) -> Any:
    """
    Lambda function that extracts data and sends it to an SQS queue.

    Args:
        event (Dict): AWS Lambda event containing S3 trigger information
        global_variables (Optional[Dict]): AWS Lambda context
    """

    global_variables["connector"] = plain_extract

    _flow = FlowManager(
        name="gam_connector",
        tasks=[TaskExtractData(), TaskDeserializeData(), TaskSendMessageToSQS()],
        event=event,
        global_variables=global_variables,
    )
    return _flow()
