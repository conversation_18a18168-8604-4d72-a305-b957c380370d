# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
pytest.ini

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
.pytest_cache/v/cache/lastfailed
.pytest_cache/v/cache/nodeids
.pytest_cache/v/cache/stepwise
.pytest_cache/v/cache/stepwise/true.dat
unittest-xml-reporting/
test_output.mv2/
test_output.mv2/initialization/
test_output.mv2/cleaning/
test_output.mv2/root-report/index.html
.noseids
.lektorproject

# Translations
*.mo
*.pot

# Django stuff:
*.log
*.pot
*.pyc
*.sqlite3
*.env

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Celery stuff
celerybeat-schedule.*
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
venv/.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
.dmypy.json.*/

# AWS Lambda specific
.aws-sam/
.template.yaml

# IDEs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Operating System files
.DS_Store
Thumbs.db

private_key_jwk.json