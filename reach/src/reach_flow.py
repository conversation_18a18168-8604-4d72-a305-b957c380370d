from typing import Any

from utils.flow_manager import FlowManager
from utils.LoggerService import logger_service
from utils.schemas.lambdas import EventSchema
from utils.task_handlers import TaskGetPackageMetadata, TaskUpdatePackageMetadata


def flow(
    event: EventSchema,
    global_variables: dict[str, Any],
) -> None:
    """
    Main flow for processing Reach API updates

    Args:
        event: Lambda event
        global_variables: Dictionary containing shared variables for the flow
    """
    logger_service.info("************ IZMA / Reach Flow Started ************")

    try:
        # Extract sidecar record from DynamoDB stream lambda event
        logger_service.info(
            "Notifying record for sidecar %s to reach",
            global_variables.get("dynamodb_stream").sidecar_id,
        )
        _flow = FlowManager(
            name="Reach Flow",
            tasks=[
                TaskGetPackageMetadata(),
                TaskUpdatePackageMetadata(),
            ],
            event=event,
            global_variables=global_variables,
        )

        return _flow()

    except Exception as error:
        logger_service.error("Flow failed: %s", error)
        raise error
