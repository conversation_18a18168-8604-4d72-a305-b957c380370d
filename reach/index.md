# Reach Metadata Updater

version: 1.0.0

Created Date: 2024-03-27

## Brief description

AWS Lambda-based serverless application designed to update metadata in the Reach API. This service processes records originating from Wonderland, retrieves and updates package metadata through the Reach API, and maintains status tracking in DynamoDB.

## Authors

- <PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>

## Index:

- Infra


- Code


- Configuration and running code


- Test (integration test)

## Links to repositories and main project confluence

- Repository: [Tacdev Project - Lambda Reach](https://gitlab.disney.com/reach-engine/workflows/tacdev-projects/-/tree/development/reach?ref_type=heads)
- Confluence: [Reach Documentation](https://confluence.disney.com/display/REACH/Code+Documentation+Reach+Metadata+Updater)