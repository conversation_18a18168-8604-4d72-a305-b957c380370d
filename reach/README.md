# Code Documentation Reach Metadata Updater

## Architecture

The Reach project implements a serverless architecture using AWS Lambda for updating metadata in the Reach API. The core components are:

1. **Lambda Function Layer**
   - Main handler (`lambda_reach.py`)
   - Processing flow (`reach_flow.py`)
   - Utility modules for configuration and AWS services

2. **AWS Services Integration**
   - DynamoDB for status tracking and data persistence
   - Lambda for serverless execution
   - Secrets Manager for credential storage

3. **External API Integration**
   - Reach API for metadata updates
   - Token-based authentication
   - RESTful service communication

## Folder Structure

```
reach/
├── src/
│   ├── lambda_reach.py     # Main Lambda handler
│   ├── reach_flow.py       # Core processing logic
│   └── requirements.txt    # Dependencies
├── test/                   # Test suite directory
├── .pytest_cache/          # pytest cache
├── .venv/                  # Virtual environment
├── .reach-ci.yml           # CI configuration
└── README.md               # Project documentation
```

## Tasks Diagram

<p style="display: flex; justify-content: center;">
    <img src="https://confluence.disney.com/download/attachments/1668898410/reach.png?version=1&modificationDate=1742533473603&api=v2" height="480px" alt="Wonderland Task Diagram" style="max-height:70vh;" />
</p>

## Tasks

### 1. Metadata Update Pipeline

The primary workflow consists of these sequential tasks:

- **Lambda Event Processing**
  - Extracts sidecar ID from event payload
  - Initializes DynamoDB client and token service
  - Sets up logging context for traceability

- **Record Retrieval and Validation**
  - Implements `Task.validate_and_update_dynamodb_record_status` to verify record integrity
  - Checks for required fields and correct status values
  - Ensures process preconditions are met before proceeding

- **Authentication Management**
  - Utilizes `ReachTokenService` to generate secure API tokens
  - Retrieves endpoint configurations from secrets manager
  - Handles token lifecycle and security

- **API Integration**
  - Employs `Task.fetch_reach_package_metadata` to fetch current metadata
  - Uses `Task.update_reach_package_metadata` to modify specific attributes
  - Handles HTTP request/response cycle with error handling

### 2. Status Management

- **DynamoDB Status Tracking**
  - Records processing state transitions in DynamoDB
  - Defined status workflow:
    - `wl_successful_ingestion` (pre-condition)
    - `wl_reach_completed` (success)
    - `Error` (failure)
  - Maintains audit trail with timestamps

- **Error Handling**
  - Comprehensive exception handling at all process stages
  - Status updates on failure conditions
  - Detailed error logging for operational support

### 3. Integration Support

- **Wonderland Integration**
  - Processes records originating from Wonderland service
  - Maintains consistent status tracking across services
  - Completes cross-service workflow chain

- **Custom HTTP Handling**
  - Implements HTTP adapter for secure connections
  - Manages SSL verification requirements
  - Ensures reliable API communication

## Services

1. **Reach**
   - Primary service for metadata updates
   - Authentication and API interaction
   - Process orchestration and error handling

2. **Wonderland**
   - Upstream service generating conversion events
   - Provides sidecar IDs for processing
   - Initial record creation

3. **DynamoDB**
   - Persistent storage for status tracking
   - Cross-service data exchange
   - Record validation and integrity

## Tools

1. **Token Service**
   - Secure authentication management
   - API credential handling
   - Endpoint configuration

2. **Logging Service**
   - Structured logging
   - Error tracking
   - Operational visibility

3. **Task Utilities**
   - Modular processing components
   - Reusable validation logic
   - Status management helpers

## Configuration and running code
### Environment Variables and Configuration

The Reach Metadata Updater service relies on several key configuration parameters extracted from event payloads and DynamoDB tables:

- **Event Parameters**: The service extracts the `sidecar_id` from incoming Lambda events, which serves as the primary identifier for record processing. In `reach_flow.py`, this ID is used to construct the DynamoDB key for retrieving the associated record.

- **DynamoDB Configuration**: The service uses a table name specified in AWS Secrets Manager, accessed via `token_service.reach_secrets["sidecar_table"]` in `lambda_reach.py`. This table stores critical record data including `reach_package_id`, `attribute_name`, and `wonderland_id` which are essential for the metadata update process.

- **Reach API Settings**: Authentication tokens and the Reach API endpoint URL are managed through the `ReachTokenService`, with the base URL retrieved from secrets via `token_service.reach_secrets["reach_url"]`. These parameters enable secure communication with the Reach API for metadata retrieval and updates.

The configuration approach follows a centralized pattern, with sensitive information stored in AWS Secrets Manager and accessed through the token service, promoting security and maintainability.


## Links to Repository

The project is maintained in a [Tacdev Project - Lambda Reach](https://gitlab.disney.com/reach-engine/workflows/tacdev-projects/-/tree/development/reach?ref_type=heads). Access requires appropriate permissions and can be granted through the organization's DevOps team.

This documentation provides a technical overview of the Reach project's architecture and components, following Python best practices and AWS serverless patterns.
