image: python:3.12.6

workflow:
  rules:
    - changes:
      - reach/**/*
      when: always
    - when: never

variables:
  PACKAGE: "reach.zip"
  ARTIFACT: "reach"
  LAMBDA: "tacdev-reach-to-wonderland-${ENV}"
  BUCKET: "tacdev-artifacts-${ENV}"
  LAYER: "shared-layer-${ENV}"

stages:
  - test
  - build
  - deploy
  - rollback

# Test job for reach
test-code-reach:
  stage: test
  script:
    - echo "Testing code"
    - pip install uv
    - uv sync --all-groups
    - cd reach/
    - uv run black .
    - uv run isort .
    # uv run pytest .
  allow_failure: false
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push" || $PARENT_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - reach/**/*

# Build job for creating the artifact
create-artifact-reach:
  stage: build
  before_script:
    - apt-get update && apt-get install -y zip
  script:
    - echo "Creating Lambda package for ${CI_COMMIT_REF_NAME}"
    - cp pyproject.toml reach/
    - cd reach/
    - zip -r $PACKAGE src/
    - mv $PACKAGE ../  # Move the ZIP to the root
  artifacts:
    paths:
      - $PACKAGE
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push" || $PARENT_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - reach/**/*

# template to deploy AWS Lambda
.deploy-template:
  stage: deploy
  before_script:
    - bash scripts/setup_aws.sh
  script:
    - echo "Deploying Lambda function for $CI_ENVIRONMENT_NAME"
    - bash scripts/deploy_lambda.sh $PACKAGE $LAMBDA $BUCKET $ARTIFACT
    - bash scripts/wait_for_lambda.sh $LAMBDA

    # Add layer to Lambda
    - LATEST_VERSION=$(aws lambda list-layer-versions --layer-name $LAYER --query 'LayerVersions[0].LayerVersionArn' --output text | head -n 1)
    - aws lambda update-function-configuration --function-name $LAMBDA --layers $LATEST_VERSION
    - bash scripts/wait_for_lambda.sh $LAMBDA
    - aws lambda update-function-configuration --function-name $LAMBDA --environment "Variables={ENV=${CI_ENVIRONMENT_NAME}}"
    - echo "Layer $LATEST_VERSION successfully applied to Lambda function $LAMBDA"

# Deploy job for the sandbox environment
deploy-reach-sbx:
  extends: .deploy-template
  environment:
    name: sbx
  rules:
    - if: $CI_COMMIT_TAG =~ /^release\/[0-9]+\.[0-9]+\.[0-9]+-sbx$/
      changes:
        - reach/**/*
      when: manual

# Deploy job for the qa environment
deploy-reach-qa:
  extends: .deploy-template
  environment:
    name: qa
  rules:
    - if: $CI_COMMIT_BRANCH == "qa" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - reach/**/*
      when: manual

# Deploy job for the production environment
deploy-reach-prod:
  extends: .deploy-template
  environment:
    name: prod
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - reach/**/*
      when: manual

# Rollback
.rollback-template:
  stage: rollback
  before_script:
    - bash scripts/setup_aws.sh
  script:
    - echo "Rolling back Lambda function for $CI_ENVIRONMENT_NAME"
    # Get the current version of the alias
    - CURRENT_VERSION=$(aws lambda get-alias --function-name $LAMBDA --name $CI_ENVIRONMENT_NAME --query 'FunctionVersion' --output text)
    - echo "Current alias version is $CURRENT_VERSION"
    # Calculate previous version
    - ROLLBACK_VERSION=$((CURRENT_VERSION - 1))
    - echo "Rolling back to version $ROLLBACK_VERSION"
    # Execute rollback
    - bash scripts/rollback_lambda.sh $LAMBDA $CI_ENVIRONMENT_NAME $ROLLBACK_VERSION

# Rollback job for the sandbox environment
rollback-reach-sbx:
  extends: .rollback-template
  environment:
    name: sbx
  rules:
    - if: $CI_COMMIT_TAG =~ /^release\/[0-9]+\.[0-9]+\.[0-9]+-sbx$/
      changes:
        - reach/**/*
      when: manual
  allow_failure: true

# Rollback job for the qa environment
rollback-reach-qa:
  extends: .rollback-template
  environment:
    name: qa
  rules:
    - if: $CI_COMMIT_BRANCH == "qa" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - reach/**/*
      when: manual
  allow_failure: true

# Rollback job for the production environment
rollback-reach-prod:
  extends: .rollback-template
  environment:
    name: prod
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - reach/**/*
      when: manual
  allow_failure: true
