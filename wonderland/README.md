# Code Documentation Wonderland json to yml converter

## Architecture

The Wonderland project implements a serverless architecture using AWS Lambda for file format conversion and processing. The core components are:

1. **Lambda Function Layer**
   - Main handler (`lambda_wonderland.py`)
   - Processing flow (`wonderland_flow.py`)
   - Utility modules for configuration and AWS services

2. **AWS Services Integration**
   - S3 for file storage
   - DynamoDB for status tracking
   - KMS for encryption
   - Lambda for serverless execution

3. **Configuration Management**
   - Environment-based settings
   - Centralized configuration via `config.py`

## Folder Structure

```
wonderland/
├── src/
│   ├── __init__.py
│   ├── lambda_wonderland.py     # Main Lambda handler
│   ├── wonderland_flow.py       # Core processing logic
│   └── requirements.txt         # Dependencies
├── tests/                       # Test suite directory
├── .pytest_cache/              # pytest cache
├── .venv/                      # Virtual environment
├── .wonderland-ci.yml          # CI configuration
└── README.md                   # Project documentation
```

## Tasks Diagram

<div style="text-align: center;">
    <img src="https://confluence.disney.com/download/attachments/1585178973/wonderland_diagram.jpeg?version=1&modificationDate=1742522085361&api=v2" alt="Wonderland Task Diagram" height="480px">
</div>

## Tasks

### 1. JSON to YAML Conversion Pipeline

The primary workflow consists of these sequential tasks:

- **S3 Event Trigger Processing**
  - Extracts file and bucket information from S3 events
  - Validates input parameters and formats
  - Handles path construction for source and destination

- **Data Extraction**
  - Implements `Task.download_s3_object` to retrieve JSON files from S3
  - Uses connector pattern for flexible data sourcing
  - Handles AWS SDK interactions via abstraction layer

- **Format Conversion**
  - Utilizes `Task.convert_json_to_yaml` for transformation
  - Maintains structure integrity during conversion
  - Handles serialization edge cases and encoding issues
  - Supports configuration options like `sort_keys`

- **Result Persistence**
  - Employs `Task.load_data` to upload processed YAML files
  - Maintains folder structures with parametrized destination paths
  - Creates appropriate file naming conventions

### 2. Status Management

- **DynamoDB Record Creation**
  - Creates initial status records in DynamoDB using `Task.update_wonderland_status`
  - Extracts critical metadata including:
    - `sidecar_id` (partition key)
    - `attribute_name`
    - `reach_package_id`
  - Records timestamps and processing state

- **Status Updates**
  - Tracks conversion progress through defined states:
    - `wl_in_progress`
    - `wl_successful_ingestion`
    - `FAILED`
    - `COMPLETED`
  - Enables recovery from failures via `RetryHandler`

### 3. Error Handling

- **Structured Exception Management**
  - Implements validation for JSON syntax and structure
  - Provides detailed error context for troubleshooting
  - Maintains process integrity through transaction-like operations

- **Retry Mechanism**
  - Configurable retry policies via settings management
  - Graceful failure handling with appropriate status updates
  - Comprehensive logging for operational visibility

### 4. Integration Support

- **Cross-Service Communication**
  - Integration with Reach service for status synchronization
  - SQS message generation for downstream processing
  - DynamoDB table updates for status tracking

This modular task structure ensures separation of concerns while maintaining a coherent workflow for JSON to YAML conversion and status tracking across the AWS serverless ecosystem.



## Services

1. **Wonderland**
   - Primary service for file conversion
   - Handles JSON to YAML transformation
   - Manages process flow

2. **Reach**
   - Status tracking and monitoring
   - Process state management

3. **SQS**
   - Message queue integration
   - Asynchronous processing support

## Tools

1. Configuration Management
   - Environment-based settings
   - Secure credential handling

2. Logging Service
   - Structured logging
   - Error tracking

3. Retry Handler
   - Automatic retry mechanism
   - Failure recovery

## Configuration and running code
### Environment Variables and Configuration

The Wonderland Lambda function relies on a configuration-driven approach managed through DynamoDB. The function
retrieves its settings from the `tacdev-event-config-{ENV}` table using the project name "wonderland" as the key. The
configuration includes the following essential parameters:

- **Status Table**: `tacdev-logs-sbx` - Tracks processing status of files
- **Landing Bucket**: `reachengine-assets-to-wonderland-sandbox` - Source for incoming files
- **Destination Bucket**: `reachengine-assets-to-wonderland-sandbox` - Target for processed files
- **Destination Folder**: `hotfolder-stg` - Specific folder for output files
- **Source Folder**: `json-to-yaml-sbx` - Location of source files

The Lambda function initializes these settings through the `SettingsManagerService`, which loads the configuration at
runtime. Status tracking is implemented using a DynamoDB client that creates and updates records in the status table
throughout the processing lifecycle. The environment is controlled via the `ENV` variable, which determines which
configuration set to use.

## Links to Repository

The project is maintained in a [Tacdev Project - Lambda Wonderland](https://gitlab.disney.com/reach-engine/workflows/tacdev-projects/-/tree/development/wonderland?ref_type=heads). Access requires appropriate permissions and can be granted through the organization's DevOps team.

This documentation provides a technical overview of the Wonderland project's architecture and components, following Python best practices and AWS serverless patterns.
