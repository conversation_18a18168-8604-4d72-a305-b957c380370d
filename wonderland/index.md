# Wonderland json to yml converter

version: 1.0.0

Created Date: 2024-03-11

## Brief description

Serverless application built with AWS Lambda for converting JSON files to YAML format. This service processes files stored in S3, performs format conversion while maintaining structure integrity, updates status records in DynamoDB, and enables downstream processing through integration with other services.

## Authors

- <PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>

## Table of Contents:

- Infra
  - AWS Lambda
  - Amazon S3
  - DynamoDB
  - KMS
  - CI/CD Pipeline (.wonderland-ci.yml)

- Code
  - Lambda Handler (lambda_wonderland.py)
  - Processing Flow (wonderland_flow.py)
  - JSON to YAML Conversion
  - Status Management Logic

- Configuration and running code
  - Environment Variables
  - AWS Service Integration
  - Centralized Configuration (config.py)
  - Deployment Process

- Test (integration test)
  - Unit Tests
  - Integration with Reach
  - S3 Event Trigger Testing

## Links to repositories and main project confluence

- Repository: [Tacdev Project - Lambda Wonderland](https://gitlab.disney.com/reach-engine/workflows/tacdev-projects/-/tree/development/wonderland?ref_type=heads)
- Confluence: [Wonderland Documentation](https://confluence.disney.com/display/REACH/Reach+to+Wonderland+-+JSON+to+YAML+Converter+Lambda)