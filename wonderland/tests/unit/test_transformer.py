import json
import os

import pytest
import yaml
from utils.tasks.transforms import convert_json_to_yaml


def test_transform_json_to_yaml():
    json_data = {"name": "test", "values": [1, 2, 3], "nested": {"key": "value"}}

    yaml_result = convert_json_to_yaml(json_data)
    assert isinstance(yaml_result, str)

    # Parse back to verify structure
    parsed_yaml = yaml.safe_load(yaml_result)
    assert parsed_yaml == json_data


def test_transform_json_to_yaml_with_string():
    json_str = json.dumps(
        {"name": "test", "values": [1, 2, 3], "nested": {"key": "value"}}
    )

    yaml_result = convert_json_to_yaml(json_str)
    assert isinstance(yaml_result, str)

    # Parse back to verify structure
    parsed_yaml = yaml.safe_load(yaml_result)
    assert parsed_yaml["name"] == "test"
    assert parsed_yaml["values"] == [1, 2, 3]
    assert parsed_yaml["nested"]["key"] == "value"


def test_transform_complex_structure():
    with open(
        os.path.join(os.path.dirname(__file__), "../files/complex.json"), "r"
    ) as f:
        complex_json = json.load(f)

    yaml_result = convert_json_to_yaml(complex_json)
    assert isinstance(yaml_result, str)

    # Transform back to verify structure
    parsed_yaml = yaml.safe_load(yaml_result)
    assert parsed_yaml == complex_json


def test_transform_with_sort_keys():
    json_data = {"c": 3, "a": 1, "b": 2}

    yaml_result = convert_json_to_yaml(json_data, sort_keys=True)
    assert isinstance(yaml_result, str)

    # Verify keys are sorted
    lines = yaml_result.strip().split("\n")
    assert lines[0].startswith("a:")
    assert lines[1].startswith("b:")
    assert lines[2].startswith("c:")


def test_transform_invalid_json():
    invalid_json = '{"key": invalid}'

    with pytest.raises(ValueError) as exc_info:
        convert_json_to_yaml(invalid_json)
    assert "Invalid JSON format" in str(exc_info.value)


def test_transform_invalid_type():
    invalid_input = object()

    with pytest.raises(ValueError) as exc_info:
        convert_json_to_yaml(invalid_input)
    assert "Error converting to YAML" in str(exc_info.value)
