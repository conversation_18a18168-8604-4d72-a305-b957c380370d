import json
import os

import pytest
from utils.tasks.data_processing import convert_from_dict, convert_to_dict


def test_serialize_to_json():
    data = {
        "string": "test",
        "number": 42,
        "float": 3.14,
        "boolean": True,
        "null": None,
        "array": [1, 2, 3],
        "nested": {"key": "value"},
    }

    result = convert_from_dict(data, "json")
    assert isinstance(result, str)
    parsed = json.loads(result)
    assert parsed == data


def test_serialize_complex_data():
    data = {
        "arrays": {
            "numbers": [1, 2, 3],
            "strings": ["a", "b", "c"],
            "mixed": [1, "two", 3.14, True, None],
        },
        "nested": {"level1": {"level2": {"level3": "deep"}}},
    }

    result = convert_from_dict(data, "json")
    assert isinstance(result, str)
    parsed = json.loads(result)
    assert parsed == data


def test_serialize_invalid_data():
    class CustomObject:
        pass

    data = {"invalid": CustomObject()}

    with pytest.raises(Exception):
        convert_from_dict(data, "json")


def test_deserialize_from_json():
    json_str = '{"name": "test", "values": [1, 2, 3]}'
    result = convert_to_dict(json_str, "json")

    assert isinstance(result, dict)
    assert result["name"] == "test"
    assert result["values"] == [1, 2, 3]


def test_deserialize_invalid_json():
    invalid_json = '{"name": "test", invalid}'

    with pytest.raises(Exception):
        convert_to_dict(invalid_json, "json")


def test_deserialize_from_file(variables):
    file_path = os.path.join(variables["file_path"], "test.json")
    with open(file_path, "r") as f:
        json_content = f.read()

    result = convert_to_dict(json_content, "json")
    assert isinstance(result, dict)
    assert "name" in result
    assert "description" in result
    assert "values" in result
    assert isinstance(result["values"], list)
    assert isinstance(result["nested"], dict)
