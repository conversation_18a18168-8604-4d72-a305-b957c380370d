import json
import os
from datetime import datetime
from unittest.mock import patch

import pytest
import yaml
from src.lambda_wonderland import lambda_handler
from src.wonderland_flow import flow
from utils.extract import extract
from utils.load import load


# Create test_put_object_from_json using aws_mock_test
@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_put_object_from_json_mock(aws_mock_test, variables):
    # Prepare test data
    test_json = {"name": "test", "values": [1, 2, 3], "nested": {"key": "value"}}

    # Upload test JSON to S3
    aws_mock_test.put_object(
        Bucket=variables["bucket_name"],
        Key=f"{variables['source_folder']}/complex1.json",
        Body=json.dumps(test_json),
    )

    # Verify file was created in bucket at source folder
    result = aws_mock_test.get_object(
        Bucket=variables["bucket_name"],
        Key=f"{variables['source_folder']}/complex1.json",
    )
    assert result["Body"].read().decode() == json.dumps(test_json)


@pytest.mark.parametrize("dynamo_db_mock", ["dynamodb"], indirect=True)
@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
@patch("src.lambda_wonderland.settings_config")
def test_successful_json_to_yaml_conversion_mocked_aws(
    mock_settings_config, aws_mock_test, dynamo_db_mock, variables
):
    # Configure the mock for settings_config
    mock_settings_config.TACDEV_EVENT_CONFIG = f"tacdev-event-config-{variables['ENV']}"

    # Prepare test data
    test_json = {
        "version": 1,
        "sidecar_id": "test_sidecar_id",
        "asset_version": "1.0.0",
        "asset_format": "single_file",
        "reach_package_id": "reach_package_id",
        "asset_id": "3jh56k45h6k35h6",
    }

    # Mock DynamoDB tables
    config_table_name = f"tacdev-event-config-{variables['ENV']}"
    status_table_name = f"tacdev-logs-{variables['ENV']}"
    reach_table_name = (
        f"tacdev-reach-to-wonderland-{variables['ENV']}"  # Renamed for clarity
    )

    # Create mock config table with required settings
    dynamo_db_mock.create_table(
        TableName=config_table_name,
        KeySchema=[{"AttributeName": "id", "KeyType": "HASH"}],
        AttributeDefinitions=[{"AttributeName": "id", "AttributeType": "N"}],
        BillingMode="PAY_PER_REQUEST",
    )

    # Create mock status table
    dynamo_db_mock.create_table(
        TableName=status_table_name,
        KeySchema=[{"AttributeName": "client_id", "KeyType": "HASH"}],
        AttributeDefinitions=[{"AttributeName": "client_id", "AttributeType": "S"}],
        BillingMode="PAY_PER_REQUEST",
    )

    # Create mock for the "Reach to Wonderland" status DynamoDB table
    dynamo_db_mock.create_table(
        TableName=reach_table_name,  # Use the renamed variable
        KeySchema=[{"AttributeName": "sidecar_id", "KeyType": "HASH"}],
        AttributeDefinitions=[{"AttributeName": "sidecar_id", "AttributeType": "S"}],
        BillingMode="PAY_PER_REQUEST",
    )

    # Define detailed WonderlandConfig data matching the structure potentially used by FlowManager/utils.config
    current_time = datetime.now().isoformat()
    wonderland_config_data = {
        "project_type": {"S": "wonderland"},  # Adding the required discriminator field
        "status_table_name": {"S": status_table_name},
        "destination_bucket": {"S": variables["bucket_name"]},
        "destination_folder": {"S": variables["destination_folder"]},
        "source_folder": {"S": variables["source_folder"]},
        "landing_bucket": {"S": variables["bucket_name"]},
        "dynamo_table": {"S": reach_table_name},
        "filename_prefix": {"S": "wl_"},
        "secret_key": {"S": "dummy_secret_key"},
        "sqs_config": {
            "M": {
                "message_group_id": {"S": "wonderland-group"},
                "desired_keys": {"L": [{"S": "key1"}, {"S": "key2"}]},
                "query": {"M": {"status": {"S": "active"}}},
                "sqs_topic": {"S": "wonderland-topic"},
            }
        },
        "api_config": {
            "M": {
                "wonderland_validations": {
                    "M": {
                        "details": {"S": "details"},
                        "status": {"S": "status"},
                        "update_status": {"S": "update_status"},
                    }
                },
                "api_url": {"S": "http://example.com/api"},
                "main_url": {"S": "http://example.com"},
                "tacdev_reach_table": {"S": reach_table_name},
                "token_url": {"S": "http://example.com/token"},
            }
        },
    }

    # --- Add minimal placeholder data for other expected configs ---
    placeholder_config_data = {
        "landing_bucket": {"S": "dummy-bucket"},
        "secrets_manager": {"S": "dummy-secret"},
        "status_table_name": {"S": "dummy-status-table"},
        "destination_bucket": {"S": "dummy-dest-bucket"},
        "secret_key": {"S": "dummy-secret-key"},
        "filename_prefix": {"S": "dummy-prefix"},
        "dynamo_table": {"S": "dummy-dynamo-table"},
        "sidecar_table": {"S": "dummy-sidecar-table"},
        # Add any other mandatory fields specific to these configs if needed
    }

    # Put detailed config data into mock table, matching DynamoDBSettings structure
    dynamo_db_mock.put_item(
        TableName=config_table_name,
        Item={
            "id": {"N": "1"},
            "event_project_name": {"S": "wonderland"},
            "configuration_rules": {
                "M": {
                    "config": {"M": wonderland_config_data},
                    "rules": {"M": {}},
                }
            },
            "frequency": {"S": "online"},
            "time": {"S": "now"},
            "retries": {"N": "0"},
            "created_at": {"S": current_time},
            "updated_at": {"S": current_time},
            "deleted_at": {"S": current_time},
            "status": {"S": "active"},
            "description": {"S": "Wonderland config for success test"},
        },
    )

    # Upload test JSON to S3
    aws_mock_test.put_object(
        Bucket=variables["bucket_name"],
        Key=f"{variables['source_folder']}/complex.json",
        Body=json.dumps(test_json),
    )

    # Create test event
    event = {
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": f"{variables['source_folder']}/complex.json"},
                }
            }
        ]
    }

    # Execute lambda
    response = lambda_handler(event, None)

    # Verify response
    assert response["statusCode"] == 200

    # Verify file was created in destination bucket
    result = aws_mock_test.get_object(
        Bucket=variables["bucket_name"],
        Key=f"{variables['destination_folder']}/complex.yml",
    )
    yaml_content = result["Body"].read().decode()
    parsed_yaml = yaml.safe_load(yaml_content)
    assert parsed_yaml["sidecar_id"] == "test_sidecar_id"

    # Verify status table was updated using mocked DynamoDB client
    status_response = dynamo_db_mock.get_item(
        TableName=status_table_name,
        Key={"client_id": {"S": f"{variables['source_folder']}/complex.json"}},
    )
    assert status_response["Item"]["process_status"]["S"] == "Completed"

    # Verify "Reach to Wonderland" status DynamoDB table was updated
    reach_response = dynamo_db_mock.get_item(
        TableName=reach_table_name,  # Use the renamed variable
        Key={"sidecar_id": {"S": "test_sidecar_id"}},
    )
    assert reach_response["Item"]["status"]["S"] == "wl_in_progress"
