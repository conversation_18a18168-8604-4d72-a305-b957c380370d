import os

import boto3
import pytest
from moto import mock_aws
from utils import config


@pytest.fixture
def variables():
    project_base_path = os.path.dirname(os.path.abspath(__file__))
    return {
        "bucket_name": "reachengine-assets-to-wonderland-sandbox",
        "source_folder": "json-to-yaml-sbx",
        "destination_folder": "hotfolder-stg",
        "file_path": f"{project_base_path}/files",
        "ENV": "development",
    }


@pytest.fixture
def aws_mock_test(request: str, variables):
    service = request.param
    with mock_aws():
        client = boto3.client(service, region_name=config.REGION_NAME)

        if service == "s3":
            # Set up S3 buckets for testing
            client.create_bucket(
                Bucket=variables["bucket_name"],
                CreateBucketConfiguration={"LocationConstraint": config.REGION_NAME},
            )

        yield client


# Fixture for mocking AWS Dynamo DB instance
@pytest.fixture
def dynamo_db_mock(request: str, variables):
    service = request.param
    with mock_aws():
        client = boto3.client(service, region_name=config.REGION_NAME)

        yield client


@pytest.fixture
def event(variables):
    """
    Mock AWS Lambda event for local testing
    """
    return {
        "Records": [
            {
                "s3": {
                    "bucket": {"name": variables["bucket_name"]},
                    "object": {"key": f"{variables['source_folder']}/test.json"},
                }
            }
        ]
    }
