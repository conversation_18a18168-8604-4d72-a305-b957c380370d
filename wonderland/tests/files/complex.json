{"id": "complex-test-001", "timestamp": "2024-01-08T12:00:00Z", "enabled": true, "count": 42, "price": 99.99, "tags": ["test", "complex", "json"], "nullValue": null, "arrays": {"numbers": [1, 2, 3, 4, 5], "mixed": [1, "two", 3.14, true, null], "objects": [{"id": 1, "name": "first"}, {"id": 2, "name": "second"}]}, "nested": {"level1": {"level2": {"level3": {"deep": "value", "array": [1, 2, 3], "object": {"key": "value"}}}}}, "metadata": {"version": "2.0", "environment": "test", "features": {"feature1": true, "feature2": false, "config": {"timeout": 30, "retries": 3, "endpoints": ["api1", "api2"]}}}}