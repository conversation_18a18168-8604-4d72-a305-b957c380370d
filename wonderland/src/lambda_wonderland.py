import json
from typing import Dict, Optional

from utils.config import settings_config
from utils.extract import extract
from utils.load import load
from utils.LoggerService import logger_service
from utils.RetryHandler import <PERSON><PERSON><PERSON>andler
from utils.schemas.dynamodb.wonderland import WonderlandConfig
from utils.schemas.lambdas import EventSchema
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .wonderland_flow import flow


def lambda_handler(event: Dict, context: Optional[Dict]) -> Dict:
    """
    Lambda function for converting JSON files to YAML format.
    Takes a JSON file from S3 bucket, deserializes it, and saves it as YAML.
    Includes logging, configuration management and status tracking.
    """

    logger_service.info("Starting Wonderland conversion process")

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    event_settings = settings_service.initialize("wonderland")
    config: WonderlandConfig = event_settings.get_config()  # type: ignore
    status_table_name = event_settings.get_status_table()
    event_object = EventSchema(**event)  # type: ignore

    try:
        file_name = event_object.get_file_name_from_s3()
        bucket_name = event_object.get_bucket_name()

        status_tracker = StatusTrackerSchema(
            client_id=file_name, landing_bucket=bucket_name
        )
        process_status_tracker = ProcessStatusTracker(status_table_name)
        process_status_tracker.create_initial_status(create_item=status_tracker)

        global_variables = {
            "destination_bucket": config.destination_bucket,
            "destination_folder": config.destination_folder,
            "source_folder": config.source_folder,
            "source_bucket": bucket_name,
            "file_name": file_name,
            "connector": extract,
            "load_data_connector": load,
        }
        process_status_tracker.mark_as_running(file_name)

        result = flow(event_object, global_variables)

        process_status_tracker.mark_as_completed(file_name)

        logger_service.info("Wonderland conversion completed successfully")
        return result

    except Exception as error:
        logger_service.error(f"Wonderland ERROR: {str(error)}")

        error_message = f"{error}"
        process_status_tracker.mark_as_failed(file_name, error_message)

        retry_handler = RetryHandler(event_settings.retries)
        retry_handler.execute(flow, event, global_variables)

        return {
            "statusCode": 500,
            "body": json.dumps(f"Unexpected error: {str(error)}"),
        }


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "s3": {
                        "object": {
                            "key": "json-to-yaml-sbx/reach_package.json",
                        },
                        "bucket": {"name": "reachengine-assets-to-wonderland-sandbox"},
                    }
                }
            ]
        },
        "",
    )
