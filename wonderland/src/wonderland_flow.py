from typing import Any, Dict, Optional

from utils.extract import extract
from utils.flow_manager import FlowManager
from utils.load import load
from utils.schemas.lambdas import EventSchema
from utils.task_handlers import (
    TaskConvertJsonToYaml,
    TaskExtractData,
    TaskLoadData,
    TaskUpdateWonderlandStatus,
    TaskValidateWonderlandSidecar,
)


def flow(event: EventSchema, context: Optional[Dict[str, Any]]) -> None:
    """
    Lambda function for converting JSON files to YAML format.
    Takes a JSON file from S3 bucket, deserializes it, and saves it as YAML.

    Args:
        event (Dict): AWS Lambda event containing S3 trigger information
        context (Optional[Dict]): AWS Lambda context
    """

    _flow = FlowManager(
        name="wonderland",
        tasks=[
            TaskExtractData(),
            TaskValidateWonderlandSidecar(),
            TaskUpdateWonderlandStatus(),
            TaskConvertJsonToYaml(),
            TaskLoadData(),
        ],
        event=event,
        global_variables=context,
    )
    return _flow()
