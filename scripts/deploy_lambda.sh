#!/bin/bash
PACKAGE=$1
LAMBDA=$2
BUCKET=$3
ARTIFACT=$4

echo "Deploying Lambda package $PACKAGE"
#VERSION="v${CI_MERGE_REQUEST_IID}"
VERSION="${CI_COMMIT_TAG#release/}"
PACKAGE_VERSIONED="$ARTIFACT-${VERSION}.zip"
mv $PACKAGE $PACKAGE_VERSIONED
echo "Deploying Lambda to AWS with versioned package $PACKAGE_VERSIONED"
aws s3 cp $PACKAGE_VERSIONED s3://$BUCKET/$ARTIFACT/$PACKAGE_VERSIONED
if [[ $? -ne 0 ]]; then
  echo "Error: Failed to upload $PACKAGE_VERSIONED to S3"
  exit 1
fi

TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
DESCRIPTION="Update by ${GITLAB_USER_LOGIN} at ${TIMESTAMP}"

aws lambda update-function-configuration --function-name $LAMBDA --description "$DESCRIPTION"
if [[ $? -ne 0 ]]; then
  echo "Error: Failed to update Lambda configuration."
  exit 1
fi

# Wait for Lambda function to complete the configuration update
echo "Waiting for Lambda function to be ready..."
while true; do
  STATE=$(aws lambda get-function-configuration --function-name $LAMBDA --query 'State' --output text)
  LAST_UPDATE_STATUS=$(aws lambda get-function-configuration --function-name $LAMBDA --query 'LastUpdateStatus' --output text)

  if [[ "$STATE" == "Active" ]] && [[ "$LAST_UPDATE_STATUS" == "Successful" ]]; then
    echo "Lambda function is ready."
    break
  elif [[ "$STATE" == "Failed" ]] || [[ "$LAST_UPDATE_STATUS" == "Failed" ]]; then
    echo "Error: Lambda function update failed."
    exit 1
  else
    echo "Lambda function is in state: $STATE and LastUpdateStatus: $LAST_UPDATE_STATUS. Waiting..."
    sleep 5
  fi
done

# Update the function code and publish (this automatically generates an internal version, but does not check it in)
aws lambda update-function-code --function-name $LAMBDA --s3-bucket $BUCKET --s3-key $ARTIFACT/$PACKAGE_VERSIONED --publish
if [[ $? -ne 0 ]]; then
  echo "Error: Failed to update Lambda function code."
  exit 1
fi
echo "Lambda function $LAMBDA code updated successfully!"

# Wait for Lambda function to complete the configuration update
echo "Waiting for Lambda function to be ready..."
while true; do
  STATE=$(aws lambda get-function-configuration --function-name $LAMBDA --query 'State' --output text)
  LAST_UPDATE_STATUS=$(aws lambda get-function-configuration --function-name $LAMBDA --query 'LastUpdateStatus' --output text)

  if [[ "$STATE" == "Active" ]] && [[ "$LAST_UPDATE_STATUS" == "Successful" ]]; then
    echo "Lambda function is ready."
    break
  elif [[ "$STATE" == "Failed" ]] || [[ "$LAST_UPDATE_STATUS" == "Failed" ]]; then
    echo "Error: Lambda function update failed."
    exit 1
  else
    echo "Lambda function is in state: $STATE and LastUpdateStatus: $LAST_UPDATE_STATUS. Waiting..."
    sleep 5
  fi
done

# Publish a new explicit version (with readable metadata)
VERSION_NUMBER=$(aws lambda publish-version --function-name $LAMBDA --description "$DESCRIPTION" --query 'Version' --output text)

if [[ $? -ne 0 ]]; then
  echo "Error: Failed to publish Lambda version."
  exit 1
fi

echo "Lambda version $VERSION_NUMBER published successfully!"

# Create or update aliases with the environment name (sbx, qa, prod)
echo "Creating/Updating alias ${CI_ENVIRONMENT_NAME} pointing to version ${VERSION_NUMBER}..."

aws lambda update-alias --function-name $LAMBDA --name ${CI_ENVIRONMENT_NAME} --function-version $VERSION_NUMBER --description "Alias for env ${CI_ENVIRONMENT_NAME} - MR ${CI_MERGE_REQUEST_IID}" || \
aws lambda create-alias --function-name $LAMBDA --name ${CI_ENVIRONMENT_NAME} --function-version $VERSION_NUMBER --description "Alias for env ${CI_ENVIRONMENT_NAME} - MR ${CI_MERGE_REQUEST_IID}"

if [[ $? -ne 0 ]]; then
  echo "Error: Failed to update or create alias ${CI_ENVIRONMENT_NAME}"
  exit 1
fi

echo "Alias ${CI_ENVIRONMENT_NAME} now points to version ${VERSION_NUMBER}"
