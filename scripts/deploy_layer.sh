#!/bin/bash
PACKAGE=$1
LAYER=$2
BUCKET=$3
FOLDER=$4

echo "Deploying Layer package $PACKAGE"
#VERSION="v${CI_MERGE_REQUEST_IID}"
VERSION="${CI_COMMIT_TAG#release/}"
PACKAGE_VERSIONED="layer-${VERSION}.zip"
mv $PACKAGE $PACKAGE_VERSIONED
echo "Deploying Layer to AWS with versioned package $PACKAGE_VERSIONED"
aws s3 cp $PACKAGE_VERSIONED s3://$BUCKET/$FOLDER/$PACKAGE_VERSIONED
if [[ $? -ne 0 ]]; then
  echo "Error: Failed to upload $PACKAGE_VERSIONED to S3"
  exit 1
fi
aws lambda publish-layer-version --layer-name $LAYER --content S3Bucket=$BUCKET,S3Key=$FOLDER/$PACKAGE_VERSIONED --description "Shared utilities layer for Nielsen and Express"
if [[ $? -ne 0 ]]; then
  echo "Error: Failed to update Layer function code"
  exit 1
fi
echo "Layer $LAYER updated successfully!"
