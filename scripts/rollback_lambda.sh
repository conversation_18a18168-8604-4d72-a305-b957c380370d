#!/bin/bash
set -e

LAMBDA=$1
ALIAS=$2
VERSION=$3

echo "Rolling back Lambda $LAMBDA alias $ALIAS to version $VERSION..."

aws lambda update-alias \
  --function-name "$LAMBDA" \
  --name "$ALIAS" \
  --function-version "$VERSION" \
  --description "Rollback to version $VERSION" || \
aws lambda create-alias \
  --function-name "$LAMBDA" \
  --name "$ALIAS" \
  --function-version "$VERSION" \
  --description "Rollback to version $VERSION"

echo "Rollback completed: alias $ALIAS now points to version $VERSION"
