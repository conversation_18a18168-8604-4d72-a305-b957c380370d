#!/bin/bash
LAMBDA=$1

echo "Waiting for Lambda function $LAMBDA to be ready..."
while true; do
  STATE=$(aws lambda get-function-configuration --function-name $LAMBDA --query 'State' --output text)
  LAST_UPDATE_STATUS=$(aws lambda get-function-configuration --function-name $LAMBDA --query 'LastUpdateStatus' --output text)

  if [ "$STATE" == "Active" ] && [ "$LAST_UPDATE_STATUS" == "Successful" ]; then
    echo "Lambda function $LAMBDA is ready."
    break
  elif [ "$STATE" == "Failed" ] || [ "$LAST_UPDATE_STATUS" == "Failed" ]; then
    echo "The Lambda function update has failed. Exiting."
    exit 1
  else
    echo "The Lambda function is in state: $STATE and LastUpdateStatus: $LAST_UPDATE_STATUS. Waiting..."
    sleep 10
  fi
done