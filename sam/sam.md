# Lambda Deployment Guide using AWS SAM

This guide describes how to deploy your own AWS Lambda functions using the SAM (Serverless Application Model) framework with a shared utility layer (`UtilsLayer`).



## 📁 Project Structure
```bash
    tacdev/
    ├── akamai-linode/ # Akamai Lambda source code
    ├── express-lane/ # ExpressLane Lambda source code
    ├── shared_layer/ # Shared Layer (UtilsLayer)
    │ ├── Makefile # Custom build instructions
    │ └── pyproject.toml # Python package configuration
    ├── sam/ # Packaged template destination
    ├── template.yaml # SAM template
    └── .aws-sam/ # SAM build artifacts (auto-generated)
```

## 🛠️ Prerequisites

- Python 3.12+
- [AWS SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)
- AWS CLI configured (`aws configure`)
- `uv` dependency manager installed:

```bash
  pip install --user uv
```

# 🚀 How to Deploy
1. Build the project
Run from the root directory:
```bash
  sam build
```
This will:
* Build the UtilsLayer using the Makefile
* Compile each Lambda function (express, akamai)
* Install all dependencies using uv into the shared layer

2. Package the build artifacts
Package the application and upload the artifacts to your S3 bucket:
```bash
  sam package --s3-bucket <your-s3-bucket-name> --template-file .aws-sam/build/template.yaml --output-template-file sam/template.yaml
```

> [!NOTE]
> Replace `your-s3-bucket-name` with your actual bucket (must exist)

3. Deploy the stack
Deploy the application with a chosen stack name:

```bash
    sam deploy --template-file sam/template.yaml --stack-name <your-stack-name> --capabilities CAPABILITY_IAM
```
> [!NOTE]
> Replace `your-stack-name` with your stack
Example:
```bash
    sam deploy --template-file sam/template.yaml --stack-name tacdev --capabilities CAPABILITY_IAM
```

> [!TIP]
> Optional: Clean up old Lambda Layer versions

Old versions of the Lambda Layer (UtilsLayer) are not deleted by sam delete.

To clean them up manually:

```bash
    LAYER_NAME="UtilsLayer"
    for version in $(aws lambda list-layer-versions --layer-name $LAYER_NAME --query 'LayerVersions[*].Version' --output text); do
        echo "Deleting version $version of $LAYER_NAME"
        aws lambda delete-layer-version --layer-name $LAYER_NAME --version-number $version
    done
```

# 🧯 Removing the Stack
If you want to delete the deployed resources:
```bash
    sam delete
```
Follow the prompts to remove the CloudFormation stack and S3 artifacts.

# 📝 Notes
All functions share a global configuration set under `Globals` in the `template.yaml`, such as `Runtime`, `Timeout`, `MemorySize`, and `Layers`.

Each function defines its own handler and `IAM Role`. Make sure your role is valid and has the right permissions. Layers are defined using a Makefile and built automatically.
