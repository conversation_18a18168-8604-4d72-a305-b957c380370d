"""
Lambda listener for s3 uploads
"""

from typing import Dict, Optional

import utils.config as config
from utils.DynamoUtils import DynamoDBClient
from utils.LoggerService import logger_service
from utils.settings_manager import SettingsManagerService

from src.nielsen.NielsenService import NielsenService


def lambda_handler(event: Dict, context: Optional[Dict]) -> None:
    """
    AWS Lambda function handler that processes files from an S3 event trigger.

    This function handles the following steps:
    1. Logs the event received from the S3 trigger.
    2. Retrieves secrets required for database and other operations.
    3. Initializes a DynamoDB client with the table name obtained from secrets.
    4. Extracts bucket name and file name from the event.
    5. Checks if the file is an XML. If not raises an error for unrecognized files.
    6. If the file is an XML, it proceeds to process it.
    7. Initializes an instance of NielsenService with the bucket name.
    8. Performs the upload request to Nielsen:
    a. Sends a request to get file key, file ID, and chunk information.
    b. Uploads the chunks.
    c. Completes the upload by sending the compound checksum.
    d. Deletes the private keys after the upload is finished.
    9. Handles exceptions by logging detailed error messages and ensuring private keys are deleted.

    Args:
        event (Dict): The event data passed to the Lambda function from the S3 trigger.
        context (Optional[Dict]): The context in which the Lambda function is running,
                                such as invocation metadata and client context.

    Raises:
        Exception: If any error occurs during processing, it logs and re-raises the exception.
    """
    logger_service.info("************ IZMA / Lambda Nielsen-iris started ************")
    logger_service.info(f"IZMA / Lambda Nielsen-iris with args: \n {event}")
    _ = context
    bucket_name = ""
    wav_file_name = ""
    xml_file_name = ""
    file_name_from_s3 = ""
    try:
        settings_service = SettingsManagerService("tacdev-event-config-sbx")
        settings_service.initialize("nielsen-iris")
        event_settings = settings_service.settings
        status_table_name = event_settings.configuration_rules["config"]["table_name"]
        table_name = status_table_name + "_" + config.ENV
        dynamo_client = DynamoDBClient(table_name)
        bucket_name = event["detail"]["bucket"]["name"]
        file_name_from_s3 = event["detail"]["object"]["key"]

        if ".xml" not in file_name_from_s3:
            logger_service.error(
                "Waiting for xml before upload wav file, saving file info in dynamo"
            )
            if ".wav" not in file_name_from_s3:
                logger_service.error("Unrecognized file finishing process")
                raise Exception(".wav not found in file")
            dynamo_client.create_item(
                {
                    "file_name": file_name_from_s3,
                    "process_status": "waiting for xml file",
                    "chunk_iteration": 0,
                    "more_details": "",
                }
            )
        xml_file_name = file_name_from_s3
        logger_service.error("Got xml file with name: %s", file_name_from_s3)
        wav_file_name = xml_file_name.replace("xml", "wav")
        nielsen = NielsenService(bucket_name, event_settings)
        file_key, file_id, chunks, file_name = nielsen.upload_request(
            xml_file_name, wav_file_name
        )
        etag_list, csum_list = nielsen.upload_chunks(
            chunks, file_name, file_id, file_key
        )
        nielsen.finish_upload(etag_list, csum_list, file_key, file_id, file_name)
        nielsen.delete_private_keys()
    except Exception as e:
        logger_service.error(
            "Error while processing file from s3: %s and wav file name: %s and xml file name:%s in the bucket: %s",
            file_name_from_s3,
            wav_file_name,
            xml_file_name,
            bucket_name,
        )
        logger_service.error(
            "Exception occurred trying to upload file: %s more details in Dynamo DB",
            e,
        )
        if bucket_name:
            # Be sure to erase all keys
            nielsen = NielsenService(
                bucket_name, settings=event_settings, delete_key=True
            )
            nielsen.delete_private_keys()

        logger_service.info(
            "************ IZMA / Lambda Nielsen-iris finished ************"
        )
