"""
Class file to define all the services for nielsen
"""

import base64
import hashlib
import json
import os
import time
import uuid
from typing import List, Tuple

import jwt
import xmltodict
from jwcrypto.jwk import JWK
from utils.config import ENV
from utils.DynamoUtils import DynamoD<PERSON>lient
from utils.LoggerService import logger_service
from utils.RequestsHandler import <PERSON>questHandler
from utils.S3Utils import S3Utils
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings import Settings


class NielsenService:
    """
    Nielsen class
    """

    def __init__(
        self, bucket_name: str, settings: Settings, delete_key: bool = False
    ) -> None:
        """
        Init of all nielsen services
        :param bucket_name: name of the bucket to start all the process
        :param settings: project settings
        :param delete_key: varible to dont create the pem key when raises an exception
        """
        secrets_manager = SecretsManagerUtils()
        secret_name = ENV + "/nielsen/secrets"
        connection_details = secrets_manager.get_secret_value(secret_name)
        self.okta_url = settings.configuration_rules["config"][
            "nielsen_okta_url_server"
        ]
        self.nielsen_s2s_url = settings.configuration_rules["config"][
            "nielsen_s2s_api_url"
        ]
        self.chunk_size = int(settings.configuration_rules["config"]["chunk_size"])
        self.client_id = connection_details.get("nielsen_client_id")
        self.private_key_text = (
            "-----BEGIN RSA PRIVATE KEY-----"
            + connection_details.get("nielsen_private_key")
            .replace("-----BEGIN RSA PRIVATE KEY-----", "")
            .replace("-----END RSA PRIVATE KEY-----", "")
            .replace(" ", "\n")
            + "-----END RSA PRIVATE KEY-----"
        )
        self.nielsen_jwk = json.loads(connection_details.get("nielsen_public_jwk"))
        # To divide the files in bytes
        self.s3_client = S3Utils(bucket_name)
        if not delete_key:
            self.create_pem_file()
        self.nielsen_api = RequestHandler(self.nielsen_s2s_url)
        self.file_size = 0
        self.table_name = connection_details.get("table_name") + "_" + ENV
        self.dynamo_client = DynamoDBClient(self.table_name)

    def create_pem_file(self) -> None:
        """
        Method to create private key file it saves as temporary file
        :return: None
        """
        logger_service.info("create_pem_file started")
        private_key_jwk = JWK.from_pem(self.private_key_text.encode("utf-8"))
        private_key_jwk_json = private_key_jwk.export(private_key=True)
        private_key_jwk_json = private_key_jwk_json.replace(
            '''"kid":"ewz8RCWnu4lQSxu_ygBYc-WznJNB_oglHseNo6ovCcc"''',
            f'''"kid":"{self.nielsen_jwk.get('kid')}"''',
        )
        with open("/tmp/private_key_jwk.json", "w") as jwk_file:
            jwk_file.write(private_key_jwk_json)

        logger_service.info("create_pem_file finished")

    @staticmethod
    def create_pem_key() -> Tuple[JWK, bytes]:
        """
        Creates a JWK private key and exports it to PEM format.

        Reads a JWK (JSON Web Key) from a file named 'private_key_jwk.json' and exports
        the private key to PEM format.

        Returns:
            Tuple[JWK, bytes]: A tuple containing the JWK object and the private key in PEM format.

        Raises:
            FileNotFoundError: If the 'private_key_jwk.json' file does not exist.
            IOError: If there is an error reading the 'private_key_jwk.json' file.
            jwk.JWKError: If there is an error processing the JWK.
        """
        logger_service.info("create_pem_key started")
        with open("/tmp/private_key_jwk.json", "r") as jwk_file:
            key = JWK.from_json(jwk_file.read())
            pk_pem_key = key.export_to_pem(private_key=True, password=None)

        logger_service.info("create_pem_key finished")

        return key, pk_pem_key

    def create_jwt(self) -> str:
        """
        Creates a JSON Web Token (JWT) signed with a private key.

        Generates a JWT with claims for 'sub', 'iss', 'iat', 'exp', 'aud', and 'jti'.
        The token is signed using the RS256 algorithm and includes the key ID ('kid')
        from a previously generated JWK.

        Returns:
            str: The signed JWT as a string.

        Raises:
            Exception: If there is an error during the token creation process,
            it will raise an exception.
        """
        logger_service.info("create_jwt started")
        _, pk_pem_key = self.create_pem_key()
        iat = int(time.time())
        okta_web_token = jwt.encode(
            {
                "sub": self.client_id,
                "iss": self.client_id,
                "iat": iat,
                "exp": iat + 300,
                "aud": self.okta_url,
                "jti": uuid.uuid4().hex,
            },
            pk_pem_key,
            algorithm="RS256",
            headers={"kid": self.nielsen_jwk.get("kid")},
        )
        logger_service.info("create_jwt finished")
        return okta_web_token

    def create_dpop_proof_from_nielsen(self) -> str:
        """
        Creates a DPoP (Demonstration of Proof-of-Possession) JWT and retrieves a
        nonce from the server's response.

        This method performs the following steps:
        1. Logs the creation of a DPoP JWT.
        2. Generates a JWK private key and exports it to PEM format.
        3. Assembles claims and headers for the DPoP proof JWT.
        4. Signs the DPoP JWT with the private key.
        5. Sends a POST request to the server with the DPoP proof and other data.
        6. Checks the response for a 'dpop-nonce' header and raises an error if missing.
        7. Returns the 'dpop-nonce' value from the response headers.

        Returns:
            str: The 'dpop-nonce' value from the server's response headers.

        Raises:
            ValueError: If the 'dpop-nonce' header is missing in the response.
            Exception: Any other exceptions raised during the JWT creation or HTTP request process.
        """
        logger_service.info("Creating DPoP from Nielsen started")
        _, pk_pem_key = self.create_pem_key()
        claims = {
            "htm": "POST",  # HTTP Method
            "htu": self.okta_url,
            "iat": int(time.time()),
        }
        headers = {
            "alg": "RS256",
            "typ": "dpop+jwt",
            "jwk": {
                "kty": self.nielsen_jwk.get("kty"),
                "e": self.nielsen_jwk.get("e"),
                "n": self.nielsen_jwk.get("n"),
                "kid": self.nielsen_jwk.get("kid"),
            },
        }
        dpop_proof = jwt.encode(claims, pk_pem_key, algorithm="RS256", headers=headers)
        requests_handler = RequestHandler(self.okta_url)
        response = requests_handler.post(
            "",
            data={
                "grant_type": "client_credentials",
                "scope": "mediaUpload",
                "client_assertion_type": "urn:ietf:params:oauth:client-assertion-type:jwt-bearer",
                "client_assertion": self.create_jwt(),
            },
            headers={"DPoP": dpop_proof},
        )
        response_headers = response.headers
        if "dpop-nonce" not in response_headers:
            logger_service.error("Missing dpop-nonce Header in response headers")
            raise ValueError("Missing dpop-nonce Header in response headers")
        logger_service.info("Creating DPoP from Nielsen finished")
        return response_headers["dpop-nonce"]

    def dpop_locally(self, dpop_nonce: str) -> str:
        """
        Creates a DPoP (Demonstration of Proof-of-Possession) JWT for a subsequent request.

        This method performs the following steps:
        1. Logs the creation of a DPoP JWT for the second request.
        2. Generates a JWK private key and exports it to PEM format.
        3. Assembles claims and headers for the DPoP proof JWT,
        including the nonce from the previous request.
        4. Signs the DPoP JWT with the private key.
        5. Returns the signed DPoP JWT as a string.

        Args:
            dpop_nonce (str): The nonce value obtained from the server's response.

        Returns:
            str: The signed DPoP JWT as a string.

        Raises:
            Exception: Any exceptions raised during the JWT creation process.
        """
        logger_service.info("Creating JWT with DPoP for second request")
        _, pk_pem_key = self.create_pem_key()
        claims = {
            "htm": "POST",
            "htu": self.okta_url,
            "iat": int(time.time()),
            "nonce": dpop_nonce,
            "jti": uuid.uuid4().hex,
        }
        headers = {
            "alg": "RS256",
            "typ": "dpop+jwt",
            "jwk": {
                "kty": self.nielsen_jwk.get("kty"),
                "e": self.nielsen_jwk.get("e"),
                "n": self.nielsen_jwk.get("n"),
                "kid": self.nielsen_jwk.get("kid"),
            },
        }
        dpop_proof = jwt.encode(claims, pk_pem_key, algorithm="RS256", headers=headers)
        logger_service.info("Creating JWT with DPoP for second request finished")
        return dpop_proof

    def create_okta_token(self) -> str:
        """
        Creates an Okta token by first creating DPoP proofs (both locally and from Nielsen)
        and then sending a request to Okta.

        This method performs the following steps:
        1. Logs the start of the token creation process.
        2. Creates a DPoP proof from Nielsen.
        3. Creates a local DPoP proof using the nonce from Nielsen.
        4. Sends a POST request to the Okta URL with the necessary data and headers.
        5. Parses the response to extract the access token.

        Returns:
            str: The access token retrieved from the Okta server's response.

        Raises:
            ValueError: If the access token is missing in the response body.
            Exception: If there is an error during the response parsing or token creation process.
        """
        logger_service.info("Starting process to create token in Nielsen")
        dpop_from_nielsen = self.create_dpop_proof_from_nielsen()
        dpop_local = self.dpop_locally(dpop_from_nielsen)
        requests_handler = RequestHandler(self.okta_url)
        response = requests_handler.post(
            "",
            data={
                "grant_type": "client_credentials",
                "scope": "mediaUpload",
                "client_assertion_type": "urn:ietf:params:oauth:client-assertion-type:jwt-bearer",
                "client_assertion": self.create_jwt(),
            },
            headers={"DPoP": dpop_local},
        )
        try:
            response_body = response.json()
            if "access_token" not in response_body:
                raise ValueError("Missing access token in the response")
            return response_body["access_token"]
        except Exception as e:
            logger_service.error("Error trying to parse response from okta url %s", e)
            raise

    def calculate_num_of_chunks(self, file_size: int) -> int:
        """
        Calculates the number of chunks needed to process a file.

        This method calculates the number of chunks required to process a file of a given size
        based on a predefined chunk size. If the division of the file size by the chunk size does
        not result in an integer, the number of chunks is rounded up to ensure the entire file
        is processed.

        Args:
            file_size (int): The size of the file to be processed, in bytes.

        Returns:
            int: The total number of chunks required to process the file.
        """
        logger_service.info("calculate_num_of_chunks started")
        number_of_chunks = file_size / self.chunk_size
        if not number_of_chunks.is_integer():
            return int(number_of_chunks + 1)
        logger_service.info("calculate_num_of_chunks finished")
        return int(number_of_chunks)

    def parse_info_from_xml_in_bucket(self, xml_name: str) -> list:
        """
        Parses mandatory information from an XML file stored in an S3 bucket.

        This method attempts to retrieve an XML file from an S3 bucket, parse it,
        and extract specific mandatory keys. If any mandatory key is missing, it
        raises a KeyError. Additionally, it attempts to extract optional keys such
        as 'seasonNum', 'episodeNum', and 'episodeName', setting them to None if
        they are not present in the XML.

        Args:
            xml_name (str): The name of the XML file to be parsed.

        Returns:
            list: A list containing the values of the mandatory and optional keys
                  extracted from the XML file.

        Raises:
            KeyError: If any mandatory key is missing in the XML file.
            Exception: If there is an error in retrieving or parsing the XML file.
        """
        logger_service.info("Trying to parse the file %s", xml_name)
        mandatory_keys = [
            "assetName",
            "distributor",
            "language",
            "airDate",
            "fileName",
            "type",
        ]
        try:
            xml_data = self.s3_client.get_object(xml_name)
            xml_data_parsed = xml_data["data"].read().decode("utf-8")
            xml_dict = xmltodict.parse(xml_data_parsed)["package"]
            data_to_return = []

            for key in mandatory_keys:
                if key not in xml_dict:
                    logger_service.error(
                        "Key: %s is not present in present in the xml", key
                    )
                    raise KeyError(f"Key: {key} is not present in present in the xml")
                data_to_return.append(xml_dict[key])

            if "seasonNum" not in xml_dict:
                data_to_return.append(None)
            else:
                data_to_return.append(int(xml_dict["seasonNum"]))

            if "episodeNum" not in xml_dict:
                data_to_return.append(None)
            else:
                data_to_return.append(int(xml_dict["episodeNum"]))

            if "episodeName" not in xml_dict:
                data_to_return.append(None)
            else:
                data_to_return.append(xml_dict["episodeName"])

            logger_service.info("parse_info_from_xml_in_bucket finished")
            return data_to_return
        except Exception as e:
            logger_service.error("Error trying to parse the file %s", e)
            raise

    def upload_request(
        self, xml_name: str, file_name: str
    ) -> Tuple[str, str, list, str]:
        """
        Processes an upload request by calculating file details, parsing XML metadata, and
        making a POST request to the Nielsen API.

        This method performs the following steps:
        1. Retrieves the size of the file from an S3 bucket.
        2. Calculates the number of chunks needed to process the file based on its size.
        3. Parses the XML metadata from an S3 bucket to extract mandatory and optional information.
        4. Constructs the parameters required for the upload request based on the extracted
        metadata and chunk information.
        5. Retrieves an Okta token for authorization.
        6. Makes a POST request to the Nielsen API with the constructed parameters
        and authorization header.
        7. Parses the JSON response from the Nielsen API to extract the file key,
        file ID, and chunk information.

        Args:
            file_name (str): The name of the file to be processed.
            xml_name (str): The name of the XML file containing metadata information.

        Returns:
            tuple: A tuple containing the file key, file ID, number of chunks, and the file name.

        Raises:
            Exception: If there is an error during the upload request or in parsing the response.
        """
        logger_service.info("upload_request started")
        self.file_size = self.s3_client.get_file_size(file_name)
        num_of_chunks = self.calculate_num_of_chunks(self.file_size)
        (
            asset_name,
            distributor,
            language,
            air_date,
            file_name_to_upload,
            type_of_file,
            season_num,
            episode_num,
            episode_name,
        ) = self.parse_info_from_xml_in_bucket(xml_name)
        parameters = {
            "type": type_of_file,
            "numChunks": num_of_chunks,
            "assetName": asset_name,
            "distributor": distributor,
            "language": language,
            "airDate": air_date,
            "fileName": file_name_to_upload,
        }
        if season_num:
            parameters["seasonNum"] = season_num
        if episode_num:
            parameters["episodeNum"] = episode_num
        if episode_name:
            parameters["episodeName"] = episode_name
        headers = {"Authorization": f"Bearer {self.create_okta_token()}"}
        key = {"file_name": file_name}
        updates = {
            "process_status": "making upload request",
            "chunk_iteration": 0,
            "more_details": "",
        }
        self.dynamo_client.update_item(key, updates)
        response = self.nielsen_api.post(
            "/uploadrequest", params=parameters, headers=headers
        )
        try:
            response_json = response.json()
            if "message" in response_json:
                logger_service.error(
                    "Error trying to call the upload request endpoint %s",
                    response_json["message"],
                )
                raise ValueError(response_json["message"])
            if "errorMessage" in response_json:
                logger_service.error(
                    "Error trying to call the upload request endpoint %s",
                    response_json["errorMessage"],
                )
                raise ValueError(response_json["errorMessage"])
            file_key = response_json["fileKey"]
            file_id = response_json["fileId"]
            chunks = response_json["chunks"]
            logger_service.info("upload_request finished")
            return file_key, file_id, chunks, file_name
        except Exception as e:
            logger_service.error(
                "Found error trying to call upload request endpoint %s", e
            )
            key = {"file_name": file_name}
            updates = {
                "process_status": "error in upload request",
                "chunk_iteration": 0,
                "more_details": str(e),
            }
            self.dynamo_client.update_item(key, updates)
            raise e

    def upload_chunks(
        self, chunks: list, file_name: str, file_id: str, file_key: str
    ) -> Tuple[list, list]:
        """
        Uploads file chunks to a specified URL using PUT requests and manages the upload process.

        This method performs the following steps:
        1. Logs the start of the process to PUT chunks.
        2. Initializes the offset, etag_list, and csum_list variables.
        3. Iterates through the list of chunks:
           a. Retrieves the signed URL for the chunk.
           b. Calculates the byte range to be downloaded from S3.
           c. Downloads the chunk data from S3 using the calculated byte range.
           d. Computes the SHA256 checksum of the chunk data.
           e. Constructs headers for the PUT request including the checksum.
           f. Sends a PUT request to upload the chunk data to the signed URL.
           g. Validates the response status code from the PUT request.
           h. Extracts the ETag from the response headers and appends it to etag_list.
        4. If an error occurs during the upload process, it aborts the upload and logs the error.

        Args:
            file_name (str): The name of the file to be uploaded.
            chunks (list): A list of chunks containing signed URLs for uploading.
            file_id (str): The ID of the file being uploaded.
            file_key (str): The key of the file in the storage system.

        Returns:
            tuple: A tuple containing the list of ETags and the list of checksums for
            the uploaded chunks.

        Raises:
            Exception: If there is any error during the chunk upload process.
        """
        logger_service.info("Starting process to PUT chunks")
        iteration = 0
        etag_list = []
        try:
            offset = 0
            csum_list = []
            for i, chunk in enumerate(chunks):
                iteration = i
                key = {"file_name": file_name}
                updates = {
                    "process_status": "Uploading chunks",
                    "chunk_iteration": i + 1,
                    "more_details": "",
                }
                self.dynamo_client.update_item(key, updates)
                upload_url = chunk["signedUrl"]
                request_handler = RequestHandler(upload_url)
                end = min(offset + self.chunk_size - 1, self.file_size - 1)
                logger_service.info("Downloading bytes from s3 %s-%s", offset, end)
                range_header = f"bytes={offset}-{end}"
                response = self.s3_client.get_object(file_name, range_header)
                chunk_data = response["data"].read()
                if not chunk_data:
                    logger_service.error(
                        "No more data to read finishing upload in iteration %s of %s",
                        i + 1,
                        len(chunks),
                    )
                    raise ValueError("no data to read")
                csum_calculated = hashlib.sha256(chunk_data).digest()
                csum_list.append(csum_calculated)
                csum_b64 = base64.b64encode(csum_calculated).decode()
                headers = {
                    "x-amz-sdk-checksum-algorithm": "SHA256",
                    "x-amz-checksum-sha256": csum_b64,
                }
                response_put = request_handler.put("", data=chunk_data, headers=headers)
                response_status_code = response_put.status_code
                response_text = response_put.text
                if response_status_code != 200:
                    logger_service.error(
                        "Status Code from AWS PUT request is not 200 finishing upload in iteration %s of %s",
                        i + 1,
                        len(chunks),
                    )
                    logger_service.error(
                        "Status Code %s and response text %s",
                        response_status_code,
                        response_text,
                    )
                    raise ValueError("Status code is not 200")
                etag = response_put.headers["ETag"]
                etag_list.append({"etag": etag, "sha256": csum_b64})
                offset += self.chunk_size
            logger_service.info("upload_chunks finished")
            return etag_list, csum_list
        except Exception as e:
            self.abort_upload(file_id, file_key, etag_list)
            logger_service.error("Found error trying to put chunks %s", e)
            key = {"file_name": file_name}
            updates = {
                "process_status": "Exception  uploading chunks",
                "chunk_iteration": iteration,
                "more_details": str(e),
            }
            self.dynamo_client.update_item(key, updates)
            raise e

    def finish_upload(
        self,
        etag_list: List,
        csum_list: List[bytes],
        file_key: str,
        file_id: str,
        file_name: str,
    ) -> bool:
        """
        Completes the upload process to Nielsen by sending the compound checksum and list of ETags.

        This method performs the following steps:
        1. Logs the start of the process to finish the upload.
        2. Computes the compound SHA256 checksum from the list of chunk checksums.
        3. Encodes the compound checksum in base64 format.
        4. Constructs parameters and data for the completion request.
        5. Sends a POST request to the Nielsen API to indicate the completion of the upload.
        6. Validates the response status code from the Nielsen API.
        7. Parses the response JSON to extract relevant information.

        Args:
            etag_list (list): A string representing the list of ETags for the uploaded chunks.
            csum_list (list[bytes]): A list of SHA256 checksums for each uploaded chunk.
            file_key (str): The key of the file in the storage system.
            file_id (str): The ID of the file being uploaded.
            file_name (str): The name of the file being uploaded.

        Returns:
            bool: True if the upload completion was successful.

        Raises:
            Exception: If there is any error during the completion request to the Nielsen API.
        """
        logger_service.info("finish_upload started")
        key = {"file_name": file_name}
        updates = {
            "process_status": "Finishing upload",
            "chunk_iteration": 0,
            "more_details": "",
        }
        self.dynamo_client.update_item(key, updates)
        logger_service.error("Finishig the upload to nielsen")
        csum_calculated = hashlib.sha256(b"".join(csum_list)).digest()
        csum_b64 = base64.b64encode(csum_calculated).decode()
        params = {"fileKey": file_key, "fileId": file_id, "compoundSHA256": csum_b64}
        data = json.dumps({"etagList": etag_list})
        headers = {"Authorization": f"Bearer {self.create_okta_token()}"}
        response = self.nielsen_api.post(
            "/uploadcomplete", params=params, data=data, headers=headers
        )
        status_code = response.status_code
        if status_code != 200:
            logger_service.error(
                "Error from nielsen API status code got: %s", status_code
            )
            logger_service.error("More details: %s", response.text)
            raise ValueError("Status code is not 200")
        response_json = response.json()
        cid = response_json["cid"]
        compound_sha = response_json["compoundSHA256"]
        key = {"file_name": file_name}
        updates = {
            "process_status": "Upload Finished",
            "chunk_iteration": 0,
            "more_details": f"cid: {cid}, compound_sha: {compound_sha}",
        }
        self.dynamo_client.update_item(key, updates)
        logger_service.info("finish_upload finished")
        return True

    def abort_upload(self, file_id: str, file_key: str, etag_list: list = None) -> bool:
        """
        Aborts the upload process to Nielsen by sending an abort request to the API.

        This method performs the following steps:
        1. Logs the start of the process to abort the upload.
        2. Constructs parameters for the abort request, including the file key and file ID.
        3. Adds authorization headers using a JWT.
        4. Sends a POST request to the Nielsen API to abort the upload.
        5. Validates the response status code from the Nielsen API.
        6. Logs the success or failure of the abort request.

        Args:
            file_id (str): The ID of the file being uploaded.
            file_key (str): The key of the file in the storage system.
            etag_list (dict, optional): A dictionary of ETags for the uploaded chunks.

        Returns:
            bool: True if the abort request was successful.
        """
        logger_service.info("Starting to abort the upload")
        params = {"fileKey": file_key, "fileId": file_id}
        headers = {"Authorization": f"Bearer {self.create_jwt()}"}
        data = None
        if etag_list:
            data = json.dumps({"etagList": etag_list})
        response = self.nielsen_api.post(
            "/uploadabort", params=params, data=data, headers=headers
        )
        status_code = response.status_code
        response_text = response.text
        if response.status_code != 200:
            logger_service.error(
                "Something wrong happened status code: %s", status_code
            )
            logger_service.error("Response: %s", response_text)
        logger_service.info("Success abort upload finished")
        return True

    @staticmethod
    def delete_private_keys():
        """
        Deletes the private key file from the local filesystem.

        This static method attempts to remove the 'private_key_jwk.json'
        file from the local filesystem.

        If an error occurs during the file deletion, it logs the error.

        Raises:
            Exception: If there is any error during the file deletion process.
        """
        logger_service.info("delete_private_keys started")
        try:
            os.remove("/tmp/private_key_jwk.json")
            logger_service.info("delete_private_keys finished")
        except FileNotFoundError as e:
            logger_service.error("Pem key does not exists: %s", e)
        except Exception as e:
            logger_service.error(
                "Something wrong happened trying to erase the key: %s, e"
            )
