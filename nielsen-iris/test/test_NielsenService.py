import base64
import hashlib
import unittest
from unittest.mock import MagicMock, mock_open, patch

import pytest
from utils.DynamoUtils import <PERSON><PERSON><PERSON><PERSON>
from utils.RequestsHandler import <PERSON>questHandler
from utils.settings import Settings

from src.nielsen.NielsenService import NielsenService


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.logger_service")
def test_calculate_chunks(mock_logger, aws_all_mock_test, settings):
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    chunks = nielsen.calculate_num_of_chunks(512)

    assert chunks == 4


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.logger_service")
def test_parse_xml(mock_logger, aws_all_mock_test, settings):
    s3_client = aws_all_mock_test["s3"]
    mocked_xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<package>
    <assetName>Sample Asset</assetName>
    <distributor>Sample Distributor</distributor>
    <language>en</language>
    <airDate>2024-09-23</airDate>
    <fileName>sample_file.mp4</fileName>
    <type>video</type>
    <seasonNum>1</seasonNum>
    <episodeNum>2</episodeNum>
    <episodeName>Pilot</episodeName>
</package>"""

    # Upload the mocked XML file to the bucket
    s3_client.put_object(Bucket="test-bucket", Key="test.xml", Body=mocked_xml_content)
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    parsed_xml = nielsen.parse_info_from_xml_in_bucket("test.xml")

    assert "Sample Asset" in parsed_xml, "'Sample Asset' No key found"
    assert (
        "Sample Distributor" in parsed_xml
    ), "'Sample Distributor' Not found from assetName key"
    assert "en" in parsed_xml, "'en' Not found from 'language' key"
    assert "2024-09-23" in parsed_xml, "'2024-09-23' Not found from 'airDate key"
    assert (
        "sample_file.mp4" in parsed_xml
    ), "'sample_file.mp4' Not found from 'fileName' key"
    assert "video" in parsed_xml, "'video' Not found from 'type' key"
    assert 1 in parsed_xml, "'1' Not found from 'seasonNum' key"
    assert 2 in parsed_xml, "'2' Not found from 'episodeNum' key"
    assert "Pilot" in parsed_xml, "'Pilot' Not found from 'episodeName' key"


@pytest.mark.parametrize("aws_all_mock_test", [None], indirect=True)
@patch("src.nielsen.NielsenService.logger_service")
def test_get_file_size(mock_lgger, aws_all_mock_test, mocker, settings):
    s3_client = aws_all_mock_test["s3"]

    # Mock token
    mock_token = "mocked_okta_token"
    mocker.patch.object(NielsenService, "create_okta_token", return_value=mock_token)
    mocker.patch.object(DynamoDBClient, "update_item")

    post_mock_response = MagicMock()
    post_mock_response.status_code = 201
    post_mock_response.json.return_value = {
        "fileKey": "sample_file.mp4",
        "fileId": "2",
        "chunks": 4,
    }

    mock_request_handler = mocker.patch.object(
        RequestHandler, "post", return_value=post_mock_response
    )

    mocked_xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<package>
    <assetName>Sample Asset</assetName>
    <distributor>Sample Distributor</distributor>
    <language>en</language>
    <airDate>2024-09-23</airDate>
    <fileName>sample_file.mp4</fileName>
    <type>video</type>
    <seasonNum>1</seasonNum>
    <episodeNum>2</episodeNum>
    <episodeName>Pilot</episodeName>
</package>"""

    # Upload the mocked XML file to the bucket
    s3_client.put_object(Bucket="test-bucket", Key="test.xml", Body=mocked_xml_content)
    # Mock binary content for an mp4 file
    mocked_mp4_content = b"fake_binary_content_for_mp4"

    # Upload the mocked mp4 file to the bucket
    s3_client.put_object(
        Bucket="test-bucket", Key="sample_file.mp4", Body=mocked_mp4_content
    )
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    response = nielsen.upload_request(file_name="sample_file.mp4", xml_name="test.xml")

    assert ("sample_file.mp4", "2", 4, "sample_file.mp4") == response


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.logger_service")
def test_parse_xml_malformed(mock_logger, aws_all_mock_test, settings):
    s3_client = aws_all_mock_test["s3"]
    malformed_xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<package>
    <assetName>Sample Asset</assetName>
    <distributor>Sample Distributor</distributor>
    <language>en</language>
    <airDate>2024-09-23</airDate>
    <fileName>sample_file.mp4</fileName>
    <!-- Missing closing tags -->
"""

    # Upload the malformed XML file to the bucket
    s3_client.put_object(
        Bucket="test-bucket", Key="malformed_test.xml", Body=malformed_xml_content
    )
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )

    with pytest.raises(Exception):
        nielsen.parse_info_from_xml_in_bucket("malformed_test.xml")


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("builtins.open", new_callable=mock_open)
@patch("src.nielsen.NielsenService.JWK")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_creation_of_pem_file(
    mock_logger, mock_jwk, mock_file, aws_all_mock_test, settings
):
    mock_jwk.from_pem = MagicMock()
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.private_key_text = (
        "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    )
    nielsen.nielsen_jwk = {"kid": "test-kid"}
    nielsen.create_pem_file()
    mock_file.assert_called_once_with("/tmp/private_key_jwk.json", "w")
    mock_file().write.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.logger_service")
def test_file_creation_with_invalid_private_key(
    mock_logger, aws_all_mock_test, settings
):
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.private_key_text = (
        "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    )
    nielsen.nielsen_jwk = {"kid": "test-kid"}
    with unittest.TestCase().assertRaises(expected_exception=ValueError):
        nielsen.create_pem_file()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("builtins.open", new_callable=mock_open)
@patch("src.nielsen.NielsenService.JWK.from_pem")
@patch("src.nielsen.NielsenService.logger_service")
def test_file_creation_with_missing_kid(
    mock_logger, mock_from_pem, mock_file, aws_all_mock_test, settings
):
    mock_jwk = mock_from_pem.return_value
    mock_jwk.export.return_value = "test_key"
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.private_key_text = (
        "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    )
    nielsen.nielsen_jwk = {}
    nielsen.create_pem_file()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("builtins.open", new_callable=mock_open)
@patch("src.nielsen.NielsenService.JWK")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_creation_of_pem_key(
    mock_logger, mock_jwk, mock_file, aws_all_mock_test, settings
):
    mock_jwk.from_json.return_value = MagicMock()
    mock_jwk = mock_jwk.from_json.return_value
    mock_jwk.export_to_pem.return_value = (
        b"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    )
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    _, pem_key = nielsen.create_pem_key()
    assert pem_key == b"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("builtins.open", side_effect=FileNotFoundError)
@patch("src.nielsen.NielsenService.logger_service")
def test_file_not_found_error_create_pem_key(
    mock_logger, _, aws_all_mock_test, settings
):
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with unittest.TestCase().assertRaises(FileNotFoundError):
        nielsen.create_pem_key()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.create_pem_key")
@patch("src.nielsen.NielsenService.jwt.encode")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_create_jwt(
    mock_logger, mock_jwt_encode, mock_create_pem, aws_all_mock_test, settings
):
    mock_pk_pem_key = b"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    mock_create_pem.return_value = (None, mock_pk_pem_key)
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.nielsen_jwk = {"kid": "test-kid"}
    mock_jwt_encode.return_value = "mocked.jwt.token"
    token = nielsen.create_jwt()
    assert token == "mocked.jwt.token"
    mock_jwt_encode.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.create_pem_key")
@patch(
    "src.nielsen.NielsenService.jwt.encode", side_effect=Exception("JWT creation error")
)
@patch("src.nielsen.NielsenService.logger_service")
def test_jwt_creation_exception(
    mock_logger, _, mock_create_pem, aws_all_mock_test, settings
):
    mock_pk_pem_key = b"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    mock_create_pem.return_value = (None, mock_pk_pem_key)
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.nielsen_jwk = {"kid": "test-kid"}
    with unittest.TestCase().assertRaises(Exception):
        nielsen.create_jwt()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.create_pem_key")
@patch("src.nielsen.NielsenService.jwt.encode")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_dpop_proof_creation(
    mock_logger,
    mock_request_handler,
    mock_jwt_encode,
    mock_create_pem,
    aws_all_mock_test,
    settings,
):
    mock_pk_pem_key = b"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    mock_nielsen_jwk = {"kty": "RSA", "e": "AQAB", "n": "mocked_n", "kid": "mocked_kid"}
    mock_response = MagicMock()
    mock_response.headers = {"dpop-nonce": "mocked_nonce"}
    mock_create_pem.return_value = (None, mock_pk_pem_key)
    mock_jwt_encode.return_value = "mocked_dpop_proof"
    mock_request_handler.return_value.post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.nielsen_jwk = mock_nielsen_jwk
    nonce = nielsen.create_dpop_proof_from_nielsen()
    assert nonce == "mocked_nonce"
    assert mock_jwt_encode.call_count == 2


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.create_pem_key")
@patch("src.nielsen.NielsenService.jwt.encode")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_missing_dpop_nonce_header(
    mock_logger,
    mock_request_handler,
    mock_jwt_encode,
    mock_create_pem,
    aws_all_mock_test,
    settings,
):
    mock_pk_pem_key = b"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
    mock_nielsen_jwk = {"kty": "RSA", "e": "AQAB", "n": "mocked_n", "kid": "mocked_kid"}
    mock_response = MagicMock()
    mock_response.headers = {}
    mock_create_pem.return_value = (None, mock_pk_pem_key)
    mock_jwt_encode.return_value = "mocked_dpop_proof"
    mock_request_handler.return_value.post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.nielsen_jwk = mock_nielsen_jwk
    with unittest.TestCase().assertRaises(ValueError) as context:
        nielsen.create_dpop_proof_from_nielsen()
    assert str(context.exception) == "Missing dpop-nonce Header in response headers"


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.create_dpop_proof_from_nielsen")
@patch("src.nielsen.NielsenService.NielsenService.dpop_locally")
@patch("src.nielsen.NielsenService.NielsenService.create_jwt")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_okta_token_creation(
    mock_logger,
    mock_request_handler,
    mock_create_jwt,
    mock_create_dpop_local,
    mock_create_dpop_nielsen,
    aws_all_mock_test,
    settings,
):
    mock_dpop_proof = "mocked_dpop_proof"
    mock_local_dpop_proof = "mocked_local_dpop_proof"
    mock_jwt = "mocked_jwt"
    mock_response = MagicMock()
    mock_response.json.return_value = {"access_token": "mocked_access_token"}
    mock_create_dpop_nielsen.return_value = mock_dpop_proof
    mock_create_dpop_local.return_value = mock_local_dpop_proof
    mock_request_handler.return_value.post.return_value = mock_response
    mock_create_jwt.return_value = mock_jwt
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    token = nielsen.create_okta_token()
    assert token == "mocked_access_token"


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.create_dpop_proof_from_nielsen")
@patch("src.nielsen.NielsenService.NielsenService.dpop_locally")
@patch("src.nielsen.NielsenService.NielsenService.create_jwt")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_missing_access_token_in_response(
    mock_logger,
    mock_request_handler,
    mock_create_jwt,
    mock_create_dpop_local,
    mock_create_dpop_nielsen,
    aws_all_mock_test,
    settings,
):
    mock_dpop_proof = "mocked_dpop_proof"
    mock_local_dpop_proof = "mocked_local_dpop_proof"
    mock_jwt = "mocked_jwt"
    mock_response = MagicMock()
    mock_response.json.return_value = {}
    mock_create_dpop_nielsen.return_value = mock_dpop_proof
    mock_create_dpop_local.return_value = mock_local_dpop_proof
    mock_request_handler.return_value.post.return_value = mock_response
    mock_create_jwt.return_value = mock_jwt
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with unittest.TestCase().assertRaises(ValueError) as context:
        nielsen.create_okta_token()
    assert str(context.exception) == "Missing access token in the response"


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.S3Utils.get_file_size")
@patch("src.nielsen.NielsenService.NielsenService.calculate_num_of_chunks")
@patch("src.nielsen.NielsenService.NielsenService.parse_info_from_xml_in_bucket")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_upload_request(
    mock_logger,
    mock_nielsen_api_post,
    mock_update_dynamo_item,
    mock_create_okta_token,
    mock_parse_info_from_xml,
    mock_calculate_num_of_chunks,
    mock_get_file_size,
    aws_all_mock_test,
    settings,
):
    xml_name = "mocked_metadata.xml"
    file_name = "mocked_file.mp4"
    mock_file_size = 1024
    mock_num_chunks = 1
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "fileKey": "mocked_file_key",
        "fileId": "mocked_file_id",
        "chunks": ["chunk1"],
    }
    mock_get_file_size.return_vale = mock_file_size
    mock_calculate_num_of_chunks.return_value = mock_num_chunks
    mock_parse_info_from_xml.return_value = (
        "mocked_asset_name",
        "mocked_distributor",
        "mocked_language",
        "mocked_air_date",
        "mocked_file_name_to_upload",
        "mocked_file_type",
        None,
        None,
        None,
    )
    mock_create_okta_token.return_value = "mocked_token"
    mock_nielsen_api_post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    file_key, file_id, chunks, returned_file_name = nielsen.upload_request(
        xml_name, file_name
    )
    assert file_key == "mocked_file_key"
    assert file_id == "mocked_file_id"
    assert chunks == ["chunk1"]
    assert returned_file_name == file_name
    mock_update_dynamo_item.assert_called_once()
    mock_nielsen_api_post.assert_called_once()
    mock_create_okta_token.assert_called_once()
    mock_parse_info_from_xml.assert_called_once()
    mock_calculate_num_of_chunks.assert_called_once()
    mock_get_file_size.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.S3Utils.get_file_size")
@patch("src.nielsen.NielsenService.NielsenService.calculate_num_of_chunks")
@patch("src.nielsen.NielsenService.NielsenService.parse_info_from_xml_in_bucket")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.logger_service")
def test_upload_nielsen_missing_key_in_response(
    mock_logger,
    mock_nielsen_api_post,
    mock_update_dynamo_item,
    mock_create_okta_token,
    mock_parse_info_from_xml,
    mock_calculate_num_of_chunks,
    mock_get_file_size,
    aws_all_mock_test,
    settings,
):
    xml_name = "mocked_metadata.xml"
    file_name = "mocked_file.mp4"
    mock_file_size = 1024
    mock_num_chunks = 1
    mock_response = MagicMock()
    mock_response.json.return_value = {"fileId": "mocked_file_id", "chunks": ["chunk1"]}
    mock_get_file_size.return_vale = mock_file_size
    mock_calculate_num_of_chunks.return_value = mock_num_chunks
    mock_parse_info_from_xml.return_value = (
        "mocked_asset_name",
        "mocked_distributor",
        "mocked_language",
        "mocked_air_date",
        "mocked_file_name_to_upload",
        "mocked_file_type",
        None,
        None,
        None,
    )
    mock_create_okta_token.return_value = "mocked_token"
    mock_nielsen_api_post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with unittest.TestCase().assertRaises(KeyError) as context:
        nielsen.upload_request(xml_name, file_name)
    assert mock_update_dynamo_item.call_count == 2
    assert str(context.exception) == "'fileKey'"
    mock_nielsen_api_post.assert_called_once()
    mock_create_okta_token.assert_called_once()
    mock_parse_info_from_xml.assert_called_once()
    mock_calculate_num_of_chunks.assert_called_once()
    mock_get_file_size.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.S3Utils.get_file_size")
@patch("src.nielsen.NielsenService.NielsenService.calculate_num_of_chunks")
@patch("src.nielsen.NielsenService.NielsenService.parse_info_from_xml_in_bucket")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.logger_service")
def test_error_during_upload_request(
    mock_logger,
    mock_nielsen_api_post,
    mock_update_dynamo_item,
    mock_create_okta_token,
    mock_parse_info_from_xml,
    mock_calculate_num_of_chunks,
    mock_get_file_size,
    aws_all_mock_test,
    settings,
):
    xml_name = "mocked_metadata.xml"
    file_name = "mocked_file.mp4"
    mock_file_size = 1024
    mock_num_chunks = 1
    mock_response = MagicMock()
    mock_response.json.side_effect = Exception(
        "JSON parsing error"
    )  # Simulate a JSON parsing error
    mock_get_file_size.return_vale = mock_file_size
    mock_calculate_num_of_chunks.return_value = mock_num_chunks
    mock_parse_info_from_xml.return_value = (
        "mocked_asset_name",
        "mocked_distributor",
        "mocked_language",
        "mocked_air_date",
        "mocked_file_name_to_upload",
        "mocked_file_type",
        None,
        None,
        None,
    )
    mock_create_okta_token.return_value = "mocked_token"
    mock_nielsen_api_post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with unittest.TestCase().assertRaises(Exception) as context:
        nielsen.upload_request(xml_name, file_name)
    assert "JSON parsing error" in str(context.exception)
    assert mock_update_dynamo_item.call_count == 2
    mock_nielsen_api_post.assert_called_once()
    mock_create_okta_token.assert_called_once()
    mock_parse_info_from_xml.assert_called_once()
    mock_calculate_num_of_chunks.assert_called_once()
    mock_get_file_size.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.S3Utils.get_file_size")
@patch("src.nielsen.NielsenService.NielsenService.calculate_num_of_chunks")
@patch("src.nielsen.NielsenService.NielsenService.parse_info_from_xml_in_bucket")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.logger_service")
def test_message_in_upload_request_response(
    mock_logger,
    mock_nielsen_api_post,
    mock_update_dynamo_item,
    mock_create_okta_token,
    mock_parse_info_from_xml,
    mock_calculate_num_of_chunks,
    mock_get_file_size,
    aws_all_mock_test,
    settings,
):
    xml_name = "mocked_metadata.xml"
    file_name = "mocked_file.mp4"
    mock_file_size = 1024
    mock_num_chunks = 1
    mock_response = MagicMock()
    mock_response.json.return_value = {"message": "error message"}
    mock_get_file_size.return_vale = mock_file_size
    mock_calculate_num_of_chunks.return_value = mock_num_chunks
    mock_parse_info_from_xml.return_value = (
        "mocked_asset_name",
        "mocked_distributor",
        "mocked_language",
        "mocked_air_date",
        "mocked_file_name_to_upload",
        "mocked_file_type",
        None,
        None,
        None,
    )
    mock_create_okta_token.return_value = "mocked_token"
    mock_nielsen_api_post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with unittest.TestCase().assertRaises(ValueError) as context:
        nielsen.upload_request(xml_name, file_name)
    assert "error message" in str(context.exception)
    assert mock_update_dynamo_item.call_count == 2
    mock_nielsen_api_post.assert_called_once()
    mock_create_okta_token.assert_called_once()
    mock_parse_info_from_xml.assert_called_once()
    mock_calculate_num_of_chunks.assert_called_once()
    mock_get_file_size.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.S3Utils.get_file_size")
@patch("src.nielsen.NielsenService.NielsenService.calculate_num_of_chunks")
@patch("src.nielsen.NielsenService.NielsenService.parse_info_from_xml_in_bucket")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.logger_service")
def test_errormessage_in_upload_request_response(
    mock_logger,
    mock_nielsen_api_post,
    mock_update_dynamo_item,
    mock_create_okta_token,
    mock_parse_info_from_xml,
    mock_calculate_num_of_chunks,
    mock_get_file_size,
    aws_all_mock_test,
    settings,
):
    xml_name = "mocked_metadata.xml"
    file_name = "mocked_file.mp4"
    mock_file_size = 1024
    mock_num_chunks = 1
    mock_response = MagicMock()
    mock_response.json.return_value = {"errorMessage": "error message"}
    mock_get_file_size.return_vale = mock_file_size
    mock_calculate_num_of_chunks.return_value = mock_num_chunks
    mock_parse_info_from_xml.return_value = (
        "mocked_asset_name",
        "mocked_distributor",
        "mocked_language",
        "mocked_air_date",
        "mocked_file_name_to_upload",
        "mocked_file_type",
        None,
        None,
        None,
    )
    mock_create_okta_token.return_value = "mocked_token"
    mock_nielsen_api_post.return_value = mock_response
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with unittest.TestCase().assertRaises(ValueError) as context:
        nielsen.upload_request(xml_name, file_name)
    assert "error message" in str(context.exception)
    assert mock_update_dynamo_item.call_count == 2
    mock_nielsen_api_post.assert_called_once()
    mock_create_okta_token.assert_called_once()
    mock_parse_info_from_xml.assert_called_once()
    mock_calculate_num_of_chunks.assert_called_once()
    mock_get_file_size.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.S3Utils.get_object")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_successful_chunk_uploads(
    mock_logger,
    mock_request_handler,
    mock_update_item,
    mock_s3_get_object,
    aws_all_mock_test,
    settings,
):
    file_name = "mocked_file.mp4"
    file_id = "mocked_file_id"
    file_key = "mocked_file_key"
    chunks = [
        {"signedUrl": "http://mocked-signed-url-1"},
        {"signedUrl": "http://mocked-signed-url-2"},
    ]
    mock_chunk_data_1 = b"chunk1_data"
    mock_chunk_data_2 = b"chunk2_data"
    mock_etag = "mocked_etag"

    mock_response_get = {
        "data": MagicMock(
            read=MagicMock(side_effect=[mock_chunk_data_1, mock_chunk_data_2])
        )
    }
    mock_response_put = MagicMock()
    mock_response_put.status_code = 200
    mock_response_put.headers = {"ETag": mock_etag}
    mock_request_handler.return_value.put.return_value = mock_response_put
    mock_s3_get_object.return_value = mock_response_get
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    etag_list, csum_list = nielsen.upload_chunks(chunks, file_name, file_id, file_key)
    assert etag_list == [
        {
            "etag": mock_etag,
            "sha256": base64.b64encode(
                hashlib.sha256(mock_chunk_data_1).digest()
            ).decode(),
        },
        {
            "etag": mock_etag,
            "sha256": base64.b64encode(
                hashlib.sha256(mock_chunk_data_2).digest()
            ).decode(),
        },
    ]
    assert len(csum_list) == 2
    mock_update_item.assert_called()
    mock_s3_get_object.assert_called()
    mock_request_handler.assert_called()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.abort_upload")
@patch("src.nielsen.NielsenService.S3Utils.get_object")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_chunk_uploads_no_data(
    mock_logger,
    mock_request_handler,
    mock_update_item,
    mock_s3_get_object,
    mock_abort_upload,
    aws_all_mock_test,
    settings,
):
    file_name = "mocked_file.mp4"
    file_id = "mocked_file_id"
    file_key = "mocked_file_key"
    chunks = [
        {"signedUrl": "http://mocked-signed-url-1"},
        {"signedUrl": "http://mocked-signed-url-2"},
    ]
    mock_chunk_data_1 = b""
    mock_chunk_data_2 = b""
    mock_etag = "mocked_etag"

    mock_response_get = {
        "data": MagicMock(
            read=MagicMock(side_effect=[mock_chunk_data_1, mock_chunk_data_2])
        )
    }
    mock_response_put = MagicMock()
    mock_response_put.status_code = 200
    mock_response_put.headers = {"ETag": mock_etag}
    mock_request_handler.return_value.put.return_value = mock_response_put
    mock_s3_get_object.return_value = mock_response_get
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with pytest.raises(ValueError) as context:
        nielsen.upload_chunks(chunks, file_name, file_id, file_key)
    assert "no data to read" in str(context)
    mock_update_item.assert_called()
    mock_abort_upload.assert_called()
    mock_s3_get_object.assert_called()
    mock_request_handler.assert_called()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.NielsenService.abort_upload")
@patch("src.nielsen.NielsenService.S3Utils.get_object")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.RequestHandler")
@patch("src.nielsen.NielsenService.logger_service")
def test_chunk_uploads_no_200_response(
    mock_logger,
    mock_request_handler,
    mock_update_item,
    mock_s3_get_object,
    mock_abort_upload,
    aws_all_mock_test,
    settings,
):
    file_name = "mocked_file.mp4"
    file_id = "mocked_file_id"
    file_key = "mocked_file_key"
    chunks = [
        {"signedUrl": "http://mocked-signed-url-1"},
        {"signedUrl": "http://mocked-signed-url-2"},
    ]
    mock_chunk_data_1 = b"data 1"
    mock_chunk_data_2 = b"data 2"
    mock_etag = "mocked_etag"

    mock_response_get = {
        "data": MagicMock(
            read=MagicMock(side_effect=[mock_chunk_data_1, mock_chunk_data_2])
        )
    }
    mock_response_put = MagicMock()
    mock_response_put.status_code = 400
    mock_response_put.headers = {"ETag": mock_etag}
    mock_request_handler.return_value.put.return_value = mock_response_put
    mock_s3_get_object.return_value = mock_response_get
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with pytest.raises(ValueError) as context:
        nielsen.upload_chunks(chunks, file_name, file_id, file_key)
    assert "Status code is not 200" in str(context)
    mock_update_item.assert_called()
    mock_abort_upload.assert_called()
    mock_s3_get_object.assert_called()
    mock_request_handler.assert_called()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.logger_service")
def test_finish_upload_success(
    mock_logger,
    mock_update_item,
    mock_create_okta_token,
    mock_post_request,
    aws_all_mock_test,
    settings,
):
    file_name = "test_file.wav"
    csum_list = [b"csum1", b"csum2"]
    file_key = "test_file_key"
    file_id = "test_file_id"
    etag_list = ["etag1", "etag2"]
    mock_response_post = MagicMock()
    mock_response_post.status_code = 200
    mock_response_post.json.return_vale = {
        "cid": "test_cid",
        "compoundSHA256": "test_compudShad",
    }
    mock_create_okta_token.return_value = "mock_token"
    mock_post_request.return_value = mock_response_post
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    response = nielsen.finish_upload(etag_list, csum_list, file_key, file_id, file_name)
    assert response is True
    assert mock_update_item.call_count == 2
    mock_create_okta_token.assert_called_once()
    mock_post_request.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.logger_service")
def test_finish_upload_status_code_not_200(
    mock_logger,
    mock_update_item,
    mock_create_okta_token,
    mock_post_request,
    aws_all_mock_test,
    settings,
):
    file_name = "test_file.wav"
    csum_list = [b"csum1", b"csum2"]
    file_key = "test_file_key"
    file_id = "test_file_id"
    etag_list = ["etag1", "etag2"]
    mock_response_post = MagicMock()
    mock_response_post.status_code = 400
    mock_response_post.text = "error"
    mock_create_okta_token.return_value = "mock_token"
    mock_post_request.return_value = mock_response_post
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with pytest.raises(ValueError) as context:
        nielsen.finish_upload(etag_list, csum_list, file_key, file_id, file_name)
    assert "Status code is not 200" in str(context)
    mock_update_item.assert_called_once()
    mock_create_okta_token.assert_called_once()
    mock_post_request.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.NielsenService.create_okta_token")
@patch("src.nielsen.NielsenService.DynamoDBClient.update_item")
@patch("src.nielsen.NielsenService.logger_service")
def test_finish_upload_status_code_not_expect_key_in_response(
    mock_logger,
    mock_update_item,
    mock_create_okta_token,
    mock_post_request,
    aws_all_mock_test,
    settings,
):
    file_name = "test_file.wav"
    csum_list = [b"csum1", b"csum2"]
    file_key = "test_file_key"
    file_id = "test_file_id"
    etag_list = ["etag1", "etag2"]
    mock_post_request = mock_post_request.return_value
    mock_post_request.status_code = 200
    mock_post_request.json.return_value = {}
    mock_create_okta_token.return_value = "mock_token"
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    with pytest.raises(KeyError) as context:
        nielsen.finish_upload(etag_list, csum_list, file_key, file_id, file_name)
    assert "cid" in str(context)
    mock_update_item.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.NielsenService.create_jwt")
@patch("src.nielsen.NielsenService.logger_service")
def test_abort_upload_success_with_etag_list(
    mock_logger, mock_create_jwt_token, mock_post_request, aws_all_mock_test, settings
):
    file_key = "test_file_key"
    file_id = "test_file_id"
    etag_list = ["etag1", "etag2"]
    mock_post_request = mock_post_request.return_value
    mock_post_request.status_code = 200
    mock_post_request.json.return_value = {}
    mock_create_jwt_token.return_value = "mock_jwt"
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    response = nielsen.abort_upload(file_id, file_key, etag_list)
    assert response is True
    mock_create_jwt_token.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.NielsenService.create_jwt")
@patch("src.nielsen.NielsenService.logger_service")
def test_abort_upload_success_without_etag_list(
    mock_logger, mock_create_jwt_token, mock_post_request, aws_all_mock_test, settings
):
    file_key = "test_file_key"
    file_id = "test_file_id"
    mock_post_request = mock_post_request.return_value
    mock_post_request.status_code = 200
    mock_post_request.json.return_value = {}
    mock_create_jwt_token.return_value = "mock_jwt"
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    response = nielsen.abort_upload(file_id, file_key)
    assert response is True
    mock_create_jwt_token.assert_called_once()


@pytest.mark.parametrize("aws_all_mock_test", [1], indirect=True)
@patch("src.nielsen.NielsenService.RequestHandler.post")
@patch("src.nielsen.NielsenService.NielsenService.create_jwt")
@patch("src.nielsen.NielsenService.logger_service")
def test_abort_upload_no_response_200(
    mock_logger, mock_create_jwt_token, mock_post_request, aws_all_mock_test, settings
):
    file_key = "test_file_key"
    file_id = "test_file_id"
    mock_post_request = mock_post_request.return_value
    mock_post_request.status_code = 400
    mock_post_request.json.return_value = {}
    mock_create_jwt_token.return_value = "mock_jwt"
    nielsen = NielsenService(
        bucket_name="test-bucket", settings=settings, delete_key=True
    )
    nielsen.abort_upload(file_id, file_key)
    mock_create_jwt_token.assert_called_once()
