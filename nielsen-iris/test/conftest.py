import json

import boto3
import pytest
from moto import mock_aws
from utils import config
from utils.DynamoUtils import Dynamo<PERSON><PERSON><PERSON>
from utils.S3Utils import S3Utils
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings import Settings


@pytest.fixture
def aws_mock_test(request: str):
    service = request.param

    with mock_aws():  # Using the generic mock_aws if it's available
        # Create the client for the service being tested
        client = boto3.client(service, region_name=config.REGION_NAME)

        if service == "s3":
            # Set up an S3 bucket if testing S3
            client.create_bucket(
                Bucket="test-bucket",
                CreateBucketConfiguration={"LocationConstraint": config.REGION_NAME},
            )
        elif service == "dynamodb":
            # Set up a DynamoDB table if testing DynamoDB
            client.create_table(
                TableName="test-table",
                KeySchema=[{"AttributeName": "id", "KeyType": "HASH"}],  # Partition key
                AttributeDefinitions=[{"AttributeName": "id", "AttributeType": "S"}],
                ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
            )
        elif service == "secretsmanager":
            client.create_secret(
                Name="test-secret",
                SecretString='{"username":"admin","password":"password123"}',
            )

        yield client


@pytest.fixture
def aws_all_mock_test(request: str):
    # Use mock_aws to cover multiple services
    with mock_aws():
        # Create S3 client
        s3_client = boto3.client("s3", region_name=config.REGION_NAME)
        s3_client.create_bucket(
            Bucket="test-bucket",
            CreateBucketConfiguration={"LocationConstraint": config.REGION_NAME},
        )
        # Create DynamoDB client
        dynamodb_client = boto3.client("dynamodb", region_name=config.REGION_NAME)
        dynamodb_client.create_table(
            TableName="test-table",
            KeySchema=[
                {"AttributeName": "file_name", "KeyType": "HASH"}
            ],  # Partition key
            AttributeDefinitions=[{"AttributeName": "file_name", "AttributeType": "S"}],
            ProvisionedThroughput={"ReadCapacityUnits": 1, "WriteCapacityUnits": 1},
        )
        # Create Secret Manager client
        secrets_manager = boto3.client("secretsmanager", region_name=config.REGION_NAME)
        secrets_manager.create_secret(
            Name="development/nielsen/secrets",
            SecretString=json.dumps(
                {
                    "nielsen_okta_url_server": "test_okta",
                    "nielsen_client_id": "test_clientid",
                    "nielsen_private_key": "-----BEGIN RSA PRIVATE KEY-----\ntest\n-----END RSA PRIVATE KEY-----",
                    "nielsen_public_jwk": json.dumps({}),
                    "nielsen_s2s_api_url": "test_s2s",
                    "chunk_size": "128",
                    "table_name": "test-table",
                }
            ),
        )
        # Return both clients for use in the test
        yield {
            "s3": s3_client,
            "dynamodb": dynamodb_client,
            "secrets_manager": secrets_manager,
        }


@pytest.fixture
def s3_utils() -> S3Utils:
    return S3Utils(bucket_name="test-bucket")


@pytest.fixture
def dynamodb_client() -> DynamoDBClient:
    return DynamoDBClient(table_name="test-table")


@pytest.fixture
def secret_manager() -> SecretsManagerUtils:
    return SecretsManagerUtils()


@pytest.fixture
def settings() -> Settings:
    settings = Settings()
    settings.configuration_rules = {
        "config": {
            "chunk_size": "128",
            "nielsen_okta_url_server": "https://nielsennmc.okta.com/oauth2/ausl4ocyypNe7PZl8357/v1/token",
            "nielsen_s2s_api_url": "https://iris-rest-uat.nonprod.scr.nielsen.com",
            "table_name": "s3_nielsen_files_status",
        },
        "rules": [],
    }
    return settings
