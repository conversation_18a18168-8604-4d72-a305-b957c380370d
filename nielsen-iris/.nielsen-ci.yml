image: python:3.12.6

workflow:
  rules:
    - changes:
      - nielsen-iris/**/*
      when: always
    - when: never

variables:
  PACKAGE: "nielsen.zip"
  ARTIFACT: "nielsen"
  LAMBDA: "nielsen-${ENV}"
  BUCKET: "tacdev-artifacts-${ENV}"
  LAYER: "shared-layer-${ENV}"

stages:
  - test
  - build
  - deploy-sbx
  - deploy-qa
  - deploy-prod

# Test job for Nielsen
test-code-nielsen:
  stage: test
  script:
    - echo "Testing code"
    - pip install -e shared_layer/
    - cd nielsen-iris/
    - pip install -r src/requirements.txt
    - pip install black==24.10.0
    - pip install pylint==3.3.4
    - pip install isort==6.0.0
    - pip install pytest
    - black --check --diff -v .
    - pylint $(git ls-files '*.py') --errors-only
    - isort --profile black . --check --diff
    - pytest
  allow_failure: false
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - nielsen-iris/**/*

# Build job for creating the artifact
create-artifact-nielsen:
  stage: build
  before_script:
    - apt-get update && apt-get install -y zip
    - pip install --cache-dir=.cache/pip -r nielsen-iris/src/requirements.txt -t build/  # Install dependencies in the current directory
  script:
    - echo "Creating Lambda package for ${CI_COMMIT_REF_NAME}"
    - cd build/
    - zip -r ../$PACKAGE . -x "boto3/*" -x "botocore/*" # Exclude boto3 and botocore
    - cd ../nielsen-iris/
    - zip -r ../$PACKAGE src/*
  artifacts:
    paths:
      - $PACKAGE
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push" || $PARENT_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - nielsen-iris/**/*

# Deploy job for the sandbox environment
deploy-nielsen-sbx:
  stage: deploy-sbx
  environment:
    name: sbx
  before_script:
    - bash scripts/setup_aws.sh
  script:
    - echo "Deploying Lambda function for $CI_ENVIRONMENT_NAME"
    - bash scripts/deploy_lambda.sh $PACKAGE $LAMBDA $BUCKET $ARTIFACT
    - bash scripts/wait_for_lambda.sh $LAMBDA

    # Add layer to lambda
    - LATEST_VERSION=$(aws lambda list-layer-versions --layer-name $LAYER --query 'LayerVersions[0].LayerVersionArn' --output text | head -n 1)
    - aws lambda update-function-configuration --function-name $LAMBDA --description "$DESCRIPTION" --layers $LATEST_VERSION
    - bash scripts/wait_for_lambda.sh $LAMBDA
    - aws lambda update-function-configuration --function-name $LAMBDA --environment "Variables={ENV=${CI_ENVIRONMENT_NAME}}"
    - echo "Layer $LATEST_VERSION was successfully applied to Lambda function $LAMBDA"
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "development"
      changes:
        - nielsen-iris/**/*
      when: manual

# Deploy job for the qa environment
deploy-nielsen-qa:
  stage: deploy-qa
  environment:
    name: qa
  before_script:
    - bash scripts/setup_aws.sh
  script:
    - echo "Deploying Lambda function for $CI_ENVIRONMENT_NAME"
    - bash scripts/deploy_lambda.sh $PACKAGE $LAMBDA $BUCKET $ARTIFACT
    - bash scripts/wait_for_lambda.sh $LAMBDA

    # Add layer to lambda
    - LATEST_VERSION=$(aws lambda list-layer-versions --layer-name $LAYER --query 'LayerVersions[0].LayerVersionArn' --output text | head -n 1)
    - aws lambda update-function-configuration --function-name $LAMBDA --description "$DESCRIPTION" --layers $LATEST_VERSION
    - bash scripts/wait_for_lambda.sh $LAMBDA
    - aws lambda update-function-configuration --function-name $LAMBDA --environment "Variables={ENV=${CI_ENVIRONMENT_NAME}}"
    - echo "Layer $LATEST_VERSION was successfully applied to Lambda function $LAMBDA"
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "qa"
      changes:
        - nielsen-iris/**/*
      when: manual

# Deploy job for the production environment
deploy-nielsen-prod:
  stage: deploy-prod
  environment:
    name: prod
  before_script:
    - bash scripts/setup_aws.sh
  script:
    - echo "Deploying Lambda function for $CI_ENVIRONMENT_NAME"
    - bash scripts/deploy_lambda.sh $PACKAGE $LAMBDA $BUCKET $ARTIFACT
    - bash scripts/wait_for_lambda.sh $LAMBDA

    # Add layer to lambda
    - LATEST_VERSION=$(aws lambda list-layer-versions --layer-name $LAYER --query 'LayerVersions[0].LayerVersionArn' --output text | head -n 1)
    - aws lambda update-function-configuration --function-name $LAMBDA --layers $LATEST_VERSION
    - bash scripts/wait_for_lambda.sh $LAMBDA
    - aws lambda update-function-configuration --function-name $LAMBDA --environment "Variables={ENV=${CI_ENVIRONMENT_NAME}}"
    - echo "Layer $LATEST_VERSION was successfully applied to Lambda function $LAMBDA"
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"
      changes:
        - express-lane/**/*
      when: manual
