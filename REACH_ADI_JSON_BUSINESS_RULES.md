# Reach Package ADI JSON Fields - Business Rules Documentation

## Overview
This document provides comprehensive business rules for ADI JSON building in the Reach Series Toolbox, extracted from the RASCL codebase analysis. These rules ensure consistent package creation and distribution across all supported networks and partners.

## ADI JSON Fields for Reach Package Creation

### Core Program Information

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `program_type` | String | Must be "series", "movie", "special", or "acquired" | - | - | **Required** | Determines package category structure and partner distribution | Throws exception if invalid | All networks | Used for content type parameter | `/reachengine/api/abc/contents?type={program_type}` |
| `content_name` | String | Non-empty string | - | - | **Required** | Used for folder naming, must be sanitized for file systems | Throws exception if empty | All networks | `name` (content creation) | `/reachengine/api/abc/contents` |
| `network` | String | Must match valid network codes | - | - | **Required** | Determines affiliate mappings, genre defaults, and partner categories | Throws exception if invalid | FX, FXX, FXM, NG, NGW, ABC | `network` (content creation) | `/reachengine/api/abc/contents` |
| `material_id` | String | Unique identifier validation | - | - | **Required** | Must be globally unique, used for package identification | Throws exception if duplicate | All networks | `metadata.properties.trafficCode.value` | `/reachengine/api/abc/packages/{id}` |
| `episode_title` | String | Non-empty for episodes | - | - | **Required** for episodes | Episode name, used in package naming and identification | Throws exception if empty for episodes | All networks | `name` (package creation), `title` (package update) | `/reachengine/api/abc/packages` |

### Identifiers & Codes

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `episode_tms_id` | String | TMS ID format validation | `""` | - | Optional | Links to Tribune Media Services, used for metadata enrichment | Warning if invalid format | All networks | `metadata.properties.episodeTmsId.value` | `/reachengine/api/abc/packages/{id}` |
| `tms_id` | String | TMS ID format validation | `""` | - | Optional | Series-level TMS identifier, inherits from episode for movies/specials | Warning if missing for series | All networks | `metadata.properties.tMSSeriesID.value` | `/reachengine/api/abc/contents/{id}` |
| `radar_group_id` | String | Radar system validation | `""` | - | Optional | Used for cast/crew lookup via Bolt API | Silent failure if invalid | All networks | `metadata.properties.radarGroupId.value` | `/reachengine/api/abc/packages/{id}` |
| `episode_production_number` | String | Production code format | `""` | - | Optional | Maps to trafficCode in package metadata | No validation | All networks | `metadata.properties.trafficCode.value` | `/reachengine/api/abc/packages/{id}` |
| `content_prefix` | String | Network-specific prefix | `""` | - | Optional | Show code for internal systems, network-dependent | Defaults to empty | Network-specific | `metadata.properties.contentPrefix.value` | `/reachengine/api/abc/packages/{id}` |

### Content Classification

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `season_num` | String/Integer | Numeric validation | `""` | - | Optional | Required for series, used in season creation | Warning if missing for series | All networks | `value` (season creation) | `/reachengine/api/abc/contents/{id}/seasons` |
| `episode_number` | String/Integer | Numeric validation | `""` | - | Optional | Used for episode ordering and identification | Warning if missing for episodes | All networks | `metadata.properties.episodeNumber.value` | `/reachengine/api/abc/packages/{id}` |
| `episode_category` | String | Category validation | `""` | - | Optional | Determines package type (Episode, Movie, Special) and partner distribution | Defaults based on program_type | All networks | `categoryType` (package creation), `category` (package update) | `/reachengine/api/abc/packages` |
| `episode_c_type` | String | Must be valid C-Type | `"C3"` | - | **Required** | Controls TVE distribution, C-Types don't go to TVE | Always defaults to C3 | FX/NG networks | `metadata.properties.episodeCType.value` | `/reachengine/api/abc/packages/{id}` |
| `episode_d_type` | String | Must be valid D-Type | `"D4"` | - | **Required** | Controls VOD distribution timing | Always defaults to D4 | FX/NG networks | `metadata.properties.episodeDType.value` | `/reachengine/api/abc/packages/{id}` |
| `country` | String | ISO country code | `"USA"` | - | **Required** | Territory restriction, affects distribution rights | Always USA for current implementation | All networks | `metadata.properties.country.value` | `/reachengine/api/abc/packages/{id}` |
| `content_type` | String | "Long Form" or "Short Form" | `"Long Form"` | - | **Required** | Determines media profiles and validation requirements | Defaults to Long Form | All networks | `metadata.properties.contentType.value` | `/reachengine/api/abc/packages/{id}` |

### Metadata & Descriptions

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `copyright` | String | Copyright holder validation | Network value | - | **Required** | Legal requirement, defaults to network name | Uses network if empty | All networks | `metadata.properties.copyright.value` | `/reachengine/api/abc/packages/{id}` |
| `content_short_synopsis` | String | Text validation | `""` | - | Optional | Used in content creation API calls | No validation | All networks | `shortDescription` (content creation) | `/reachengine/api/abc/contents` |
| `content_long_synopsis` | String | Text validation | `""` | - | Optional | Used in content creation API calls | No validation | All networks | `longDescription` (content creation) | `/reachengine/api/abc/contents` |
| `episode_short_synopsis` | String | Text validation | `""` | - | Optional | Episode-specific description | No validation | All networks | `metadata.properties.episodeShortSynopsis.value` | `/reachengine/api/abc/packages/{id}` |
| `episode_long_synopsis` | String | Text validation | `""` | - | Optional | Episode-specific long description | No validation | All networks | `metadata.properties.episodeLongSynopsis.value` | `/reachengine/api/abc/packages/{id}` |
| `season_short_synopsis` | String | Text validation | `""` | - | Optional | Season-level description | No validation | All networks | `shortDescription` (season creation) | `/reachengine/api/abc/contents/{id}/seasons` |
| `season_long_synopsis` | String | Text validation | `""` | - | Optional | Season-level long description | No validation | All networks | `longDescription` (season creation) | `/reachengine/api/abc/contents/{id}/seasons` |
| `keywords` | String | Comma-separated validation | `""` | - | Optional | Auto-generated from content, used for search | No validation | All networks | `keywords` (package update) | `/reachengine/api/abc/packages/{id}` |

### Rating & Content Labels

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `default_rating` | String | Valid rating codes | `""` | - | Optional | Content rating (TV-14, TV-MA, etc.) | Warning if invalid | All networks | `tVRating` (package update) | `/reachengine/api/abc/packages/{id}` |
| `rating_content_labels` | Array/String | Rating label validation | `""` | - | Optional | Specific content warnings (Violence, Language, etc.) | No validation | All networks | `tVRatingDescriptors` (package update) | `/reachengine/api/abc/packages/{id}` |

### Technical & Folder Information

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `cms_folder_name` | String | Alphanumeric + hyphens only | Cleaned content name | - | **Required** | Used for GoPub folder structure, sanitized automatically | Auto-sanitizes invalid chars | All networks | `metadata.properties.dATGGoPubFolder.value` | `/reachengine/api/abc/contents/{id}` |
| `show_folder` | String | Alphanumeric + hyphens only | Same as cms_folder_name | - | **Required** | MVPD folder reference, must match cms_folder_name | Inherits from cms_folder_name | All networks | Partner-specific MVPD properties | `/reachengine/api/abc/contents/{id}` |
| `title_brief` | String | Length validation | Truncated cms_folder_name | 9 chars (series), 15 chars (movies) | **Required** | MVPD system requirement, auto-truncated | Auto-truncates to limit | All networks | Partner-specific MVPD properties | `/reachengine/api/abc/contents/{id}` |

### Dates & Windows

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `episode_airdate` | String | ISO 8601 date format | `""` | - | Optional | Original broadcast date, affects windowing | Warning if invalid format | All networks | `metadata.properties.episodeAirdate.value` | `/reachengine/api/abc/packages/{id}` |
| `episode_window_start` | String | ISO 8601 datetime format | `""` | - | Optional | Licensing window start, affects partner delivery | Used in partner window calculations | All networks | Partner-specific window properties | `/reachengine/api/abc/packages/{id}/partnerPackages` |
| `episode_window_end` | String | ISO 8601 datetime format | `""` | - | Optional | Licensing window end, affects partner delivery | Used in partner window calculations | All networks | Partner-specific window properties | `/reachengine/api/abc/packages/{id}/partnerPackages` |
| `bankable_date` | String | ISO 8601 datetime format | `""` | - | Optional | Financial reporting date | No validation | All networks | `metadata.properties.bankableDate.value` | `/reachengine/api/abc/packages/{id}` |
| `content_start_year` | Integer | Year validation (1900-current) | `""` | - | Optional | Content production year | Warning if out of range | All networks | `metadata.properties.contentStartYear.value` | `/reachengine/api/abc/packages/{id}` |

### Genre & Classification

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `genre_code` | String/Array | Valid genre codes | `""` | - | Optional | MVPD genre classification, can be pipe-separated | Converts array to pipe-separated | All networks | Partner-specific genre properties | `/reachengine/api/abc/contents/{id}` |
| `reach_genre` | String | Mapped genre validation | `"Drama"` | - | **Required** | Reach-specific genre mapping from genre_code | Maps from genre_code or defaults | All networks | `genre` (content creation) | `/reachengine/api/abc/contents` |

### Cast & Crew

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `actors` | Array | Structured actor objects | `[]` | - | Optional | Cast information from Bolt API or parsed string | Silent failure if Bolt unavailable | Movies only | `actors` (season update) | `/reachengine/api/abc/contents/seasons/{id}` |
| `actors_raw` | String | Comma-separated names | `""` | - | Optional | Raw actor string, parsed into structured format | Parses "Last, First" format | All networks | `metadata.properties.actorsRaw.value` | `/reachengine/api/abc/packages/{id}` |

### Analytics & Tracking

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `comscore_c6` | String | Comscore format validation | `""` | - | Optional | Comscore analytics identifier | No validation | All networks | `metadata.properties.comscoreC6.value` | `/reachengine/api/abc/contents/{id}` |
| `ad_content_id` | String | Ad system validation | `""` | - | Optional | Advertisement content identifier | No validation | All networks | `metadata.properties.adContentId.value` | `/reachengine/api/abc/packages/{id}` |

### MVPD & Distribution

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `sd_mappings` | String | MVPD mapping validation | `""` | - | Optional | Standard definition distribution categories | Generated from affiliate rules | Network-specific | Partner-specific SD mapping properties | `/reachengine/api/abc/contents/seasons/{id}` |
| `hd_mappings` | String | MVPD mapping validation | `""` | - | Optional | High definition distribution categories | Generated from affiliate rules | Network-specific | Partner-specific HD mapping properties | `/reachengine/api/abc/contents/seasons/{id}` |
| `categories_and_distribution` | String | Distribution validation | `""` | - | Optional | Partner-specific category mappings | Generated from partner rules | Partner-specific | Partner-specific category properties | `/reachengine/api/abc/contents/{id}` |

### Special Flags

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `original_content_20cf` | Boolean | Boolean validation | `false` | - | Optional | 20th Century Fox original content flag | Defaults to false if missing | FX networks | `metadata.properties.originalContent20cf.value` | `/reachengine/api/abc/packages/{id}` |
| `fmx_retro` | Boolean | Boolean validation | `false` | - | Optional | FXM retro content classification | Defaults to false if missing | FXM only | `metadata.properties.fmxRetro.value` | `/reachengine/api/abc/packages/{id}` |
| `is_4k` | String/Boolean | 4K content validation | `""` | - | Optional | 4K/UHD content indicator | No validation | All networks | `metadata.properties.is4k.value` | `/reachengine/api/abc/packages/{id}` |

### Asset Information

| Field Name | Data Type | Validation Needed | Default Value | Length Limit | Required/Optional | Business Rules | Error Handling | Network Dependencies | Reach Engine Field | API Endpoint |
|------------|-----------|-------------------|---------------|--------------|-------------------|----------------|----------------|---------------------|-------------------|--------------|
| `movie_asset_id` | String | Asset ID validation | `""` | - | Optional | Movie-specific asset identifier | No validation | Movies only | `metadata.properties.movieAssetId.value` | `/reachengine/api/abc/packages/{id}` |
| `source_filename` | String | Filename validation | `""` | - | Optional | Original source file reference | No validation | All networks | `metadata.properties.sourceFilename.value` | `/reachengine/api/abc/packages/{id}` |
| `display_run_time` | String | Time format validation | `""` | - | Optional | Human-readable runtime | No validation | All networks | `metadata.properties.displayRunTime.value` | `/reachengine/api/abc/packages/{id}` |
| `closed_captioning` | String | CC format validation | `""` | - | Optional | Closed captioning availability | No validation | All networks | `metadata.properties.closedCaptioning.value` | `/reachengine/api/abc/packages/{id}` |
| `audio_type` | String | Audio format validation | `""` | - | Optional | Audio format specification | No validation | All networks | `metadata.properties.audioType.value` | `/reachengine/api/abc/packages/{id}` |
| `languages` | String | Language code validation | `""` | - | Optional | Available language tracks | No validation | All networks | `metadata.properties.languages.value` | `/reachengine/api/abc/packages/{id}` |

## Additional Business Rules by Context

### Partner-Specific Rules

#### Comcast
- Requires specific window calculations with C-Type adjustments
- C-Types are excluded from TVE distribution
- Uses different date formats for STB vs TVE windows
- Supports both Free and Paid TVE categories

#### Charter
- **Network Limitation**: Only supports FX and National Geographic networks
- **Category Mapping**: Uses "Primetime Free" for FX, "News Science Free" for National Geographic
- **Title Brief**: Limited to 9 characters for series titles
- **Content Exclusion**: Does not receive FXX, FXM, or Nat Geo Wild content

#### DISH
- Uses special date format: `3000-01-01T08:00:00.000+0000` for "never expires"
- Different window calculation logic compared to other partners
- Supports both STB and general DISH categories

#### Armstrong
- **Character Limit**: 32-character limit on title fields
- **Validation**: Enforces strict length validation with truncation
- **Content Support**: Supports both HD and SD content categories

### Network-Specific Rules

#### FX Networks (FX, FXX, FXM)
- **Default Categories**: C3/D4 categories by default
- **Genre Mappings**: Specific genre code mappings for drama, comedy, etc.
- **Original Content**: Uses `original_content_20cf` flag for content classification
- **FXM Special**: FXM content uses `fmx_retro` flag for retro classification

#### ABC Networks
- **Media Profiles**: Different media profile requirements (PNG thumbnails required)
- **Localish**: Special handling for ABC Localish Studios content
- **Content Types**: Supports both Long Form and Short Form content

#### National Geographic (NG, NGW)
- **Category Structure**: Special category mappings for educational content
- **Charter Integration**: Specific "News Science Free" category for Charter
- **Genre Defaults**: Different genre mapping compared to entertainment networks

### Content Type Rules

#### Series Content
- **Required Fields**: `season_num`, `episode_number` are highly recommended
- **Title Brief**: Limited to 9 characters maximum
- **Category Structure**: Uses Episode, Episode D Type, Episode C Type categories
- **Season Creation**: Automatically creates season structure in Reach

#### Movie Content
- **Title Brief**: Limited to 15 characters maximum
- **Category Structure**: Uses Movie/Special categories
- **Cast Information**: Supports Bolt API integration for cast/crew data
- **Asset ID**: Uses `movie_asset_id` for asset tracking

#### Special Content
- **Hybrid Rules**: Combines movie and series rules
- **Category Structure**: Uses Special and Special C Type categories
- **TMS Handling**: Can inherit TMS ID from episode level

### Validation Hierarchy

#### Critical Fields (System Failure if Missing)
1. `program_type` - Determines entire package structure
2. `content_name` - Required for all content identification
3. `network` - Determines partner distribution and rules
4. `material_id` - Must be globally unique identifier

#### Important Fields (Warnings if Missing)
1. `episode_c_type` - Controls distribution channels
2. `episode_d_type` - Controls VOD timing
3. `country` - Territory restrictions
4. `content_type` - Media profile selection

#### Recommended Fields (Soft Warnings)
1. `tms_id` - Metadata enrichment
2. `season_num` - Required for series content
3. `episode_number` - Required for episodic content
4. `copyright` - Legal compliance

#### Optional Fields (Graceful Degradation)
- All other fields provide enhanced functionality but system continues without them
- Default values are applied where appropriate
- Silent failures for non-critical integrations (Bolt API, etc.)

### Data Format Requirements

#### Date/Time Fields
- **Format**: ISO 8601 format (`YYYY-MM-DDTHH:MM:SS.sssZ`)
- **Timezone**: UTC timezone required (`+0000` or `Z`)
- **Special Values**: `9999-01-01T00:00:00.000Z` for "never expires"
- **Validation**: Year range 1900 to current year + 10

#### String Sanitization
- **Folder Names**: Alphanumeric characters and hyphens only
- **Auto-Cleaning**: Invalid characters automatically removed
- **Length Limits**: Auto-truncation applied where specified

#### Identifier Formats
- **TMS IDs**: Must follow Tribune Media Services format
- **Material IDs**: Must be globally unique across all content
- **Radar IDs**: Must be valid Radar system identifiers

## Implementation Notes

### Error Handling Strategy
- **Critical Errors**: Throw exceptions and halt processing
- **Warnings**: Log warnings but continue processing
- **Silent Failures**: Continue with default values for non-critical features

### Performance Considerations
- **Bolt API**: Cast/crew lookup may timeout, implement graceful fallback
- **Partner Rules**: Cache partner-specific rules to avoid repeated lookups
- **Validation**: Implement validation caching for repeated content processing

### Maintenance Guidelines
- **Network Addition**: New networks require partner rule configuration
- **Partner Addition**: New partners require category mapping and window logic
- **Field Addition**: New fields should include default values and validation rules

## Reach Engine Field Mapping Summary

### API Endpoints Used
1. **Content Creation**: `/reachengine/api/abc/contents?type={program_type}`
2. **Season Creation**: `/reachengine/api/abc/contents/{id}/seasons`
3. **Package Creation**: `/reachengine/api/abc/packages`
4. **Package Update**: `/reachengine/api/abc/packages/{id}`
5. **Partner Distribution**: `/reachengine/api/abc/packages/{id}/partnerPackages`
6. **Season Update**: `/reachengine/api/abc/contents/seasons/{id}`

### Field Mapping Patterns
- **Direct Mapping**: Most ADI JSON fields map directly to `metadata.properties.{fieldName}.value`
- **Content Level**: Fields like `content_name`, `network`, `tms_id` are stored at content level
- **Package Level**: Episode-specific fields are stored in package metadata properties
- **Season Level**: Season-specific fields like `season_short_synopsis` are stored at season level
- **Partner-Specific**: MVPD fields like `sd_mappings`, `hd_mappings` are partner-specific properties

### Special Mappings
- `material_id` → `metadata.properties.trafficCode.value` (package level)
- `episode_production_number` → `metadata.properties.trafficCode.value` (same as material_id)
- `episode_title` → `name` (package creation) and `title` (package update)
- `episode_category` → `categoryType` (package creation) and `category` (package update)
- `cms_folder_name` → `metadata.properties.dATGGoPubFolder.value` (content level)
- `tms_id` → `metadata.properties.tMSSeriesID.value` (content level)

### Partner-Specific Fields
Fields like `title_brief`, `show_folder`, `sd_mappings`, and `hd_mappings` are stored as partner-specific properties with naming patterns like:
- `{partnerName}SeriesTitleBrief`
- `{partnerName}SeriesShowFolder`
- `{partnerName}SeriesSeasonSDMappingStrings`
- `{partnerName}SeriesSeasonHDMappingStrings`

---

**Document Version**: 1.1
**Last Updated**: 2025-01-09
**Source**: Reach Series Toolbox (RASCL) Codebase Analysis
**Maintainer**: Reach Development Team
