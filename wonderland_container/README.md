# Code Documentation Wonderland Container

## Architecture

The Wonderland Container project implements a containerized application architecture for processing and monitoring Wonderland ingestion statuses. The core components are:

1. **Container Application Layer**
   - Main entry point (`main.py`)
   - Processing flow (`flow.py`)
   - Utility modules for configuration and AWS services

2. **AWS Services Integration**
   - SQS for message queueing
   - DynamoDB for status tracking
   - Secrets Manager for credential storage

3. **External API Integration**
   - Wonderland API for status validation
   - Token-based authentication
   - RESTful service communication

## Folder Structure

```
wonderland_container/
├── src/
│   ├── main.py             # Main container entry point
│   ├── flow.py             # Core processing logic
│   └── requirements.txt    # Dependencies
├── tests/                  # Test suite directory
├── Dockerfile              # Container definition
├── .dockerignore           # Docker build exclusions
├── .wonderland-container-ci.yml  # CI configuration
└── README.md               # Project documentation
```

## Tasks Diagram


<div style="text-align: center;">
    <img src="https://confluence.disney.com/download/attachments/1668898406/wonderland_container.png?version=1&modificationDate=1742533010102&api=v2" alt="Wonderland Task Diagram" height="480px" />
</div>

## Tasks

### 1. Status Monitoring Pipeline

The primary workflow consists of these sequential tasks:

- **Container Initialization**
  - Loads configuration from SettingsManagerService
  - Establishes DynamoDB connection
  - Creates initial status record with UUID
  - Prepares global variables for processing

- **DynamoDB Query Processing**
  - Queries records from reach-to-wonderland table
  - Validates query results existence
  - Prepares records for status verification

- **Authentication Management**
  - Utilizes `create_auth_token` to generate secure API tokens
  - Retrieves endpoint configurations from secrets
  - Formats authorization headers for API requests

- **Sidecar Status Verification**
  - Fetches current status from Wonderland API
  - Validates against configuration-defined status values
  - Makes processing decisions based on verification results

### 2. Status Management

- **DynamoDB Status Tracking**
  - Creates initial process records with unique client_id
  - Maintains process state with timestamps
  - Status workflow:
    - `Ready to process` (initial)
    - `COMPLETED` (success)
    - `FAILED` (error)

- **Sidecar Timestamp Updates**
  - Updates timestamp for non-completed sidecars
  - Tracks last verification attempt
  - Ensures records remain current for future processing

### 3. Messaging Support

- **SQS Message Generation**
  - Triggers downstream processing via SQS
  - Utilizes `send_message_to_sqs` for queue management
  - Formats messages with required attributes
  - Applies message group ID for FIFO queues

- **Error Handling**
  - Comprehensive exception handling at multiple levels
  - Detailed error logging with context
  - Status updates on failure conditions

## Services

1. **Wonderland Container**
   - Primary containerized service for status monitoring
   - Periodic execution via container orchestration
   - Integration bridge between Reach and Wonderland

2. **Wonderland API**
   - External service for status verification
   - Authentication endpoint
   - Sidecar details endpoint

3. **SQS**
   - Message queue for downstream triggers
   - Configured with message grouping
   - Asynchronous processing support

## Tools

1. **Settings Manager**
   - Environment-specific configuration
   - Dynamic settings retrieval
   - Centralized configuration management

2. **Request Handler**
   - API communication abstraction
   - Authentication token management
   - Response serialization

3. **Logging Service**
   - Structured logging with context
   - Error tracking and notification
   - Operational visibility

## Configuration and running code
### Environment Variables and Configuration

The Wonderland Container service relies on several key configuration parameters managed through environment variables and AWS service integrations:

- **Container Configuration**: The service loads settings from the `SettingsManagerService` during initialization in `main.py`. This includes environment-specific parameters and connection details for AWS services.

- **DynamoDB Configuration**: The container queries records from the reach-to-wonderland table, with table names and query parameters configured through environment variables. These settings determine which records are processed and how status tracking is maintained.

- **Wonderland API Settings**: Authentication tokens and endpoint URLs are managed through the `create_auth_token` function, with base URLs and credentials retrieved from AWS Secrets Manager. These parameters enable secure communication with the Wonderland API for status verification.

- **SQS Configuration**: Message queue settings, including queue URL and message attributes, are configured through environment variables. These control how downstream processing is triggered and how message grouping is applied for FIFO queues.

The container follows a centralized configuration approach, with Docker environment variables for non-sensitive settings and AWS Secrets Manager for credentials and endpoint information, promoting security and maintainability in a containerized environment.

## Links to Repository

The project is maintained in a [Tacdev Project - Wonderland Docker Container](https://gitlab.disney.com/reach-engine/workflows/tacdev-projects/-/tree/development/wonderland_container?ref_type=heads). Access requires appropriate permissions and can be granted through the organization's DevOps team.

This documentation provides a technical overview of the Wonderland Container project's architecture and components, following Python best practices and Docker containerization patterns.
