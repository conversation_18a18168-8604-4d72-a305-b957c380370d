# Wonderland Container

version: 1.0.0

Created Date: 2024-03-21

## Brief Description

Containerized application for processing and monitoring Wonderland ingestion statuses. This service queries records from DynamoDB, verifies statuses via the Wonderland API, updates status tracking information, and triggers downstream processing through SQS messages.

## Authors

- <PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>

## Index:

- Infra
  - Docker Container
  - AWS ECR
  - CI/CD Pipeline (.wonderland-container-ci.yml)
  - AWS SQS
  - AWS DynamoDB

- Code
  - Main Entry Point (main.py)
  - Processing Flow (flow.py)
  - Authentication and API Integration
  - Status Management Logic

- Configuration and running code
  - Environment Variables
  - Docker Configuration
  - AWS Service Integration
  - Deployment Process

- Test (integration test)
  - Unit Tests
  - Integration with Reach
  - End-to-end API Testing

## Links to Repositories and Main Project Confluence

- Repository: [Tacdev Project - Wonderland Docker Container](https://gitlab.disney.com/reach-engine/workflows/tacdev-projects/-/tree/development/wonderland_container?ref_type=heads)
-
Confluence: [Wonderland Documentation](https://confluence.disney.com/display/REACH/Code+Documentation+Wonderland+Container)