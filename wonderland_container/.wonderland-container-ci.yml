default:
  image: docker:24.0.5
  services:
    - name: docker:24.0.5-dind
      variables:
        HEALTHCHECK_TCP_PORT: "2375"
  before_script:
    - docker info

workflow:
  rules:
    - changes:
        - wonderland_container/**/*
      when: always
    - when: never

stages:
  - build
  - fake-job

variables:
  DOCKER_TLS_CERTDIR: ""  # Disable TLS for Docker-in-Docker
  DOCKER_HOST: "tcp://docker:2375"
  SHARED_LAYER_PATH: "./shared_layer"

.build-template:
  stage: build
  before_script:
    - apk add --no-cache aws-cli
    - sh scripts/docker_aws.sh
  script:
    - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
    - cp -r $SHARED_LAYER_PATH wonderland_container/shared_layer
    - cp pyproject.toml wonderland_container/
    - cd wonderland_container
    - docker build -t wonderland .
    - docker tag wonderland:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/wonderland-${ENV}:latest
    - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/wonderland-${ENV}:latest

noop-job:
  stage: fake-job
  script:
    - echo "No updates required for this pipeline run"
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push" || $PARENT_PIPELINE_SOURCE == "merge_request_event"

build-sbx:
  extends: .build-template
  environment:
    name: sbx
  rules:
    - if: $CI_COMMIT_TAG =~ /^release\/[0-9]+\.[0-9]+\.[0-9]+-sbx$/
      changes:
        - wonderland_container/**/*

build-qa:
  extends: .build-template
  environment:
    name: qa
  rules:
    - if: $CI_COMMIT_BRANCH == "qa" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - wonderland_container/**/*

build-prod:
  extends: .build-template
  environment:
    name: prod
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - wonderland_container/**/*
