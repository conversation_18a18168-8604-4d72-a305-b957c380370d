from utils.config import ENV, settings_config
from utils.expresslane_utils import logger_service
from utils.schemas.dynamodb.wonderland import WonderlandConfig
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .flow import flow


def main() -> None:
    """
    This is the entry point for wonderland container
    """
    logger_service.info("************ IZMA / Running Main Container ************")

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    event_settings = settings_service.initialize("wonderland")
    config: WonderlandConfig = event_settings.get_config()  # type: ignore
    status_table_name = event_settings.get_status_table()
    secret_key = config.secret_key
    sqs_config = config.sqs_config
    api_config = config.api_config
    wonderland_validations = api_config.wonderland_validations
    tacdev_reach_table = api_config.tacdev_reach_table

    status_tracker = StatusTrackerSchema(landing_bucket=tacdev_reach_table)
    process_status_tracker = ProcessStatusTracker(status_table_name)
    process_status_tracker.create_initial_status(create_item=status_tracker)

    global_variables = {
        "message_group_id": sqs_config.message_group_id,
        "desired_keys": sqs_config.desired_keys,
        "sqs_topic": sqs_config.sqs_topic,
        "query": sqs_config.query,
        "method": "qrl_query_items",
        "api_config": {
            "token_url": api_config.token_url,
            "main_url": api_config.main_url,
            "api_url": api_config.api_url,
            "secrets": secret_key,
            "wonderland_validations": {
                "update_status": wonderland_validations.update_status,
                "status": wonderland_validations.status,
            },
        },
        "tacdev_reach_table": f"{tacdev_reach_table}-{ENV}",
    }
    try:
        process_status_tracker.mark_as_running(status_tracker.client_id)

        flow(global_variables)

        process_status_tracker.mark_as_completed(status_tracker.client_id)

        logger_service.info("************ IZMA / Main Container Finished ************")

    except BaseException as error:
        logger_service.error("IZMA ERROR: %s", event_settings.event_project_name)
        error_message = f"{error}"
        process_status_tracker.mark_as_failed(status_tracker.client_id, error_message)


if __name__ == "__main__":
    main()
