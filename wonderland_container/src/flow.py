from utils.expresslane_utils import logger_service
from utils.flow_manager import FlowManager
from utils.task_handlers import (
    TaskCreateAuthToken,
    TaskExtractObj,
    TaskFetchWonderlandContainerDetailsTask,
)


def flow(global_variables: dict, event_settings: dict = None) -> None:
    logger_service.info("************ IZMA / Main Flow Container Started ************")
    logger_service.info(
        "Reach Flow with args: \n %s, global_variables: \n %s",
        event_settings,
        global_variables,
    )

    try:
        _flow = FlowManager(
            name="Wonderland Container",
            tasks=[
                TaskExtractObj(),
                TaskCreateAuthToken(),
                TaskFetchWonderlandContainerDetailsTask(),
            ],
            event={},
            global_variables=global_variables,
        )

        logger_service.info("IZMA / Main Flow Container Finished")

        return _flow()

    except Exception as error:
        logger_service.error("error while running %s", str(error))
        raise Exception(f"error while running {error}")
