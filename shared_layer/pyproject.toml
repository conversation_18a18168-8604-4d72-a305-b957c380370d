[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "utils"
version = "0.0.1"
authors = [
  { name="MASS REACH Team"},
]
description = "A package for utils layer"
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = ["requests", "pytz", "psutil", "pydantic", "fuzzywuzzy==0.18.0"]
[project.urls]
Homepage = "https://gitlab.disney.com/reach-engine/workflows/nielsen-iris-integration"
