# Shared-layer

## Getting started

## Definition
```
shared_layer
├── README.md
├── python
│   └── utils
│       ├── DynamoUtils.py
│       ├── KMSUtils.py
│       ├── RequestsHandler.py
│       ├── S3Utils.py
│       ├── SecretsManagerUtils.py
│       ├── __init__.py
│       ├── __pycache__
│       │   ├── DynamoUtils.cpython-311.pyc
│       │   ├── KMSUtils.cpython-311.pyc
│       │   ├── RequestsHandler.cpython-311.pyc
│       │   ├── S3Utils.cpython-311.pyc
│       │   ├── SecretsManagerUtils.cpython-311.pyc
│       │   └── __init__.cpython-311.pyc
│       ├── config.py
│       └── loggin_service.py
├── requirements.txt
├── src
│   └── utils
│       ├── DynamoUtils.py
│       ├── KMSUtils.py
│       ├── RequestsHandler.py
│       ├── S3Utils.py
│       ├── SecretsManagerUtils.py
│       ├── __init__.py
│       ├── __pycache__
│       │   ├── DynamoUtils.cpython-311.pyc
│       │   ├── KMSUtils.cpython-311.pyc
│       │   ├── RequestsHandler.cpython-311.pyc
│       │   ├── S3Utils.cpython-311.pyc
│       │   ├── SecretsManagerUtils.cpython-311.pyc
│       │   └── __init__.cpython-311.pyc
│       ├── config.py
│       └── loggin_service.py
└── tests
    ├── conftest.py
    ├── test_dynamodb.py
    ├── test_kms_utils.py
    ├── test_request_handler.py
    ├── test_s3.py
    └── test_secret_manager.py
```

## Test and Deploy
- ./shared-layer
- pytest

## Tools
- Python 3.12
- Boto3
- AWS
- SAM CLI
- coverage
- psutil

## ENVIRONMENT VARIABLES
### Define de following env variables in you .env (local):
- **STAGE**: The stage where you are running this (production, development or localyournamelastname)
- **AWS_ACCESS_KEY_ID**: Your aws secrete
- **AWS_SECRET_ACCESS_KEY**: Your aws access key
- **AWS_DEFAULT_REGION**: Your aws region
- **ENV**: (DEV, PROD)
