import json


def serialize_json(response) -> dict:
    """
    Converts a JSON response or string into a Python dictionary.

    :param response: A JSON string or a Response object.
    :return: Dictionary representation of the JSON data.
    """
    try:
        if hasattr(response, "json"):  # Check if it's a Response object
            return response.json()  # Use the built-in .json() method
        elif isinstance(
            response, (str, bytes, bytearray)
        ):  # Check if it's a JSON string
            return json.loads(response)
        else:
            raise TypeError(
                "Invalid input type. Expected Response object or JSON string."
            )
    except (json.JSONDecodeError, TypeError) as e:
        print(f"Error decoding JSON: {e}")
        return {}
