def default_media_profiles():
    """
    The default media accept profiles to be added to all series/seasons
    :return:
    """
    j = [
        {
            "type": "Long Form Content",
            "profile": "PNG 1280x720",
            "validation": "OPTIONAL",
        },
        {
            "type": "Long Form Content",
            "profile": "PNG 1920x1080",
            "validation": "OPTIONAL",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 2.0 On Ch 7/8 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 5.1/2.0 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 2.0 On Ch 7/8 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 5.1/2.0 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MXF 1280x720 59.94p 50mbps PCM 8Ch W/CC 01hrTC",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "PNG 1280x720",
            "validation": "OPTIONAL",
        },
        {
            "type": "Short Form Content",
            "profile": "PNG 1920x1080",
            "validation": "OPTIONAL",
        },
        {
            "type": "Short Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 2.0 On Ch 7/8 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 5.1/2.0 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1280x720 59.94p 2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1280x720 59.94p 5.1/2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1920x1080 23.98p 2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1920x1080 23.98p 5.1/2.0",
            "validation": "REQUIRED",
        },
    ]

    return j


def default_media_profiles_fxng():
    """
    The default media accept profiles to be added to all series/seasons
    :return:
    """
    j = [
        {
            "type": "Long Form Content",
            "profile": "PNG 1280x720",
            "validation": "OPTIONAL",
        },
        {
            "type": "Long Form Content",
            "profile": "PNG 1920x1080",
            "validation": "OPTIONAL",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 2.0 On Ch 7/8 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 5.1/2.0 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 2.0 On Ch 7/8 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 5.1/2.0 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MXF 1280x720 59.94p 50mbps PCM 8Ch W/CC 01hrTC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MPEG-TS 1280x720 59.94p 50mbps AC3 6Ch w/CC 00hrTC (D-Type from MH)",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "PNG 1280x720",
            "validation": "OPTIONAL",
        },
        {
            "type": "Short Form Content",
            "profile": "PNG 1920x1080",
            "validation": "OPTIONAL",
        },
        {
            "type": "Short Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 2.0 On Ch 7/8 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 5.1/2.0 - 01HR TC",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1280x720 59.94p 2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1280x720 59.94p 5.1/2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1920x1080 23.98p 2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1920x1080 23.98p 5.1/2.0",
            "validation": "REQUIRED",
        },
    ]

    return j


def default_partners_and_categories():
    """
    The default partners and categories for FX and NG nets (FX, FXX, FXM, National Geographic, Nat Geo Wild)
    :return:
    """
    j = [
        {
            "namePartner": "Charter",
            "typeCategories": [
                {"categoryName": "Clean", "networks": ["FX", "National Geographic"]},
                {"categoryName": "CType", "networks": ["FX", "National Geographic"]},
            ],
        },
        {
            "namePartner": "Comcast Unified",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {
                    "categoryName": "CType",
                    "networks": ["FX", "FXX", "National Geographic", "Nat Geo Wild"],
                },
            ],
        },
        {
            "namePartner": "DirecTV",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {"categoryName": "CType", "networks": []},
            ],
        },
        {
            "namePartner": "DISH",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {
                    "categoryName": "CType",
                    "networks": ["FX", "FXX", "National Geographic", "Nat Geo Wild"],
                },
            ],
        },
        {
            "namePartner": "DTCI Video Platforms",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {"categoryName": "CType", "networks": []},
            ],
        },
        {
            "namePartner": "FuboTV",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {
                    "categoryName": "CType",
                    "networks": ["FX", "FXX", "National Geographic", "Nat Geo Wild"],
                },
            ],
        },
        {
            "namePartner": "Hulu dMVPD",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {
                    "categoryName": "CType",
                    "networks": ["FX", "FXX", "National Geographic", "Nat Geo Wild"],
                },
            ],
        },
        {
            "namePartner": "Verizon TVE",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {
                    "categoryName": "CType",
                    "networks": [
                        # "National Geographic",
                        # "Nat Geo Wild"
                    ],
                },
            ],
        },
        # {
        #     "namePartner": "Vidgo",
        #     "typeCategories": [
        #         {
        #             "categoryName": "Clean",
        #             "networks": [
        #                 "FX",
        #                 "FXX",
        #                 "FXM",
        #                 "National Geographic",
        #                 "Nat Geo Wild"
        #             ]
        #         },
        #         {
        #             "categoryName": "CType",
        #             "networks": [
        #                 "FX",
        #                 "FXX",
        #                 "National Geographic",
        #                 "Nat Geo Wild"
        #             ]
        #         }
        #     ]
        # },
        {
            "namePartner": "YouTube dMVPD",
            "typeCategories": [
                {
                    "categoryName": "Clean",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {"categoryName": "CType", "networks": []},
            ],
        },
        {
            "namePartner": "MVPD Affiliate Group",
            "typeCategories": [
                {
                    "categoryName": "VOD",
                    "networks": [
                        "FX",
                        "FXX",
                        "FXM",
                        "National Geographic",
                        "Nat Geo Wild",
                    ],
                },
                {"categoryName": "CType", "networks": []},
            ],
        },
    ]

    return j
