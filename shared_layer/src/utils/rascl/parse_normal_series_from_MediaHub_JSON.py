# pylint: skip-file
import inspect
import json
import re
import string
from datetime import datetime, timedelta
from xml.dom.minidom import parseString
from xml.etree import ElementTree as ET
from xml.etree.ElementTree import Element, SubElement

import requests
from dateutil import tz
from fuzzywuzzy import fuzz

from utils.LoggerService import logger_service

# custom scripts
from .default_properties import *


def config_parse(config):
    bolt_bearer_token = config.get("bolt_bearer_token")
    if not bolt_bearer_token.startswith("Bearer ") and bolt_bearer_token != "":
        bolt_bearer_token = "Bearer " + bolt_bearer_token
    config_json = {
        "auth_z_client_id": config.get("auth_z_client_id"),
        "auth_z_client_secret": config.get("auth_z_client_secret"),
        "auth_z_grant_type": config.get("auth_z_grant_type"),
        "auth_z_scope": config.get("auth_z_scope"),
        "bolt_bearer_token": bolt_bearer_token,
        "gracenote_api_key": config.get("gracenote_api_key"),
    }
    return config_json


def utc_string_to_datetime(utc_string):
    utc_clean = utc_string.replace("Z", "UTC")
    dt = datetime.strptime(utc_clean, "%Y-%m-%dT%H:%M:%S%Z")
    return dt


def authz_get_token(client_id, client_secret, grant_type, scope):
    """
    Get an oauth token from AuthZ
    :param client_id:
    :param client_secret:
    :param grant_type:
    :param scope:
    :return:
    """

    params = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": grant_type,
        "scope": scope,
    }

    resp = requests.post(
        url="https://cp-auth-service.maestro.dmed.technology/v2/as/token.oauth2",
        params=params,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        return {
            "Authorization": resp_json["token_type"] + " " + resp_json["access_token"]
        }
    else:
        raise Exception("Unable to get token from AuthZ")


def bolt_get_series_from_group_id(auth_bearer_token, radar_group_id):
    params = {"radarGroupId": radar_group_id}
    auth_token = {"Authorization": auth_bearer_token}

    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/series",
        params=params,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if len(resp_json) == 1:
            return resp_json[0]
        else:
            return resp_json
    else:
        resp.raise_for_status()


def bolt_get_movie_from_group_id(auth_bearer_token, radar_group_id):
    params = {"radarGroupId": radar_group_id}
    auth_token = {"Authorization": auth_bearer_token}
    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/feature",
        params=params,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if len(resp_json) == 1:
            return resp_json[0]
        else:
            return resp_json
    else:
        resp.raise_for_status()


def bolt_get_season_from_series_id(auth_bearer_token, bolt_series_id, season_num):
    params = {"seriesId": bolt_series_id}
    auth_token = {"Authorization": auth_bearer_token}
    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/season",
        params=params,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        for this_season in resp_json:
            try:
                this_season_num = this_season["seasonNumber"]
            except KeyError:
                continue

            if str(this_season_num) == str(season_num):
                return this_season

        return ""
    else:
        resp.raise_for_status()


def bolt_get_cast_from_season_id(auth_bearer_token, bolt_season_id):
    auth_token = {"Authorization": auth_bearer_token}

    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/season/{}/cast/original/expanded".format(
            bolt_season_id
        ),
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        return resp_json
    else:
        resp.raise_for_status()


def bolt_get_cast_from_feature_id(auth_bearer_token, bolt_feature_id):
    # params = {"seriesId": bolt_series_id}
    auth_token = {"Authorization": auth_bearer_token}

    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/feature/{}/cast/original/expanded".format(
            bolt_feature_id
        ),
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # return resp_json
    else:
        resp_json = []
        resp.raise_for_status()

    # only return first 10 actors (e.g. Fight Club has everyone - 53 actors)
    cast_list_short = []
    for this_actor in resp_json:
        listing_num_str = this_actor.get("member", {}).get("order")
        if listing_num_str is not None:
            try:
                if int(listing_num_str) <= 10:
                    cast_list_short.append(this_actor)
            except:
                continue

        if len(cast_list_short) > 9:
            break

    return cast_list_short


def gracenote_get_program_details(api_key, tms_id):
    """
    Get program details from Gracenote given the TMS ID. Note: this can grab Movie, Series or Episode details.
    :param api_key:
    :param tms_id:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    url = "http://data.tmsapi.com/v1.1/programs/{}".format(tms_id)
    params = {"api_key": api_key}

    try:
        response = requests.get(url=url, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json

    return {}


def gracenote_get_series_details(api_key, series_id):
    """
    Get series details from Gracenote given the series id (e.g. "17389621") Note: series id only works for series, not movies
    :param api_key:
    :param series_id:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    url = "http://data.tmsapi.com/v1.1/series/{}".format(series_id)
    params = {"api_key": api_key, "titleLang": "en"}

    try:
        response = requests.get(url=url, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json

    return {}


def gracenote_get_series_or_movie_details(api_key, root_id_or_series_id):
    """
    Get series details from Gracenote given the series id (e.g. "17389621")
    Note: series id only works for series, not movies
    root id is for movies (yes, can also be series but let's try to keep things separated)
    :param api_key:
    :param root_id_or_series_id:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    url = "http://data.tmsapi.com/v1.1/series/{}".format(root_id_or_series_id)
    params = {"api_key": api_key, "titleLang": "en"}

    try:
        response = requests.get(url=url, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json

    return {}


def gracenote_get_all_images(api_key, tms_id):
    """
    Given a TMS ID, get all image URLs from Gracenote. Note: the uri must be preceded by https://dabc.tmsimg.com/
    Example: https://dabc.tmsimg.com/    +     assets/p16929579_p_v12_ah.jpg?h=2880
    :param api_key:
    :param tms_id:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    url = "http://data.tmsapi.com/v1.1/programs/{}/images".format(tms_id)
    params = {"api_key": api_key}

    try:
        response = requests.get(url=url, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json

    return []


def gracenote_get_short_description(gracenote_result_json):
    """
    Check the Gracenote results and extract certain data
    :param gracenote_result_json: this should be a json (dict) object
    :return:
    """

    short_description = gracenote_result_json.get("shortDescription", "")
    return short_description


def gracenote_get_long_description(gracenote_result_json):
    """
    Check the Gracenote results and extract certain data
    :param gracenote_result_json: this should be a json (dict) object
    :return:
    """

    long_description = gracenote_result_json.get("longDescription", "")
    return long_description


def gracenote_get_start_year(gracenote_result_json):
    """
    Check the Gracenote results and extract certain data
    :param gracenote_result_json: this should be a json (dict) object
    :return:
    """

    start_year = gracenote_result_json.get("releaseYear", "")
    return start_year


def gracenote_get_genre_list(gracenote_result_json):
    """
    Check the Gracenote results and extract certain data
    :param gracenote_result_json: this should be a json (dict) object
    :return:
    """

    genre_list = gracenote_result_json.get("genres", [])
    return genre_list


def gracenote_get_subtype(gracenote_result_json):
    """
    Check the Gracenote results and extract certain data
    :param gracenote_result_json: this should be a json (dict) object
    :return:
    """

    subtype = gracenote_result_json.get("subType", "")
    return subtype


def titleapi_run_search_for_episode(auth_token, series_name, season_num, episode_title):
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: SERIES_NAME, values: [ \""""
        + series_name
        + """\" ] }, 
          { field: SEASON_NAME, values: [ \""""
        + season_num
        + """\" ] }, 
          { field: NAME, values: [ \""""
        + episode_title
        + """\" ] } 
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print("TitleAPI error (episode search): {}".format(this_err["message"]))

        return resp_json
        # return {'Authorization': resp_json['token_type'] + ' ' + resp_json['access_token']}
    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_run_search_for_episode_given_episode_num_and_description(
    auth_token, series_name, season_num, episode_num, description
):
    """
    Episode titles might be off. Loop through everything in the season. See if the episode number and descriptions match
    If so, use that title.
    :param auth_token:
    :param series_name:
    :param season_num:
    :param episode_num:
    :param description:
    :return:
    """
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: SERIES_NAME, values: [ \""""
        + series_name
        + """\" ] }, 
          { field: SEASON_NAME, values: [ \""""
        + season_num
        + """\" ] }
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print(
                    "TitleAPI error (episode matching num/descrip): {}".format(
                        this_err["message"]
                    )
                )
                return {}

        all_episode_edges = (
            resp_json.get("data", {}).get("searchTitles", {}).get("edges", [])
        )
        for this_episode in all_episode_edges:
            this_episode_number = this_episode.get("node", {}).get(
                "firstAirRunningOrder", ""
            )
            this_episode_name = this_episode.get("node", {}).get("name", "")
            try:
                this_episode_number = int(this_episode_number)
            except:
                continue

            if int(episode_num) == int(this_episode_number):
                # 'synopses' can sometimes be None (null). So we work some magic in case they are None (and not a dict)
                this_episode_short_descripton = (
                    this_episode.get("node", {}).get("synopses", {}) or {}
                )
                this_episode_short_descripton = (
                    this_episode_short_descripton.get("synopsisShort", "") or ""
                )
                match_ratio = fuzz.ratio(
                    description.lower(), this_episode_short_descripton.lower()
                )
                if match_ratio > 94:
                    # episode number matches and description is pretty close
                    logger_service.info(
                        'Found TitleAPI match based on series/episode nums. Episode name in TitleAPI: "{}"'.format(
                            this_episode_name
                        )
                    )
                    cleaned_response = {
                        "data": {
                            "searchTitles": {"totalCount": 1, "edges": [this_episode]}
                        }
                    }
                    return cleaned_response

        return {}  # still couldn't match anything

    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_run_search_for_episode_given_episode_name_and_description(
    auth_token, series_name, season_num, episode_name, description
):
    """
    Use only if Episode number not provided. Loop through everything in the season. See if the episode name matches
    If so, use that title.
    :param auth_token:
    :param series_name:
    :param season_num:
    :param episode_name:
    :param description:
    :return:
    """
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: SERIES_NAME, values: [ \""""
        + series_name
        + """\" ] }, 
          { field: SEASON_NAME, values: [ \""""
        + season_num
        + """\" ] }
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print(
                    "TitleAPI error (episode matching num/descrip): {}".format(
                        this_err["message"]
                    )
                )
                return {}

        all_episode_edges = (
            resp_json.get("data", {}).get("searchTitles", {}).get("edges", [])
        )
        for this_episode in all_episode_edges:
            this_episode_number = this_episode.get("node", {}).get(
                "firstAirRunningOrder", ""
            )
            this_episode_name = this_episode.get("node", {}).get("name", "")
            match_ratio_ep = fuzz.ratio(this_episode_name.lower(), episode_name.lower())
            if match_ratio_ep > 94:
                # 'synopses' can sometimes be None (null). So we work some magic in case they are None (and not a dict)
                this_episode_short_descripton = (
                    this_episode.get("node", {}).get("synopses", {}) or {}
                )
                this_episode_short_descripton = (
                    this_episode_short_descripton.get("synopsisShort", "") or ""
                )
                match_ratio = fuzz.ratio(
                    description.lower(), this_episode_short_descripton.lower()
                )
                if match_ratio > 94:
                    # episode number matches and description is pretty close
                    logger_service.info(
                        'Found TitleAPI match based on series/episode nums. Episode name in TitleAPI: "{}"'.format(
                            this_episode_name
                        )
                    )
                    cleaned_response = {
                        "data": {
                            "searchTitles": {"totalCount": 1, "edges": [this_episode]}
                        }
                    }
                    return cleaned_response

        return {}  # still couldn't match anything

    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_run_search_entire_damn_series_for_episode_given_episode_name(
    auth_token, series_name, episode_name
):
    """
    Oh man - we aren't finding anything. Desperate times calls for desperate measures.
    Search every episode in the series try to match episode titles.

    If so, use that title.
    :param auth_token:
    :param series_name:
    :param episode_name:
    :return:
    """

    # TODO: I'm seeing inconsistent results with TitleAPI. Could be I'm just a noob.
    #   But same query with "first:1" returns different values
    #   Given this inconsistency, not going to continue further with this function
    #
    # splitting these so we can paginate later
    graphql_header_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: SERIES_NAME, values: [ \""""
        + series_name
        + """\" ] }
        ]
      }"""
    )

    graphql_search_string = """  
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print(
                    "TitleAPI error (episode matching num/descrip): {}".format(
                        this_err["message"]
                    )
                )
                return {}

        all_episode_edges = (
            resp_json.get("data", {}).get("searchTitles", {}).get("edges", [])
        )
        for this_episode in all_episode_edges:
            this_episode_number = this_episode.get("node", {}).get(
                "firstAirRunningOrder", ""
            )
            this_episode_name = this_episode.get("node", {}).get("name", "")
            match_ratio_ep = fuzz.ratio(this_episode_name.lower(), episode_name.lower())
            if match_ratio_ep > 94:
                # 'synopses' can sometimes be None (null). So we work some magic in case they are None (and not a dict)
                this_episode_short_descripton = (
                    this_episode.get("node", {}).get("synopses", {}) or {}
                )
                this_episode_short_descripton = (
                    this_episode_short_descripton.get("synopsisShort", "") or ""
                )
                match_ratio = fuzz.ratio(
                    description.lower(), this_episode_short_descripton.lower()
                )
                if match_ratio > 94:
                    # episode number matches and description is pretty close
                    logger_service.info(
                        'Found TitleAPI match based on series/episode nums. Episode name in TitleAPI: "{}"'.format(
                            this_episode_name
                        )
                    )
                    cleaned_response = {
                        "data": {
                            "searchTitles": {"totalCount": 1, "edges": [this_episode]}
                        }
                    }
                    return cleaned_response

        return {}  # still couldn't match anything

    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_run_search_for_special(auth_token, series_name, season_num, episode_title):
    graphql_string = (
        """query{
    searchTitles(input:
      {stringFilterFields:
        [ { field: TYPE, values: [ "Special"]},
          { field: NAME, values: [ \""""
        + episode_title
        + """\" ] }
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print("TitleAPI error (episode search): {}".format(this_err["message"]))

        return resp_json
        # return {'Authorization': resp_json['token_type'] + ' ' + resp_json['access_token']}
    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_run_search_for_anything_in_season(auth_token, series_name, season_num):
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: SERIES_NAME, values: [ \""""
        + series_name
        + """\" ] }, 
          { field: SEASON_NAME, values: [ \""""
        + season_num
        + """\" ] }
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print("TitleAPI error (season search): {}".format(this_err["message"]))

        return resp_json
        # return {'Authorization': resp_json['token_type'] + ' ' + resp_json['access_token']}
    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_run_search_for_movie(auth_token, movie_name, movie_year):
    """
    Use TitleAPI to search for a movie.
    :param auth_token:
    :param movie_name:
    :param movie_year: might be empty string. Use to compare results if second point of reference needed
    :return:
    """
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: NAME, values: [ \""""
        + movie_name
        + """\" ]}, 
          { field: TYPE, values: [ "Feature" ] } 
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        }
      }
    }
  }
}"""
    )

    json_body = {"query": graphql_string, "variables": {}}

    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
    )

    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        # response could be HTTP 200 but still have errors - check for them
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                logger_service.error("TitleAPI error: {}".format(this_err["message"]))

        # remove others that don't exactly match (e.g. "My Old School" when searching for "Old School")
        # first make sure we have an exact match
        edges = resp_json.get("data", {}).get("searchTitles", {}).get("edges", [])
        edge_count = len(edges)
        exact_matches = 0
        for this_edge in edges:
            edge_node = this_edge.get("node", {})
            node_name = edge_node.get("name", "")
            if node_name == movie_name:
                exact_matches = exact_matches + 1

        if exact_matches == 1 and edge_count == 1:  # only one found and it matches
            return resp_json

        if (
            exact_matches == 1 and edge_count > 1
        ):  # there are some extraneous matches found. Let's remove the extras
            edges = [
                x for x in edges if x.get("node", {}).get("name", "") == movie_name
            ]  # create list comprehension
            resp_json["data"]["searchTitles"][
                "edges"
            ] = edges  # override edges with the list comp above
            return resp_json

        if (
            exact_matches > 1
        ):  # multiple exact matches? very odd but could happen. Can only return all for now...
            return resp_json

        # check if name starts with "The" or "A" - put them at the end and try once more
        movie_name_converted = movie_name
        if movie_name.startswith("The "):
            movie_name_converted = movie_name[4:] + ", The"
        if movie_name.startswith("A "):
            movie_name_converted = movie_name[2:] + ", A"
        if movie_name.startswith("An "):
            movie_name_converted = movie_name[3:] + ", An"

        for this_edge in edges:
            edge_node = this_edge.get("node", {})
            node_name = edge_node.get("name", "")
            if node_name == movie_name_converted:
                exact_matches = exact_matches + 1

        if exact_matches == 1 and edge_count == 1:  # only one found and it matches
            return resp_json

        if (
            exact_matches == 1 and edge_count > 1
        ):  # there are some extraneous matches found. Let's remove the extras
            edges = [
                x
                for x in edges
                if x.get("node", {}).get("name", "") == movie_name_converted
            ]  # create list comprehension
            resp_json["data"]["searchTitles"][
                "edges"
            ] = edges  # override edges with the list comp above
            return resp_json

        # fuzzy searching - if over 90% match and same release year = a match
        if (
            exact_matches == 0
        ):  # none found - let's just double check. Might be some naming issues
            for this_edge in edges:
                edge_node = this_edge.get("node", {})
                node_name = edge_node.get("name", "")
                node_uuid = edge_node.get("id", "")
                earliest_release_date = edge_node.get("earliestReleaseDate", "")
                match_ratio = fuzz.ratio(node_name.lower(), movie_name.lower())
                if match_ratio > 90:
                    if movie_year != "" and earliest_release_date != "":
                        earliest_release_date_dt = utc_string_to_datetime(
                            earliest_release_date
                        )
                        movie_release_year = earliest_release_date_dt.strftime("%Y")
                        if movie_release_year == movie_year:
                            edges = [
                                x
                                for x in edges
                                if x.get("node", {}).get("id", "") == node_uuid
                            ]  # create list comprehension
                            resp_json["data"]["searchTitles"][
                                "edges"
                            ] = edges  # override edges with the list comp
                            return resp_json

        # return {'Authorization': resp_json['token_type'] + ' ' + resp_json['access_token']}
    else:
        resp.raise_for_status()
        # raise Exception('Unable to get response from TitleAPI')


def titleapi_get_series_short_description(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] == "Feature" or this_node["type"] == "Special":
                short_synopsis = this_node["synopses"]["synopsisShort"]
                return short_synopsis

            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue
            this_node_parent = this_node["parent"]
            if this_node_parent["type"] != "Season":
                # print('TitleAPI: Parent is not Season')
                continue
            this_node_parent_parent = this_node_parent["parent"]
            if this_node_parent_parent["type"] != "Series":
                # print('TitleAPI: Parent-parent is not Series')
                continue

            return this_node_parent_parent["synopses"]["synopsisShort"]
    except:
        logger_service.error("Unable to get Series Short Description from TitleAPI")
        return ""


def titleapi_get_series_long_description(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] == "Feature" or this_node["type"] == "Special":
                med_synopsis = this_node["synopses"]["synopsisMedium"]
                return med_synopsis

            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue
            this_node_parent = this_node["parent"]
            if this_node_parent["type"] != "Season":
                # print('TitleAPI: Parent is not Season')
                continue
            this_node_parent_parent = this_node_parent["parent"]
            if this_node_parent_parent["type"] != "Series":
                # print('TitleAPI: Parent-parent is not Series')
                continue
            return this_node_parent_parent["synopses"]["synopsisMedium"]
    except:
        logger_service.error("Unable to get Series Long Description from TitleAPI")
        return ""


def titleapi_get_season_short_description(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue
            this_node_parent = this_node["parent"]
            if this_node_parent["type"] != "Season":
                # print('TitleAPI: Parent is not Season')
                continue

            if this_node_parent["synopses"]["synopsisShort"] is None:
                return ""

            return this_node_parent["synopses"]["synopsisShort"]
    except:
        logger_service.error("Unable to get Series Short Description from TitleAPI")

    return ""


def titleapi_get_season_long_description(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue
            this_node_parent = this_node["parent"]
            if this_node_parent["type"] != "Season":
                # print('TitleAPI: Parent is not Season')
                continue

            if this_node_parent["synopses"]["synopsisMedium"] is None:
                return ""

            return this_node_parent["synopses"]["synopsisMedium"]
    except:
        logger_service.error("Unable to get Series Long Description from TitleAPI")

    return ""


def titleapi_get_episode_short_description(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue

            return this_node["synopses"]["synopsisShort"]
    except:
        logger_service.error("Unable to get Episode Short Description from TitleAPI")

    return ""


def titleapi_get_episode_long_description(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue

            return this_node["synopses"]["synopsisMedium"]
    except:
        logger_service.error("Unable to get Episode Long Description from TitleAPI")

    return ""


def titleapi_get_series_start_year(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue
            this_node_parent = this_node["parent"]
            if this_node_parent["type"] != "Season":
                # print('TitleAPI: Parent is not Season')
                continue
            this_node_parent_parent = this_node_parent["parent"]
            if this_node_parent_parent["type"] != "Series":
                # print('TitleAPI: Parent-parent is not Series')
                continue

            series_start_raw = this_node_parent_parent["earliestReleaseDate"]
            # series_start_clean = series_start_raw.replace('Z', 'UTC')
            # series_start_dt = datetime.strptime(series_start_clean, '%Y-%m-%dT%H:%M:%S%Z')
            series_start_dt = utc_string_to_datetime(series_start_raw)
            return series_start_dt.strftime("%Y")
    except:
        logger_service.error("Unable to get Series Start Year from TitleAPI")
        return ""


def titleapi_get_movie_year(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Feature":
                continue

            movie_start_raw = this_node["earliestReleaseDate"]
            series_start_dt = utc_string_to_datetime(movie_start_raw)
            return series_start_dt.strftime("%Y")
    except:
        logger_service.error("Unable to get Movie Year from TitleAPI")
        return ""


def titleapi_get_special_year(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Special":
                continue

            movie_start_raw = this_node["earliestReleaseDate"]
            series_start_dt = utc_string_to_datetime(movie_start_raw)
            return series_start_dt.strftime("%Y")
    except:
        logger_service.error("Unable to get Special Year from TitleAPI")
        return ""


def titleapi_get_genre_list(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    result_string = []
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            this_node_genres = this_node.get("genres", [])
            if len(this_node_genres) > 0:
                return this_node_genres
    except:
        logger_service.error("Unable to get genre list from TitleAPI")
        return []


def titleapi_get_episode_production_number(titleapi_result_json, network):
    """
    Check the TitleAPI results and extract certain data
    :param network: name of the network. will determine production number length.
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GraphQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue

            temp_prod_num = this_node.get("productionNumber", None)
            if temp_prod_num is not None and temp_prod_num.lower() != "null":
                return temp_prod_num

            # no prod num. Make it from season and episode num.
            temp_season_num = str(this_node.get("seasonNumber", ""))
            temp_episode_num = str(this_node.get("seasonEpisodeNumber", ""))
            logger_service.info(
                "No productionNumber from TitleAPI. Making one from season/episode"
            )
            logger_service.info(
                "Network={}   Season={}   Episode={}".format(
                    network, temp_season_num, temp_episode_num
                )
            )

            if (
                network.lower() != "freeform" and network.lower() != "ff"
            ):  # DATG has 3/4 digit prod num (SEE or SSEE)
                if temp_episode_num != "":
                    temp_episode_num = temp_episode_num.zfill(2)
                    temp_prod_num = temp_season_num + temp_episode_num
                    return temp_prod_num
                else:
                    return ""

            else:  # Freeform has 4 digit production numbers (SEEE or SSEE)
                if temp_episode_num != "":
                    if len(temp_season_num) == 1:
                        temp_episode_num = temp_episode_num.zfill(3)
                        temp_prod_num = temp_season_num + temp_episode_num
                        return temp_prod_num
                    elif len(temp_season_num) == 2:
                        temp_episode_num = temp_episode_num.zfill(2)
                        temp_prod_num = temp_season_num + temp_episode_num
                        return temp_prod_num
                else:
                    return ""

    except:
        logger_service.error("Unable to get Episode Production Number from TitleAPI")
        return ""


def titleapi_get_episode_number(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GraphQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue

            # return this_node['episodeNumber']  # don't use this. episodeNumber is cumulative over all seasons.
            # return this_node['seasonEpisodeNumber']  # don't use this. seasonEpisodeNumber is incorrect. use firstAirRunningOrder
            first_air_running_order = this_node["firstAirRunningOrder"]
            return str(first_air_running_order)
    except:
        logger_service.error("Unable to get Season Episode Number from TitleAPI")
        return ""


def titleapi_get_radar_group_id(
    program_type, episode_title, content_title, titleapi_result_json
):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if program_type == "movie" and this_node["type"] == "Feature":
                groupId = this_node["productGroupId"]
                return groupId

            if (
                program_type == "special" or program_type == "special c type"
            ) and this_node["type"] == "Special":
                groupId = this_node["productGroupId"]
                return groupId

            # if this_node['type'] != 'Episode':
            #     # print('TitleAPI: Child is not Episode')
            #     continue
            if (
                program_type == "episode" or program_type == "episode c type"
            ) and this_node["type"] == "Episode":
                this_node_parent = this_node["parent"]

                if this_node_parent["type"] != "Season":
                    # print('TitleAPI: Parent is not Season')
                    continue
                this_node_parent_parent = this_node_parent["parent"]
                if this_node_parent_parent["type"] != "Series":
                    # print('TitleAPI: Parent-parent is not Series')
                    continue

                if this_node_parent_parent["productGroupId"] is None:
                    return ""
                return this_node_parent_parent["productGroupId"]
    except:
        logger_service.error(
            "Unable to get Series product Group ID (Radar) from TitleAPI"
        )
        return ""

    return ""


def titleapi_get_series_keywords(titleapi_result_json):
    """
    Check the TitleAPI results and extract certain data
    :param titleapi_result_json: this should be a json (dict) object
    :return:
    """

    # This is assuming the GraphQL query was for a specific episode.
    # Then the parent would be the season, and its grandparent would be series
    result_string = ""
    try:
        for this_edge in titleapi_result_json["edges"]:
            this_node = this_edge["node"]
            if this_node["type"] == "Feature":
                keywords_raw = this_node["keywords"]
                keywords_str = ", ".join(keywords_raw)
                return keywords_str

            if this_node["type"] != "Episode":
                # print('TitleAPI: Child is not Episode')
                continue
            this_node_parent = this_node["parent"]
            if this_node_parent["type"] != "Season":
                # print('TitleAPI: Parent is not Season')
                continue
            this_node_parent_parent = this_node_parent["parent"]
            if this_node_parent_parent["type"] != "Series":
                # print('TitleAPI: Parent-parent is not Series')
                continue

            # keywords might be empty at series level. If so, try to get them at season and episode level.
            keywords_raw = this_node_parent_parent["keywords"]
            if len(keywords_raw) == 0:  # trying parent (Season)
                try:
                    keywords_raw = this_node_parent["keywords"]
                except KeyError:
                    keywords_raw = []

            if len(keywords_raw) == 0:  # trying episode
                try:
                    keywords_raw = this_node["keywords"]
                except KeyError:
                    keywords_raw = []

            keywords_str = ", ".join(keywords_raw)
            # series_start_clean = series_start_raw.replace('Z', 'UTC')
            # series_start_dt = datetime.strptime(series_start_clean, '%Y-%m-%dT%H:%M:%S%Z')
            # series_start_dt = utc_string_to_datetime(series_start_raw)
            return keywords_str
    except:
        logger_service.error("Unable to get Series Keywords from TitleAPI")
        return ""


def get_matching_section_name(section_name, template_data):
    """
    Check if section name is part of human_description
    :param section_name:
    :param template_data:
    :return:
    """
    template_keys = template_data.keys()
    for this_key in template_keys:
        this_key_description = template_data[this_key]["human_description"]
        if type(this_key_description) == list:
            if any(
                single_description in section_name.strip()
                for single_description in this_key_description
            ):
                return this_key
        else:
            if this_key_description in section_name.strip():
                return this_key

    return ""


def get_sd_mapping_from_hd_mapping(hd_mapping_list):
    # Example: lvl 1 = "TV Shows/By Network/FX/A Wilderness HD"
    #          lvl 2 = "TV Shows/Shows T-Z HD/A Wilderness"
    #
    # SD should be:
    #          lvl 1 = "TV Shows/By Network/FX/A Wilderness""
    #          lvl 2 = "TV Shows/Shows T-Z/A Wilderness"
    sd_mapping_list = []
    for this_hd_mapping in hd_mapping_list:
        if this_hd_mapping[-3:] == " HD":
            this_hd_mapping = this_hd_mapping[:-3]

        if this_hd_mapping[-5:] == " (HD)":
            this_hd_mapping = this_hd_mapping[:-5]

        if " HD/" in this_hd_mapping:
            this_hd_mapping = this_hd_mapping.replace(" HD/", "/")

        sd_mapping_list.append(this_hd_mapping)

    return sd_mapping_list


def get_reach_genre_from_genre_list(json_root):
    """
    From JSON, get a list of genres, find the first match of Reach presets and return it.
    This is because the genres from MediaHub can be multiple values and we need to find the exact match for Reach.
    :param json_root:
    :return:
    """
    reach_genre_list = [
        "Animation",
        "Award Show",
        "Comedy",
        "Documentary",
        "Drama",
        "Horror",
        "Kids and Family",
        "Music",
        "News",
        "Reality and Game Show",
        "Sports",
        "Talk Show",
    ]

    genre_list = json_root.get("Genres", [])
    if genre_list is None:
        return ""
    # if not genre_list:
    #     raise Exception('No genre in JSON')

    if type(genre_list) == str:
        genre_list = [genre_list]

    for this_given_genre in genre_list:
        for this_reach_genre in reach_genre_list:
            if this_given_genre.strip().lower() == this_reach_genre.strip().lower():
                return this_reach_genre

    # catchall
    # logger_service.info('Unable to find matching genre in Reach. Given genre from JSON: {}'.format('|'.join(genre_list)))
    # logger_service.info('Using "Drama" because we need something')
    # return 'Drama'
    return ""


def get_reach_genre_given_list_of_potential_genres(genre_list):
    """
    From JSON, get a list of genres, find the first match of Reach presets and return it.
    This is because the genres from MediaHub can be multiple values and we need to find the exact match for Reach.
    :param json_root:
    :return:
    """
    reach_genre_list = [
        "Animation",
        "Award Show",
        "Comedy",
        "Documentary",
        "Drama",
        "Horror",
        "Kids and Family",
        "Music",
        "News",
        "Reality and Game Show",
        "Sports",
        "Talk Show",
    ]

    if type(genre_list) == str:
        genre_list = [genre_list]

    if genre_list is None:
        return ""

    for this_given_genre in genre_list:
        for this_reach_genre in reach_genre_list:
            if this_given_genre.strip().lower() == this_reach_genre.strip().lower():
                return this_reach_genre

    return ""


def xml_test_get_content_title(adi_root):
    """
    test XML for filelist
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    series_title = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Series_Name"]')
    if len(series_title) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        # raise Exception('No Series Title in XML')
        series_title = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Title"]')
        if len(series_title) == 1:
            return series_title[0].attrib["Value"]
        else:
            raise Exception("No Series Title in XML")

    if len(series_title) > 1:
        raise Exception("Multiple Series Titles in XML")

    return series_title[0].attrib["Value"]


def xml_test_get_cms_folder_name(adi_root):
    """
    test XML for series name - then convert to alpha only, no spaces or characters
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    series_title = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Title"]')
    if len(series_title) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Series Title in XML")

    if len(series_title) > 1:
        raise Exception("Multiple Series Titles in XML")

    allowed_chars = string.ascii_letters + string.digits + "-"
    series_title_string = series_title[0].attrib["Value"]
    series_cleaned_for_gopub_folder = re.sub(
        "[^{}]".format(allowed_chars), "", series_title_string
    )
    # print('series_cleaned_for_gopub_folder={}'.format(series_cleaned_for_gopub_folder))
    # return re.sub('[^{}]'.format(allowed_chars), '', series_title)
    return series_cleaned_for_gopub_folder


def xml_test_get_season_num(adi_root):
    """
    test XML for filelist
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    season_num = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Series_Number"]')
    if len(season_num) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Season in XML")

    if len(season_num) > 1:
        raise Exception("Multiple Season in XML")

    return season_num[0].attrib["Value"]


def xml_test_get_episode_title(adi_root):
    """
    test XML for filelist
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    episode_name = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Episode_Name"]')
    if len(episode_name) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        # raise Exception('No Episode Name in XML')
        return ""

    if len(episode_name) > 1:
        raise Exception("Multiple Episode Name in XML")

    return episode_name[0].attrib["Value"]


def xml_test_get_video_asset_id(adi_root):
    """
    test XML for filelist
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    # episode_name = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Episode_Name"]')
    video_asset_id = adi_root.findall(
        './/Asset/Asset/Metadata/AMS[@Asset_Class = "movie"]'
    )
    if len(video_asset_id) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        # raise Exception('No Episode Name in XML')
        return ""

    if len(video_asset_id) > 1:
        raise Exception("Multiple movie Asset_ID in XML")

    return video_asset_id[0].attrib["Asset_ID"]


def xml_test_get_ad_content_id(adi_root):
    """
    test XML for filelist
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    # episode_name = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Episode_Name"]')
    ad_content_id = adi_root.findall(
        './/Asset/Metadata/App_Data[@Name="Ad_Content_ID"]'
    )
    if len(ad_content_id) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        # raise Exception('No Episode Name in XML')
        return ""

    if len(ad_content_id) > 1:
        raise Exception("Multiple Ad_Content_ID in XML")

    return ad_content_id[0].attrib["Value"]


def xml_test_get_network(adi_root):
    """
    test XML for network
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    network = adi_root.findall("./Metadata/AMS")
    if len(network) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Network in XML")

    if len(network) > 1:
        raise Exception("Multiple Network in XML")

    temp_network_value = network[0].attrib["Provider"]
    if temp_network_value == "FX_AUTH":
        return "FX"
    elif temp_network_value == "FXX_AUTH":
        return "FXX"
    elif temp_network_value == "FXM_AUTH":
        return "FXM"
    elif temp_network_value == "NATIONAL GEOGRAPHIC":
        return "National Geographic"
    elif temp_network_value == "NAT GEO WILD":
        return "Nat Geo Wild"

    print("New network? {}".format(temp_network_value))
    return temp_network_value


def xml_test_get_genre(adi_root):
    """
    test XML for genre
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    genre = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Genre"]')
    if len(genre) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Genre in XML")

    if len(genre) > 1:
        genre_list = []
        for this_genre in genre:
            genre_list.append(this_genre.attrib["Value"])
        return genre_list

    return genre[0].attrib["Value"]


def xml_test_get_rating(adi_root):
    """
    test XML for rating
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    rating = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Rating"]')
    if len(rating) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Rating in XML")

    if len(rating) > 1:
        raise Exception("Multiple Rating in XML")

    return rating[0].attrib["Value"]


def xml_test_get_program_type(adi_root):
    """
    test XML for program type (movie or series)
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    program_type = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Program_Type"]')
    if len(program_type) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Program_Type in XML")

    if len(program_type) > 1:
        raise Exception("Multiple Program_Type in XML")

    return program_type[0].attrib["Value"]


def xml_test_get_tms_id(adi_root):
    """
    test XML for tms id if feature
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    tms_id = adi_root.findall('.//Asset/Metadata/App_Data[@Name="TMS_ID"]')
    if len(tms_id) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No TMS_ID in XML")

    if len(tms_id) > 1:
        raise Exception("Multiple TMS_ID in XML")

    return tms_id[0].attrib["Value"]


def xml_test_get_category(adi_root):
    """
    test XML for genre
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    ams = adi_root.findall(".//Asset/Metadata/AMS")
    win_start = adi_root.findall(
        './/Asset/Metadata/App_Data[@Name="Licensing_Window_Start"]'
    )
    win_end = adi_root.findall(
        './/Asset/Metadata/App_Data[@Name="Licensing_Window_End"]'
    )
    if len(ams) != 1 and len(win_start) != 1 and len(win_end) != 1:
        raise Exception(
            "Missing data in XML trying to determine category type (based on dates)"
        )

    win_start_dt = utc_string_to_datetime(win_start[0])
    win_end_dt = utc_string_to_datetime(win_end[0])
    diff = win_end_dt - win_start_dt

    if diff.days < 5 and diff.days > 0 and "_C3_" in ams[0]:
        return "C3"

    if diff.days < 9 and diff.days > 0 and "_C7_" in ams[0]:
        return "C7"

    if "_C3_" not in ams[0]:
        return "Episode"

    return ""


def xml_test_get_HD_mappings(adi_root):
    """
    test XML for HD mappings (will return two values: lvl 1 and lvl 2)
    :param adi_root:
    :return:
    """
    # global exit_code
    exit_code = 0
    mapping_list = adi_root.findall('.//Asset/Metadata/App_Data[@Name="Category"]')
    if len(mapping_list) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Category in XML")

    # lvl1 = ''
    # lvl2 = ''
    hd_maps = []
    for this_mapping in mapping_list:
        this_mapping_value = this_mapping.attrib["Value"]
        hd_maps.append(this_mapping_value)
        # if 'By Network' in this_mapping_value:
        #     lvl1 = this_mapping_value
        # else:
        #     lvl2 = this_mapping_value

    # return lvl1, lvl2
    return hd_maps


def xml_add_new_metadata(adi_root, adi_json):
    """
    add new data to XML
    :param adi_json:
    :param adi_root:
    :return:
    """
    program_type = adi_root.get("program_type", {}).get("value")

    addl_element = SubElement(adi_root, "AdditionalMetadata")
    # add these titles to the XML
    actor_list = adi_json.get("actors", {}).get("value")
    for this_actor in actor_list:
        actor_full = this_actor.get("first", "") + " " + this_actor.get("last", "")
        actor_element = SubElement(addl_element, "actor")
        actor_element.text = actor_full

    short_description_element = SubElement(addl_element, "episodeShortDescription")
    long_description_element = SubElement(addl_element, "episodeLongDescription")
    content_short_description_element = SubElement(
        addl_element, "contentShortDescription"
    )
    content_long_description_element = SubElement(
        addl_element, "contentLongDescription"
    )

    short_description_element.text = adi_json.get("episode_short_synopsis", {}).get(
        "value"
    )
    long_description_element.text = adi_json.get("episode_long_synopsis", {}).get(
        "value"
    )

    content_short_description_element.text = adi_json.get(
        "content_short_synopsis", {}
    ).get("value")
    content_long_description_element.text = adi_json.get(
        "content_long_synopsis", {}
    ).get("value")

    keyword_element = SubElement(addl_element, "keywords")
    keyword_element.text = adi_json.get("keywords", {}).get("value")

    episode_prod_num_element = SubElement(addl_element, "episodeProductionNumber")
    episode_prod_num_element.text = adi_json.get("episode_production_number", {}).get(
        "value"
    )

    # tms_element = SubElement(addl_element, 'tmsId')
    # tms_element.text = 'this_will_come_later'  # not needed -provided from MediaHub

    return adi_root


def json_test_is_movie_in_cts_mappings(json_root):
    """
    test JSON for CTS mappings
    :param json_root:
    :return:
    """

    temp_categories = json_root.get("Categories", [])
    if len(temp_categories) == 0 or type(temp_categories) != list:
        logger_service.error("No Categories in JSON")
        return ""

    if len(temp_categories) > 1:
        logger_service.error("Multiple Categories in JSON")
        return ""

    temp = temp_categories[0]
    cts_list = temp.get("CTS", [])
    is_movie = False
    for this_cts in cts_list:
        if "Movies" in this_cts:
            is_movie = True

    return is_movie


def json_test_get_content_title(json_root):
    """
    test JSON for content title
    :param json_root:
    :return:
    """

    temp = json_root.get("ShowName", "")
    if temp == "" or temp is None:
        # might be a movie
        tms_id = json_root.get("TmsId", "").strip()
        network_name = json_root.get("Network", "")
        material_id = json_root.get("MaterialId", "")
        if (
            tms_id.startswith("MV")
            or network_name.lower().strip() == "fxm"
            or str(material_id).startswith("VOD")
            or str(material_id).startswith("XMX")
        ):
            title_full_temp = json_root.get("TitleFullSd", "")
            name_temp = json_root.get("Name", "")
            if len(name_temp) >= len(title_full_temp):
                temp = name_temp
            elif len(title_full_temp) > len(name_temp):
                temp = title_full_temp
            elif name_temp == "" and title_full_temp == "":
                logger_service.error("No Movie Title in JSON")
        else:
            logger_service.error("No Series Title in JSON")
            raise Exception("No Series Title in JSON")

    # replace weird/fancy characters
    temp = cleanup(temp)

    return temp


def json_test_get_cms_folder_name(json_root):
    """
    test JSON for series name - then convert to alphanumeric only, no spaces or characters (except hyphen)
    :param json_root:
    :return:
    """
    program_type = json_test_get_program_type(json_root)
    if program_type.lower() == "movie":
        temp = json_root.get("Name", False)
        if not temp:
            raise Exception("No Name in JSON")
    elif program_type.lower() == "series":
        temp = json_root.get("ShowName", False)
        if not temp:
            logger_service.error("No ShowName in JSON")
            return ""

    series_title = temp

    if type(temp) == list:
        raise Exception(
            "Unexpected data format for Name. Expected string, received list"
        )

    if len(series_title) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Series Name in JSON")

    allowed_chars = string.ascii_letters + string.digits + "-"
    # series_title_string = series_title[0].attrib['Value']
    series_cleaned_for_gopub_folder = re.sub(
        "[^{}]".format(allowed_chars), "", series_title
    )
    # print('series_cleaned_for_gopub_folder={}'.format(series_cleaned_for_gopub_folder))
    # return re.sub('[^{}]'.format(allowed_chars), '', series_title)
    return series_cleaned_for_gopub_folder


def json_test_get_material_id(json_root):
    """
    test JSON for MaterialId
    :param json_root:
    :return:
    """

    temp = json_root.get("MaterialId", "")

    return temp


def json_test_get_season_num(json_root):
    """
    test JSON for season num
    :param json_root:
    :return:
    """

    temp = json_root.get("SeasonNumber", False)
    if not temp:
        raise Exception("No SeasonNumber in JSON")

    if type(temp) == list:
        raise Exception(
            "Unexpected data format for SeasonNumber. Expected int, received list"
        )

    season_num = str(temp)

    if len(season_num) == 0:
        raise Exception("No Season in JSON")

    return season_num


def json_test_get_episode_number(json_root):
    """
    test JSON for episode title
    :param json_root:
    :return:
    """

    temp = json_root.get("EpisodeNumberInSeason", "")
    if temp == "":
        logger_service.error("No episode number in JSON")
        return ""
        # raise Exception('No episode number in JSON')

    episode_num = temp
    if type(episode_num) == list:
        raise Exception(
            "Unexpected data format for EpisodeNumberInSeason. Expected string, received list"
        )

    # if len(episode_num) == 0:
    #     # report.append('ERROR: No files in /Root/FileList/File')
    #     # show_stoppers.append('ERROR: No files in /Root/FileList/File')
    #     # return [], 1
    #     raise Exception('No Episode number in JSON')

    return episode_num


def json_test_get_episode_short_description(json_root):
    """
    test JSON for episode title
    :param json_root:
    :return:
    """

    temp = json_root.get("SummaryShort", False)
    if not temp:
        logger_service.error("No SummaryShort in JSON")
        return ""
        # raise Exception('No episode number in JSON')

    episode_short_description = temp
    if type(episode_short_description) == list:
        raise Exception(
            "Unexpected data format for SummaryShort. Expected string, received list"
        )

    # if len(episode_num) == 0:
    #     # report.append('ERROR: No files in /Root/FileList/File')
    #     # show_stoppers.append('ERROR: No files in /Root/FileList/File')
    #     # return [], 1
    #     raise Exception('No Episode number in JSON')
    episode_short_description = cleanup(episode_short_description)

    return episode_short_description


def json_test_get_episode_title(json_root):
    """
    test JSON for episode title
    :param json_root:
    :return:
    """

    temp = json_root.get("Name", False)
    if not temp:
        raise Exception("No episode name in JSON")

    episode_name = temp
    if type(episode_name) == list:
        raise Exception(
            "Unexpected data format for Name. Expected string, received list"
        )

    if len(episode_name) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Episode Name in JSON")

    # remove special characters
    episode_name = cleanup(episode_name)

    return episode_name


def json_test_get_network(json_root):
    """
    test JSON for network
    :param json_root:
    :return:
    """

    temp = json_root.get("Network", False)
    if not temp:
        raise Exception("No network in JSON")

    network = temp
    if type(network) == list:
        raise Exception(
            "Unexpected data format for Network. Expected string, received list"
        )

    if len(network) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        raise Exception("No Network in JSON")

    return network


def json_test_get_showcode(json_root):
    """
    test JSON for showcode - for Series (won't work with Movies)
    Should limit return to 3 characters
    :param json_root:
    :return:
    """
    material_id = json_root.get("MaterialId", "")
    if material_id == "":
        logger_service.error("No MaterialId in JSON")
        return ""

    match = re.match("^[a-zA-Z]{,4}", material_id)
    if match == None:
        logger_service.error(
            'Couldn\'t extract prefix from MaterialID. Value provided was "{}"'.format(
                material_id
            )
        )
        return ""

    prefix_string = match.group(0)

    return prefix_string


def json_test_get_showcode_movie(json_root):
    """
    test JSON for showcode - for Movies
    :param json_root:
    :return:
    """
    # FX/NG movies should just have the network + 'Movie' as prefix
    network: str = json_root.get("Network", "")

    # old way when we only wanted the network as prefix
    # if 'fx' in network.lower() or ('nat' in network.lower() and 'geo' in network.lower()):
    #     return network.upper() + 'Movie'

    if "fx" in network.lower():
        return network.upper() + "Movie"

    if "ngc" in network.lower():
        return "NGMovie"

    if "ngw" in network.lower():
        return "NGWMovie"

    temp = json_root.get("MaterialId", False)
    if not temp:
        raise Exception("No MaterialId in JSON")

    showcode = temp
    if type(showcode) == list:
        raise Exception(
            "Unexpected data format for MaterialId. Expected string, received list"
        )

    if len(showcode) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        # raise Exception('No SeriesId in JSON') # might be missing for movies. don't throw exception
        return ""

    return showcode


def json_test_get_showcode_special(json_root):
    """
    test JSON for showcode - for Specials
    :param json_root:
    :return:
    """
    # FX/NG movies should just have the network + 'Movie' as prefix
    network: str = json_root.get("Network", "")

    # old way when we only wanted the network as prefix
    # if 'fx' in network.lower() or ('nat' in network.lower() and 'geo' in network.lower()):
    #     return network.upper() + 'Movie'

    if "fx" in network.lower():
        return network.upper() + "Special"

    if "ngc" in network.lower():
        return "NGSpecial"

    if "ngw" in network.lower():
        return "NGWSpecial"

    return showcode


def json_test_get_genre(json_root):
    """
    test XML for genre
    :param json_root:
    :return:
    """

    genres = json_root.get("Genres", [])
    # if not temp:
    #     raise Exception('No genre in JSON')

    # genres = temp
    genre_str = ""
    # if type(genres) == str:  # if string - single value in JSON
    if type(genres) == str and len(genres) == 0:
        # report.append('ERROR: No files in /Root/FileList/File')
        # show_stoppers.append('ERROR: No files in /Root/FileList/File')
        # return [], 1
        # raise Exception('No Genre in JSON')
        logger_service.error("No Genre in JSON")

    if type(genres) == str and len(genres) > 0:
        genre_str = genres

    if type(genres) == list and len(genres) > 0:
        for this_genre in genres:
            if len(this_genre) > 0:
                genre_str = this_genre
                break
    else:
        genre_str = ""

    return genre_str


def json_test_get_rating(json_root):
    """
    test JSON for rating
    :param json_root:
    :return:
    """
    temp = json_root.get("Rating", False)
    if not temp:
        raise Exception("No Rating in JSON")

    # rating = temp

    return temp


def json_test_get_airdate(json_root):
    """
    test JSON for airdate
    :param json_root:
    :return:
    """
    temp = json_root.get("AirDate", False)
    if not temp:
        raise Exception("No AirDate in JSON")

    return temp


def json_test_get_year(json_root):
    """
    test JSON for year
    :param json_root:
    :return:
    """
    temp = json_root.get("Year", "")
    if temp == "":
        logger_service.info("No Year in JSON")
        return ""

    return temp


def json_test_get_rating_content_labels(json_root):
    """
    test JSON for rating content labels (violence, language, etc)
    :param json_root:
    :return:
    """
    temp = json_root.get("ContentLabels", False)
    if not temp:
        raise Exception("No ContentLabels in JSON")

    content_labels = temp
    # NOTE: default values in Reach:
    # [
    #     {
    #         "id": "200",
    #         "type": "TV_US",
    #         "name": "Dialog"
    #     },
    #     {
    #         "id": "220",
    #         "type": "TV_US",
    #         "name": "Language"
    #     },
    #     {
    #         "id": "240",
    #         "type": "TV_US",
    #         "name": "Sexual Content"
    #     },
    #     {
    #         "id": "260",
    #         "type": "TV_US",
    #         "name": "Violence"
    #     }
    # ]
    content_label_list = []
    # check label - if it is L, SC, V, D add to list
    if type(content_labels) == list:
        for this_label in content_labels:
            this_label_value = this_label.get("Label", "")
            if (
                this_label_value.lower() == "language"
                or this_label_value.lower() == "sexual content"
                or this_label_value.lower() == "violence"
                or this_label_value.lower() == "dialog"
            ):
                content_label_list.append(this_label_value)

    return content_label_list


def json_test_is_media_4k(json_root):
    """
    test JSON for program type if it is 4K
    :param json_root:
    :return:
    """

    temp = json_root.get(
        "ShowType", ""
    )  # it's possible this is missing - work around it

    if len(temp) == 0:
        # there's no ShowType in JSON - might be a movie
        logger_service.error("No ShowType in JSON.")
        # raise Exception('No ShowType in JSON')

    if len(temp) > 1 and type(temp) == list:
        raise Exception("Multiple ShowType in JSON")

    if temp == "4K" or temp == "4k":
        return True
    else:
        return False


def json_test_get_program_type(json_root):
    """
    test JSON for program type (movie or series)
    :param json_root:
    :return:
    """

    # if FXM, automatically make it movie
    temp = json_root.get("Network", "")
    if temp.lower().strip() == "fxm":
        return "movie"

    temp = json_root.get(
        "ShowType", ""
    )  # it's possible this is missing - work around it
    tms_id = json_root.get("TmsId", "").strip()
    if len(tms_id) > 0:
        if tms_id.startswith("MV"):
            return "movie"

    if len(temp) == 0:
        # there's no ShowType in JSON - might be a movie
        logger_service.error("No ShowType in JSON.")
        # raise Exception('No ShowType in JSON')

    if len(temp) > 1 and type(temp) == list:
        raise Exception("Multiple ShowType in JSON")

    if temp == "4K":
        if len(tms_id) > 0:
            if tms_id.startswith("MV"):
                return "movie"
            elif tms_id.startswith("EP"):
                return "series"
        else:
            # lol - just assume it's series at this point - can't do much else. If it's wrong and there's a 4K movie...
            return "series"

    if (
        temp == "Acquired"
    ):  # "acquired" content (means there's no C-window content. This is Clean only on Day 1)
        # - check TMS ID to make determination
        if len(tms_id) > 0:
            if tms_id.startswith("MV"):
                return "movie"
            # elif tms_id.startswith('EP'):
            else:
                return "series"

    if temp.lower() == "one off":
        return "special"

    return temp.lower()


def json_test_get_acquired(json_root):
    """
    test JSON for acquired
    :param json_root:
    :return:
    """

    temp = json_root.get("ShowType", "")

    if (
        temp == "Acquired"
    ):  # "acquired" content (means there's no C-window content. This is Clean only on Day 1)
        return True
    else:
        return False


def json_test_get_nld_type(json_root):
    """
    test JSON for program type (C3 or Clean or VOD or VOD4)
    :param json_root:
    :return:
    """

    temp = json_root.get("NLDTypes", [])
    if len(temp) == 0 or type(temp) != list:
        raise Exception("No NLDTypes in JSON")

    if len(temp) > 1:
        raise Exception("Multiple NLDTypes in JSON")

    first_nld = temp[0]
    nld_type = first_nld.get("Type", "")
    return nld_type


def json_test_get_nld_window_dates(json_root):
    """
    get start and end dates for window
    :param json_root:
    :return: dict of start_win, end_win
    """

    temp = json_root.get("NLDTypes", [])
    if len(temp) == 0 or type(temp) != list:
        raise Exception("No NLDTypes in JSON")

    if len(temp) > 1:
        raise Exception("Multiple NLDTypes in JSON")

    first_nld = temp[0]
    start_window_str = first_nld.get("StartDate")
    end_window_str = first_nld.get("EndDate")
    airdate_str = json_root.get("AirDate", "")

    # nld_type = first_nld.get('Type')
    utc_zone = tz.gettz("UTC")
    # pac_zone = tz.gettz('America/Los_Angeles')

    three_hours = timedelta(hours=3)
    # parse strings into datetimes - convert UTC to Pacific
    airdate_str_clean = airdate_str.replace(
        "Z", "UTC"
    )  # convert the Z to UTC for cleaner parsing
    airdate_dt = datetime.strptime(airdate_str_clean, "%Y-%m-%dT%H:%M:%S%Z")
    airdate_dt = (
        airdate_dt + three_hours
    )  # shifting times 3 hours because MediaHub is Eastern, Reach is Pacific
    airdate_dt = airdate_dt.replace(tzinfo=utc_zone)

    start_window_str_clean = start_window_str.replace(
        "Z", "UTC"
    )  # convert the Z to UTC for cleaner parsing
    start_window_dt = datetime.strptime(start_window_str_clean, "%Y-%m-%dT%H:%M:%S%Z")
    start_window_dt = start_window_dt + three_hours
    start_window_dt = start_window_dt.replace(tzinfo=utc_zone)
    # start_window_dt_pac = start_window_dt.astimezone(pac_zone)

    # make sure start date is NOT before air date
    if airdate_dt > start_window_dt:
        start_window_dt = airdate_dt

    end_window_str_clean = end_window_str.replace(
        "Z", "UTC"
    )  # convert the Z to UTC for cleaner parsing
    end_window_dt = datetime.strptime(end_window_str_clean, "%Y-%m-%dT%H:%M:%S%Z")
    end_window_dt = end_window_dt + three_hours
    end_window_dt = end_window_dt.replace(tzinfo=utc_zone)
    # end_window_dt_pac = end_window_dt.astimezone(pac_zone)

    # final output should look like:   2024-02-17T11:00:00.000+0000 which is UTC time
    # strftime microseconds is 6 digits. So we need to lop off the extra 3 digits
    start_window_first = start_window_dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]
    start_tz = start_window_dt.strftime("%z")
    start_window_utc_string = start_window_first + start_tz

    end_window_first = end_window_dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]
    end_tz = end_window_dt.strftime("%z")
    end_window_utc_string = end_window_first + end_tz

    output = {
        "start_window": start_window_utc_string,
        "end_window": end_window_utc_string,
    }

    return output


def json_test_get_bankable_date(json_root):
    """
    get bankable date
    :param json_root:
    :return: string of cleaned date
    """

    dates_bankable = json_root.get("Dates", {}).get("Bankable", "")

    if dates_bankable == "" or dates_bankable is None:
        return ""

    # if len(temp) == 0 or type(temp) != list:
    #     raise Exception('No NLDTypes in JSON')
    #
    # if len(temp) > 1:
    #     raise Exception('Multiple NLDTypes in JSON')
    #
    # first_nld = temp[0]
    # start_window_str = first_nld.get('StartDate')
    # end_window_str = first_nld.get('EndDate')
    # airdate_str = json_root.get('AirDate', '')

    # nld_type = first_nld.get('Type')
    utc_zone = tz.gettz("UTC")
    # pac_zone = tz.gettz('America/Los_Angeles')

    # parse strings into datetimes - convert UTC to Pacific
    three_hours = timedelta(hours=3)
    dates_bankable_clean = dates_bankable.replace(
        "Z", "UTC"
    )  # convert the Z to UTC for cleaner parsing
    date_bankable_dt = datetime.strptime(dates_bankable_clean, "%Y-%m-%dT%H:%M:%S%Z")
    date_bankable_dt = (
        date_bankable_dt + three_hours
    )  # shifting times 3 hours because MediaHub is Eastern, Reach is Pacific
    date_bankable_dt = date_bankable_dt.replace(tzinfo=utc_zone)

    # final output should look like:   2024-02-17T11:00:00.000+0000 which is UTC time
    # strftime microseconds is 6 digits. So we need to lop off the extra 3 digits
    bankable_first = date_bankable_dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]
    bankable_tz = date_bankable_dt.strftime("%z")  # should be "+0000"
    bankable_utc_string = bankable_first + bankable_tz

    return bankable_utc_string


def json_test_get_tms_id(json_root):
    """
    test JSON for tms id
    :param json_root:
    :return:
    """

    temp = json_root.get("TmsId", "").strip()
    if temp == "":
        raise Exception("No TmsId in JSON")

    return temp


def json_test_get_series_tms_id(json_root):
    """
    test JSON for series tms id
    :param json_root:
    :return:
    """

    temp = json_root.get("TmsSeriesId", "").strip()
    if temp == "":
        raise Exception("No TmsSeriesId in JSON")

    return temp


def json_test_get_original_content_20cf(json_root):
    """
    test JSON for OriginalContent20CF
    :param json_root:
    :return:
    """

    temp = json_root.get("OriginalContent20CF", "")
    if temp == "":
        logger_service.error("No OriginalContent20CF in JSON")
        temp = False

    return temp


def json_test_get_fxm_retro(json_root):
    """
    test JSON for FXMRetro20CF
    :param json_root:
    :return:
    """

    temp = json_root.get("FXMRetro20CF", "")
    if temp == "":
        logger_service.error("No FXMRetro20CF in JSON")
        temp = False

    return temp


def json_test_get_category(json_root):
    """
    test JSON for category (Clean or C-Type)
    :param json_root:
    :return:
    """
    nld_type = json_test_get_nld_type(json_root)
    try:
        material_id = json_test_get_material_id(json_root)
    except:
        material_id = ""

    try:
        tms_id = json_test_get_tms_id(json_root)
    except:
        tms_id = ""

    try:
        tms_series_id = json_test_get_series_tms_id(json_root)
    except:
        tms_series_id = ""

    try:
        season_num = json_test_get_season_num(
            json_root
        )  # this is missing for Movies/Specials
    except:
        season_num = ""

    try:
        season_num = json_test_get_season_num(
            json_root
        )  # this is missing for Movies/Specials
    except:
        season_num = ""

    try:
        program_type_str = json_test_get_program_type(json_root)
    except:
        program_type_str = ""

    is_movie = json_test_is_movie_in_cts_mappings(json_root)

    cat_to_return = ""
    # TODO: add more nld types as we get more examples (for now we only have Clean and C3 samples)
    # logical order of operations:
    # Try to pull out the Movies/Specials first

    # 1) If Clean (not C-Type) and TMS starts with MV, obviously a movie
    # 2) If Clean and no TMS - check if "Movie" in Mappings
    # 3) If Clean, no TMS, no Mappings, is there's no Season in JSON, it's probably a movie or special
    if nld_type.lower() == "clean":
        # might be a movie though
        tms_id = json_root.get("TmsId", "").strip()
        if tms_id.startswith("MV"):
            cat_to_return = "Movie (Theatrical Formatted for TV)"

        elif material_id.startswith("NONE") and tms_id.startswith("SH"):
            cat_to_return = "Special"

        elif program_type_str.lower() == "special":
            cat_to_return = "Special"

        elif tms_id == "":
            # No TMS ID
            if is_movie != "":  # if empty, then there are no CTS mappings in JSON
                if is_movie:
                    cat_to_return = "Movie (Theatrical Formatted for TV)"
                else:
                    cat_to_return = "Episode"  # assumption
            else:
                logger_service.error(
                    "Unable to determine if this is a Movie/Special or Episode. We are missing a ton of data."
                )
                cat_to_return = ""
        else:
            cat_to_return = "Episode"

    elif nld_type.lower() == "c3" or nld_type.lower() == "c7":
        if program_type_str.lower() == "movie":
            cat_to_return = "Movie C Type"
        elif material_id.startswith("NONE") and tms_id.startswith(
            "SH"
        ):  # MediaHub has sent a special but as "Series"
            cat_to_return = "Special C Type"
        elif program_type_str.lower() == "special":
            cat_to_return = "Special C Type"
        else:
            cat_to_return = "Episode C Type"

    elif nld_type.lower() == "vod" or nld_type.lower() == "vod4":
        if material_id.startswith("NONE") and tms_id.startswith(
            "SH"
        ):  # MediaHub has sent a special but as "Series"
            cat_to_return = "Special D Type"
        elif program_type_str.lower() == "special":
            cat_to_return = "Special D Type"
        elif program_type_str.lower() == "movie":
            cat_to_return = "Movie D Type"
        else:
            cat_to_return = "Episode D Type"

    return cat_to_return


def json_test_get_HD_mappings(json_root):
    """
    test JSON for HD mappings (will return all values)
    :param json_root:
    :return:
    """

    categories = json_root.get("Categories", "")
    if categories == "":
        raise Exception("No Categories in JSON")

    maps = []
    if len(categories) == 1 and type(categories) == list:
        firstcat = categories[0]
        cts = firstcat.get("CTS", [])
        for this_cts in cts:
            maps.append(this_cts)

    return maps


def json_test_get_source_media_filename(json_root):
    """
    test JSON to get source video filename
    :param json_root:
    :return:
    """

    temp = json_root.get("FileName", "")
    if temp == "":
        logger_service.error("No FileName in JSON")
        # temp = False

    return temp


def json_add_new_metadata(adi_root, adi_json):
    """
    add new data to XML
    :param adi_json:
    :param adi_root:
    :return:
    """
    program_type = adi_root.get("program_type", {}).get("value")

    addl_element = SubElement(adi_root, "AdditionalMetadata")
    # add these titles to the XML
    actor_list = adi_json.get("actors", {}).get("value")
    for this_actor in actor_list:
        actor_full = this_actor.get("first", "") + " " + this_actor.get("last", "")
        actor_element = SubElement(addl_element, "actor")
        actor_element.text = actor_full

    short_description_element = SubElement(addl_element, "episodeShortDescription")
    long_description_element = SubElement(addl_element, "episodeLongDescription")
    content_short_description_element = SubElement(
        addl_element, "contentShortDescription"
    )
    content_long_description_element = SubElement(
        addl_element, "contentLongDescription"
    )

    short_description_element.text = adi_json.get("episode_short_synopsis", {}).get(
        "value"
    )
    long_description_element.text = adi_json.get("episode_long_synopsis", {}).get(
        "value"
    )

    content_short_description_element.text = adi_json.get(
        "content_short_synopsis", {}
    ).get("value")
    content_long_description_element.text = adi_json.get(
        "content_long_synopsis", {}
    ).get("value")

    keyword_element = SubElement(addl_element, "keywords")
    keyword_element.text = adi_json.get("keywords", {}).get("value")

    episode_prod_num_element = SubElement(addl_element, "episodeProductionNumber")
    episode_prod_num_element.text = adi_json.get("episode_production_number", {}).get(
        "value"
    )

    # tms_element = SubElement(addl_element, 'tmsId')
    # tms_element.text = 'this_will_come_later'  # not needed -provided from MediaHub

    return adi_root


def make_category_list_given_program_type(program_type_str, network_str):
    cat_values = ""

    if program_type_str.lower() == "movie":
        # C3 movies are very rare. So lookup partner list as if this was a Clean episode
        cat_values = [
            {
                "type": "Movie (Theatrical Formatted for TV)",
                "partners": get_partner_list_from_default_properties(
                    network_str, "Clean"
                ),
            },
            {
                "type": "Movie C Type",
                "partners": get_partner_list_from_default_properties(
                    network_str, "CType"
                ),
            },
            {
                "type": "Movie D Type",
                "partners": get_partner_list_from_default_properties(
                    network_str, "VOD"
                ),
            },
        ]

    if program_type_str.lower() == "series" or program_type_str.lower() == "acquired":
        cat_values = [
            {
                "type": "Episode",
                "partners": get_partner_list_from_default_properties(
                    network_str, "Clean"
                ),
            },
            {
                "type": "Episode D Type",
                "partners": get_partner_list_from_default_properties(
                    network_str, "VOD"
                ),
            },
            {
                "type": "Episode C Type",
                "partners": get_partner_list_from_default_properties(
                    network_str, "CType"
                ),
            },
        ]

    if program_type_str.lower() == "special":
        cat_values = [
            {
                "type": "Special",
                "partners": get_partner_list_from_default_properties(
                    network_str, "Clean"
                ),
            },
            {
                "type": "Special C Type",
                "partners": get_partner_list_from_default_properties(
                    network_str, "CType"
                ),
            },
        ]

    if cat_values == "":
        logger_service.error(
            "Unknown program_type_str value: {}".format(program_type_str)
        )

    return cat_values


def get_partner_list_from_default_properties(network_name, cat_type):
    """

    :param network_name:
    :return:
    """

    partner_clean_list = []
    all_default_partners = default_partners_and_categories()
    for this_partner in all_default_partners:
        partner_name = this_partner.get("namePartner", "")
        cat_list = this_partner.get("typeCategories", [])
        for this_cat in cat_list:
            if this_cat.get("categoryName", "").lower() == cat_type.lower():
                this_cat_network_list = this_cat.get("networks")
                for this_net in this_cat_network_list:
                    if this_net.lower() == network_name.lower():
                        partner_clean_list.append(partner_name)
                        break

    return partner_clean_list


def cleanup(raw_string):
    """
    Replace fancy/curly characters (usually from copy/paste) to normal characters
    :param raw_string:
    :return:
    """
    transl_table = dict([(ord(x), ord(y)) for x, y in zip("‘’´“”—–-", "'''\"\"---")])
    try:
        fixed_string = raw_string.translate(transl_table)
    except:
        fixed_string = raw_string

    return fixed_string


def prettify(x):
    try:
        reparsed = parseString(ET.tostring(x))
    except:
        # print(ET.tostring(x))
        # reparsed = ET.tostring(x)
        return ET.tostring(x)
    new_x = "\n".join(
        [
            line
            for line in reparsed.toprettyxml(indent=" " * 4).split("\n")
            if line.strip()
        ]
    )
    return new_x


def main(json_root, local_config_dict):
    """
    Parse the data from raw JSON.
    :param json_root: the raw json data (parsed from file)
    :param local_config_dict:
    :return:
    """
    # adi_json = create_series_excel_json()
    adi_json = {
        "program_type": {"value": ""},
        "content_name": {"value": ""},
        "episode_tms_id": {"value": ""},
        "tms_id": {"value": ""},
        "season_num": {"value": ""},
        "material_id": {"value": ""},
        "source_filename": {"value": ""},
        "content_prefix": {"value": ""},
        "comscore_c6": {"value": ""},
        "network": {"value": ""},
        "copyright": {"value": ""},
        "default_rating": {"value": ""},
        "rating_content_labels": {"value": ""},
        "episode_c_type": {"value": ""},
        "episode_d_type": {"value": ""},
        "country": {"value": ""},
        "content_type": {"value": ""},
        "cms_folder_name": {"value": ""},
        "show_folder": {"value": ""},
        "title_brief": {"value": ""},
        "title_brief": {"value": ""},
        "sd_mappings": {"value": ""},
        "hd_mappings": {"value": ""},
        "genre_code": {"value": ""},
        "ad_content_id": {"value": ""},
        "movie_asset_id": {"value": ""},
        "is_4k": {"value": ""},
        "content_start_year": {"value": ""},
        "categories_and_distribution": {"value": ""},
        "reach_genre": {"value": ""},
        "content_short_synopsis": {"value": ""},
        "content_long_synopsis": {"value": ""},
        "episode_short_synopsis": {"value": ""},
        "season_short_synopsis": {"value": ""},
        "season_long_synopsis": {"value": ""},
        "episode_long_synopsis": {"value": ""},
        "episode_production_number": {"value": ""},
        "episode_number": {"value": ""},
        "radar_group_id": {"value": ""},
        "actors": {"value": ""},
        "keywords": {"value": ""},
    }

    # Program Type - series or movie
    try:
        program_type_str = json_test_get_program_type(json_root)
    except:
        program_type_str = ""
    adi_json["program_type"]["value"] = program_type_str

    # ASK to MALCOM Is it acquired? (meaning there's no C-Window. Clean is available on Day 1)
    is_acquired = json_test_get_acquired(json_root)
    adi_json["is_acquired"] = {"value": is_acquired}

    # Series/Movie Title
    try:
        content_name_str = json_test_get_content_title(json_root)
    except:
        content_name_str = ""
    adi_json["content_name"]["value"] = content_name_str

    # if this is a movie, there will not be a Series ID (because there is no series for a movie)
    # this also means the TMS ID which is normally used for an episode is used for the movie (usually starting with MV)

    # TODO: need to verify the above. Waiting for new JSON files from Ted
    # if program_type_str == 'movie':
    #     tms_id = xml_test_get_tms_id(adi_root)
    # else:
    #     tms_id = ''
    # adi_json['tms_id']['value'] = tms_id

    # this gets the Episode TMS
    try:
        tms_id = json_test_get_tms_id(json_root)
    except:
        tms_id = ""
    adi_json["episode_tms_id"]["value"] = tms_id

    # and this gets the series TMS (if series)
    if (
        program_type_str.lower() == "movie" and tms_id != ""
    ):  # TODO: maybe lookup and find movie TMS
        series_tms_id = tms_id
    elif program_type_str.lower() == "special" and tms_id != "":
        series_tms_id = tms_id
    else:
        try:
            series_tms_id = json_test_get_series_tms_id(json_root)
        except:
            series_tms_id = ""
    adi_json["tms_id"]["value"] = series_tms_id

    # Season Num
    try:
        # season_num_str = xml_test_get_season_num(adi_root)
        season_num_str = json_test_get_season_num(json_root)
    except:
        season_num_str = ""
    adi_json["season_num"]["value"] = season_num_str

    # Episode Title
    episode_title_str = json_test_get_episode_title(json_root)
    adi_json["episode_title"] = {}
    adi_json["episode_title"]["value"] = episode_title_str
    # not in 'create_series_excel_json' but this is just for tracking

    # Episode Number
    if program_type_str == "movie":
        episode_number = ""
    else:
        episode_number = json_test_get_episode_number(json_root)

    # Episode short description
    episode_short_description = json_test_get_episode_short_description(json_root)

    # Episode Window Data (start/end)
    episode_window_data = json_test_get_nld_window_dates(json_root)

    episode_bankable_date = json_test_get_bankable_date(json_root)
    adi_json["bankable_date"] = {}
    adi_json["bankable_date"]["value"] = episode_bankable_date

    # {'start_window': start_window_utc_string,
    #  'end_window': end_window_utc_string}
    adi_json["episode_window_start"] = {}
    adi_json["episode_window_start"]["value"] = episode_window_data.get(
        "start_window", ""
    )

    adi_json["episode_window_end"] = {}
    adi_json["episode_window_end"]["value"] = episode_window_data.get("end_window", "")
    # not in 'create_series_excel_json' but this is just for tracking

    # Network name
    network_str = json_test_get_network(json_root)
    if network_str == "NGC":
        network_str = "National Geographic"
    elif network_str == "NGW":
        network_str = "Nat Geo Wild"

    # MaterialId
    try:
        material_id = json_test_get_material_id(json_root)
    except:
        material_id = ""

    adi_json["material_id"] = {"value": material_id}

    # is this really a special in series clothing? (e.g. did MediaHub say this is a series but it's really a special?)
    # Episode TMS should start with "EP". If TMS for the _Episode_ starts with "SH", then most likely a Special
    if material_id.startswith("NONE") and tms_id.startswith("SH"):
        is_special = True
        program_type_str = "special"
        adi_json["program_type"]["value"] = "special"
    else:
        is_special = False

    if program_type_str == "":
        if material_id.startswith("VOD") or material_id.startswith("XMX"):
            program_type_str = "movie"
            adi_json["program_type"]["value"] = "movie"

    # ShowCode
    if program_type_str == "series":
        showcode_str = json_test_get_showcode(
            json_root
        )  # will grab 3 or 4 letters of Material ID up until a number.
        # match = re.match('^[a-zA-Z]{,4}', material_id)
        # if len(material_id) > 2:
        #     showcode_str = material_id[:3]
        # showcode_str = ''
        comscore_c6_str = showcode_str

    elif program_type_str == "special":
        showcode_str = json_test_get_showcode_special(json_root)
        comscore_c6_str = "AFM"

    elif program_type_str == "movie":
        showcode_str = json_test_get_showcode_movie(json_root)
        comscore_c6_str = "AFM"
    else:
        comscore_c6_str = ""
        showcode_str = ""

    # Get source video filename
    try:
        source_video_filename = json_test_get_source_media_filename(json_root)
    except:
        source_video_filename = ""
    adi_json["source_filename"]["value"] = source_video_filename

    adi_json["content_prefix"]["value"] = showcode_str
    adi_json["comscore_c6"]["value"] = comscore_c6_str

    adi_json["network"]["value"] = network_str

    # Copyright (required) - just use Network
    adi_json["copyright"]["value"] = network_str

    # Original Content for 20CF (if false then "acquired")
    original_content_20cf = json_test_get_original_content_20cf(json_root)
    adi_json["original_content_20cf"] = {}
    adi_json["original_content_20cf"]["value"] = original_content_20cf

    # FXM Retro (applies to FXM content - Retro is super old stuff)
    fmx_retro = json_test_get_fxm_retro(json_root)
    adi_json["fmx_retro"] = {}
    adi_json["fmx_retro"]["value"] = fmx_retro

    # Rating
    try:
        # rating_str = xml_test_get_rating(adi_root)
        rating_str = json_test_get_rating(json_root)
    except:
        rating_str = ""
    adi_json["default_rating"]["value"] = rating_str

    # Rating labels for this specific episode (if available)
    try:
        # rating_str = xml_test_get_rating(adi_root)
        episode_rating_labels = json_test_get_rating_content_labels(json_root)
    except:
        episode_rating_labels = []

    if len(episode_rating_labels) > 0:
        adi_json["rating_content_labels"]["value"] = episode_rating_labels

    # C-Type categories - default for Fox/NG networks is C3/D4
    adi_json["episode_c_type"]["value"] = "C3"
    adi_json["episode_d_type"]["value"] = "D4"

    # Country - just default to USA
    adi_json["country"]["value"] = "USA"

    # Content type - default to "Long Form"
    adi_json["content_type"]["value"] = "Long Form"

    # CMS folder name
    try:
        # cms_folder_name = xml_test_get_cms_folder_name(adi_root)
        cms_folder_name = json_test_get_cms_folder_name(json_root)
    except:
        allowed_chars = string.ascii_letters + string.digits + "-"
        series_cleaned_for_gopub_folder = re.sub(
            "[^{}]".format(allowed_chars), "", content_name_str
        )
        cms_folder_name = series_cleaned_for_gopub_folder
    adi_json["cms_folder_name"]["value"] = cms_folder_name

    # Show Folder
    adi_json["show_folder"]["value"] = cms_folder_name

    # Title Brief - limited to 9 characters for series, 15 for movies
    if program_type_str == "series":
        adi_json["title_brief"]["value"] = cms_folder_name[:9]
    elif program_type_str == "movie" or program_type_str == "special":
        adi_json["title_brief"]["value"] = cms_folder_name[:15]

    # HD/SD mappings
    try:
        hd_mappings = json_test_get_HD_mappings(json_root)
    except:
        hd_mappings = []

    sd_mappings = get_sd_mapping_from_hd_mapping(hd_mappings)

    adi_json["sd_mappings"]["value"] = sd_mappings
    adi_json["hd_mappings"]["value"] = hd_mappings

    # Genre
    # try:
    genre = json_test_get_genre(json_root)
    # except:
    #     genre = ''

    adi_json["genre_code"]["value"] = genre

    # Reach genre - update: let's do this later, try to match to TitleAPI, Gracenote
    # adi_json['reach_genre']['value'] = get_reach_genre_from_genre_list(json_root)

    # ad_content_id
    # TODO: Mediahub JSON does not have ad content id
    # try:
    #     ad_content_id = xml_test_get_ad_content_id(adi_root)
    # except:
    #     ad_content_id = ''
    # adi_json['ad_content_id']['value'] = ad_content_id
    adi_json["ad_content_id"]["value"] = ""

    # movie_asset_id
    # TODO: Mediahub JSON does not have video asset id
    # try:
    #     movie_asset_id = xml_test_get_video_asset_id(adi_root)
    # except:
    #     movie_asset_id = ''
    # adi_json['movie_asset_id']['value'] = movie_asset_id
    adi_json["movie_asset_id"]["value"] = ""

    # Episode category
    episode_category = json_test_get_category(json_root)

    # Is this 4K?
    is_4k = json_test_is_media_4k(json_root)
    adi_json["is_4k"] = {"value": is_4k}

    # cross check with Gracenote to confirm
    gracenote_api_key = local_config_dict.get("gracenote_api_key", "")
    if gracenote_api_key != "":
        if tms_id != "":
            gracenote_episode_results = gracenote_get_program_details(
                gracenote_api_key, tms_id
            )

            if gracenote_episode_results != {}:
                try:
                    gracenote_subtype = gracenote_episode_results.get("subType", "")
                    if gracenote_subtype.clean().lower() == "miniseries":
                        gracenote_subtype = "Series"
                except Exception as e:
                    gracenote_subtype = ""

                if gracenote_subtype != "":
                    if (
                        program_type_str == "series"
                    ):  # this seems to be almost the default for MediaHub stuff
                        if gracenote_subtype.lower() != program_type_str.lower():
                            # should this be a C Type or not?
                            nld_type = json_test_get_nld_type(json_root)
                            if nld_type.lower() == "clean":
                                pass
                            elif nld_type.lower() == "c3" or nld_type.lower() == "c7":
                                gracenote_subtype = (
                                    gracenote_subtype + " C Type"
                                )  # e.g. "Special" becomes "Special C Type"
                            elif (
                                nld_type.lower() == "vod" or nld_type.lower() == "vod4"
                            ):
                                gracenote_subtype = (
                                    gracenote_subtype + " D Type"
                                )  # e.g. "Special" becomes "Special D Type"

                            logger_service.error(
                                "MediaHub JSON says this is a {0} but Gracenote says it is {1}. Switching to {1}".format(
                                    program_type_str, gracenote_subtype
                                )
                            )

                            episode_category = gracenote_subtype

                            # cleanup earlier assigned variables
                            adi_json["title_brief"]["value"] = cms_folder_name[:15]
                            program_type_str = gracenote_subtype

                            if gracenote_subtype.lower() == "special":
                                showcode_str = json_test_get_showcode_special(json_root)
                                comscore_c6_str = "AFM"
                                adi_json["content_prefix"]["value"] = showcode_str
                                adi_json["comscore_c6"]["value"] = comscore_c6_str

    # adi_json['episode_category']['value'] = episode_category
    adi_json["episode_category"] = {"value": episode_category}

    # Episode Airdate - as ISO 8601 format (e.g. 2023-04-06T02:00:00Z )
    episode_airdate = json_test_get_airdate(json_root)
    # adi_json['episode_airdate']['value'] = episode_airdate
    adi_json["episode_airdate"] = {"value": episode_airdate}

    movie_year = json_test_get_year(json_root)  # Year (probably only there for movies)
    if movie_year != "" and movie_year is not None:
        adi_json["content_start_year"]["value"] = movie_year

    cat_values = make_category_list_given_program_type(program_type_str, network_str)

    adi_json["categories_and_distribution"]["value"] = cat_values

    #
    #  Gracenote stuff
    # gracenote_api_key = local_config_dict.get('gracenote_api_key', '')
    gracenote_episode_results = {}
    gracenote_series_results = {}
    if gracenote_api_key != "":
        # get series/movie details - series_tms_id will be the movie TMS if this is a movie
        if tms_id != "":
            gracenote_episode_results = gracenote_get_program_details(
                gracenote_api_key, tms_id
            )
        else:
            gracenote_episode_results = {}

        if series_tms_id != "":
            gracenote_series_results = gracenote_get_program_details(
                gracenote_api_key, series_tms_id
            )
        elif (
            series_tms_id == "" and tms_id != ""
        ):  # has to be series/episode, not movie.
            # Because earlier we said if movie, then copy tms to series_tms
            series_id = gracenote_episode_results.get("seriesId", "")
            if series_id != "":
                series_details = gracenote_get_series_details(
                    gracenote_api_key, series_id
                )
                series_tms_id = series_details.get("tmsId", "")
                if series_tms_id != "":
                    adi_json["tms_id"]["value"] = series_tms_id
                    gracenote_series_results = gracenote_get_program_details(
                        gracenote_api_key, series_tms_id
                    )
        else:
            gracenote_series_results = {}

    # Program Type override - if Gracenote says it's a Special, let's use that
    if gracenote_episode_results != {}:
        try:
            gracenote_subtype = gracenote_episode_results.get("subType", "")
            if gracenote_subtype.clean().lower() == "miniseries":
                gracenote_subtype = "Series"
        except Exception as e:
            gracenote_subtype = ""

        if gracenote_subtype != "":
            if program_type_str == "series":
                if gracenote_subtype.lower() != program_type_str.lower():
                    nld_type = json_test_get_nld_type(json_root)
                    if nld_type.lower() == "clean":
                        pass
                    elif nld_type.lower() == "c3" or nld_type.lower() == "c7":
                        gracenote_subtype = (
                            gracenote_subtype + " C Type"
                        )  # e.g. "Special" becomes "Special C Type"
                    elif nld_type.lower() == "vod" or nld_type.lower() == "vod4":
                        gracenote_subtype = (
                            gracenote_subtype + " D Type"
                        )  # e.g. "Special" becomes "Special C Type"
                    logger_service.error(
                        "MediaHub JSON says this is a {0} but Gracenote says it is {1}. Switching to {1}".format(
                            program_type_str, gracenote_subtype
                        )
                    )

                    if (
                        gracenote_subtype.lower() == "special"
                        and nld_type.lower() == "clean"
                    ):
                        adi_json["episode_category"] = {"value": "special"}
                        adi_json["program_type"]["value"] = "special"
                    elif gracenote_subtype.lower() == "special" and (
                        nld_type.lower() == "c3" or nld_type.lower() == "c7"
                    ):
                        adi_json["episode_category"] = {"value": "special c type"}
                        adi_json["program_type"]["value"] = "special"
                    elif gracenote_subtype.lower() == "special" and (
                        nld_type.lower() == "vod" or nld_type.lower() == "vod4"
                    ):
                        adi_json["episode_category"] = {"value": "special d type"}
                        adi_json["program_type"]["value"] = "special"

                    # TODO: other changes needed based on 'special' instead of 'series'
                    if gracenote_subtype.lower() == "special":
                        showcode_str = json_test_get_showcode_special(json_root)
                        comscore_c6_str = "AFM"

                    # override categories and distribution to match what Gracenote says
                    program_type_str = gracenote_subtype
                    cat_values = make_category_list_given_program_type(
                        program_type_str, network_str
                    )
                    adi_json["categories_and_distribution"]["value"] = cat_values

    #
    # TitleAPI stuff
    authz_client_id = local_config_dict.get("auth_z_client_id", "")
    authz_client_secret = local_config_dict.get("auth_z_client_secret", "")
    authz_grant_type = local_config_dict.get("auth_z_grant_type", "")
    authz_scope = local_config_dict.get("auth_z_scope", "")
    bolt_bearer_token = local_config_dict.get("bolt_bearer_token", "")

    token = authz_get_token(
        authz_client_id, authz_client_secret, authz_grant_type, authz_scope
    )

    title_api_success = False
    try:
        if program_type_str.lower() == "series":
            resp = titleapi_run_search_for_episode(
                token, content_name_str, season_num_str, episode_title_str
            )
        elif program_type_str.lower() == "special":
            resp = titleapi_run_search_for_special(
                token, content_name_str, season_num_str, episode_title_str
            )
        elif program_type_str.lower() == "movie":
            resp = titleapi_run_search_for_movie(token, content_name_str, movie_year)
        else:
            resp = {}
    except requests.exceptions.HTTPError as e:
        logger_service.error(e)
        resp = {}

    try:
        resp_data = resp.get("data", {})
        titleapi_results = resp_data.get("searchTitles", {})
        titleapi_episode_missing = False
        # title_api_success = True
        # print(json.dumps(titleapi_results, indent=2))
    except KeyError as e:
        titleapi_results = {}
        logger_service.error("TitleAPI response did not have expected data:")
        logger_service.error(resp)
        logger_service.error(e)
    except AttributeError as e:
        logger_service.error(e)
        titleapi_results = {}

    if titleapi_results is None:
        titleapi_result_count = 0
    else:
        titleapi_result_count = titleapi_results.get("totalCount", 0)

    # if nothing found in TitleAPI, we're done
    if titleapi_result_count == 0:
        if program_type_str.lower() == "series":
            logger_service.warning(
                'No results found in TitleAPI. Series: "{0}"   Season:"{1}"   Episode Title:"{2}"'.format(
                    content_name_str, season_num_str, episode_title_str
                )
            )
            logger_service.warning(
                "Will try to find anything from Season {}".format(season_num_str)
            )
            # titleapi_episode_missing = True
            # get all episodes from season - then get any episode and look at parent. This will be the season.
            if episode_number != 0 and episode_number != "":
                resp = (
                    titleapi_run_search_for_episode_given_episode_num_and_description(
                        token,
                        content_name_str,
                        season_num_str,
                        episode_number,
                        episode_short_description,
                    )
                )
            else:  # no episode number - try to match based on episode title in the season
                resp = (
                    titleapi_run_search_for_episode_given_episode_name_and_description(
                        token,
                        content_name_str,
                        season_num_str,
                        episode_title_str,
                        episode_short_description,
                    )
                )

            titleapi_results = resp.get("data", {}).get("searchTitles", {})

            # check if we still have nothing - try to get from season level
            if titleapi_results == {}:
                titleapi_episode_missing = True
                resp = titleapi_run_search_for_anything_in_season(
                    token, content_name_str, season_num_str
                )
                titleapi_results = resp.get("data", {}).get(
                    "searchTitles", {}
                )  # this will be a list of all episodes in season

        elif program_type_str.lower() == "movie":
            logger_service.warning(
                'No Movie results found in TitleAPI. Movie: "{0}"   '.format(
                    content_name_str
                )
            )
        elif program_type_str.lower() == "special":
            logger_service.warning(
                'No Special results found in TitleAPI. Special: "{0}"   '.format(
                    content_name_str
                )
            )
        else:
            logger_service.warning(
                'No results found in TitleAPI. Series: "{0}"   Season:"{1}"   Episode Title:"{2}"'.format(
                    content_name_str, season_num_str, episode_title_str
                )
            )

        # return adi_json  # not quite done - try Gracenote

    if titleapi_result_count == 0 and gracenote_episode_results == {}:
        logger_service.info("Nothing found in TitleAPI or Gracenote. Finishing...")
        return adi_json

    # Genre
    adi_json["reach_genre"]["value"] = get_reach_genre_from_genre_list(json_root)
    if (
        adi_json["reach_genre"]["value"] == ""
        or adi_json["reach_genre"]["value"] is None
    ):
        titleapi_genre_list = titleapi_get_genre_list(titleapi_results)
        adi_json["reach_genre"]["value"] = (
            get_reach_genre_given_list_of_potential_genres(titleapi_genre_list)
        )
        if adi_json["reach_genre"]["value"] == "":
            # no genre list from TitleAPI? try Gracenote
            gracenote_genre_list = gracenote_get_genre_list(gracenote_series_results)
            adi_json["reach_genre"]["value"] = (
                get_reach_genre_given_list_of_potential_genres(gracenote_genre_list)
            )

    # if no other genre, just use "Drama" because we need _something_
    if (
        adi_json["reach_genre"]["value"] == ""
        or adi_json["reach_genre"]["value"] is None
    ):
        adi_json["reach_genre"]["value"] = "Drama"

    # Genre MVPD mappings
    if (
        adi_json["genre_code"]["value"] == ""
        or adi_json["genre_code"]["value"] is None
        or len(adi_json["genre_code"]["value"]) == 0
    ):
        titleapi_genre_list = titleapi_get_genre_list(titleapi_results)
        if len(titleapi_genre_list) > 0:
            adi_json["genre_code"]["value"] = titleapi_genre_list
        else:  # try Gracenote
            gracenote_genre_list = gracenote_get_genre_list(gracenote_series_results)
            if len(gracenote_genre_list) > 0:
                adi_json["genre_code"]["value"] = gracenote_genre_list

    # Series Short Descriptions (#1 TitleAPI, #2 Gracenote, #3 "Presented by...")
    series_short_description = titleapi_get_series_short_description(titleapi_results)
    # series_short_description = ''  # TODO for troubleshooting
    if series_short_description == "" or series_short_description is None:
        series_short_description = gracenote_get_short_description(
            gracenote_series_results
        )

    if series_short_description == "":
        series_short_description = "Presented by {}".format(network_str)
    adi_json["content_short_synopsis"]["value"] = series_short_description

    # Series Long Descriptions (#1 TitleAPI, #2 Gracenote, #3 "Presented by...")
    series_long_description = titleapi_get_series_long_description(titleapi_results)
    if series_long_description == "" or series_long_description is None:
        series_long_description = gracenote_get_long_description(
            gracenote_series_results
        )
    if series_long_description == "":
        series_long_description = "Presented by {}".format(network_str)
    adi_json["content_long_synopsis"]["value"] = series_long_description

    # Episode Descriptions
    if program_type_str.lower() == "series":
        if not titleapi_episode_missing:
            episode_short_description = titleapi_get_episode_short_description(
                titleapi_results
            )
        else:
            # get episode description from gracenote
            if gracenote_episode_results != {}:
                episode_short_description = gracenote_get_short_description(
                    gracenote_episode_results
                )

        if episode_short_description == "":
            episode_short_description = "Presented by {}".format(network_str)

        adi_json["episode_short_synopsis"]["value"] = episode_short_description

        if not titleapi_episode_missing:
            episode_long_description = titleapi_get_episode_long_description(
                titleapi_results
            )
        else:
            episode_long_description = gracenote_get_long_description(
                gracenote_episode_results
            )

        if episode_long_description == "":
            episode_long_description = "Presented by {}".format(network_str)
        adi_json["episode_long_synopsis"]["value"] = episode_long_description
    elif program_type_str.lower() == "movie" or program_type_str.lower() == "special":
        adi_json["episode_short_synopsis"]["value"] = series_short_description
        adi_json["episode_long_synopsis"]["value"] = series_long_description

    # Season Descriptions (only Series has seasons)
    # TODO: MUST HAVE SOMETHING IN ALL FIELDS
    # TODO: Use "Presented by {Network}" if nothing
    if program_type_str.lower() == "series":
        season_short_description = titleapi_get_season_short_description(
            titleapi_results
        )
        if season_short_description == "" or season_short_description is None:
            season_short_description = "Presented by {}".format(network_str)
    else:
        season_short_description = ""  # movies and specials don't have seasons
    adi_json["season_short_synopsis"]["value"] = season_short_description

    season_long_description = titleapi_get_season_long_description(titleapi_results)
    if program_type_str.lower() == "series":
        if season_long_description == "" or season_long_description is None:
            season_long_description = "Presented by {}".format(network_str)
    else:
        season_long_description = ""  # movies and specials don't have seasons
    adi_json["season_long_synopsis"]["value"] = season_long_description

    # Episode Production Number
    if adi_json.get("episode_production_number", {}).get("value", "") == "":
        if program_type_str.lower() == "series":
            if not titleapi_episode_missing:
                episode_production_number = titleapi_get_episode_production_number(
                    titleapi_results, network_str
                )
            else:
                try:
                    episode_production_number = str(int(season_num_str)) + str(
                        int(episode_number)
                    ).zfill(2)
                except:
                    episode_production_number = str(season_num_str) + str(
                        episode_number
                    )
            adi_json["episode_production_number"]["value"] = episode_production_number

        if program_type_str.lower() == "movie" or program_type_str.lower() == "special":
            adi_json["episode_production_number"]["value"] = material_id

    # Episode Number
    if adi_json.get("episode_number", {}).get("value", "") == "":
        if program_type_str.lower() == "series":
            if not titleapi_episode_missing:
                episode_number_titleapi = titleapi_get_episode_number(
                    titleapi_results
                )  # TitleAPI is sometimes wrong
            else:
                try:
                    episode_number_titleapi = str(
                        int(episode_number)
                    )  # clean leading zero
                except:
                    episode_number_titleapi = str(episode_number)

            adi_json["episode_number"]["value"] = episode_number_titleapi

        elif (
            program_type_str.lower() == "movie" or program_type_str.lower() == "special"
        ):
            adi_json["episode_number"]["value"] = 1

    # Start Year - we tried to get from MediaHub JSON. If not there, try to get from TitleAPI. Then try Gracenote.
    if adi_json.get("content_start_year", {}).get("value", "") == "":
        if program_type_str.lower() == "series":
            start_year = titleapi_get_series_start_year(titleapi_results)
            if start_year == "" or start_year is None:
                start_year = gracenote_get_start_year(gracenote_series_results)

            adi_json["content_start_year"]["value"] = str(start_year)
        elif program_type_str.lower() == "movie":
            start_year = titleapi_get_movie_year(titleapi_results)
            if start_year == "" or start_year is None:
                start_year = gracenote_get_start_year(gracenote_series_results)
            adi_json["content_start_year"]["value"] = str(start_year)

        elif program_type_str.lower() == "special":
            start_year = titleapi_get_special_year(titleapi_results)
            if start_year == "" or start_year is None:
                start_year = gracenote_get_start_year(gracenote_series_results)
            adi_json["content_start_year"]["value"] = str(start_year)

    if (
        adi_json["content_start_year"]["value"] == ""
        or adi_json["content_start_year"]["value"] is None
    ):
        logger_service.error("ERROR: Still not able to find Start Year.")
        adi_json["content_start_year"]["value"] = ""

    # Keywords - we need SOMETHING
    # if no keywords, use genre list. Then use series name split on spaces. Then use network name.
    keywords = titleapi_get_series_keywords(titleapi_results)
    if keywords == "" or keywords == None:
        try:
            temp_keyword_list = json_root.get("Genres", [])
            temp_keywords = ", ".join(temp_keyword_list)
        except:
            temp_keywords = []

        if temp_keywords == [] or temp_keywords == "":
            if content_name_str != "":
                temp_keyword_list = content_name_str.split(" ")
                temp_keywords = ", ".join(temp_keyword_list)
            else:
                temp_keywords = network_str

        keywords = temp_keywords

    adi_json["keywords"]["value"] = keywords

    # RADAR Group ID
    radar_group_id = titleapi_get_radar_group_id(
        program_type_str.lower(), episode_title_str, content_name_str, titleapi_results
    )
    adi_json["radar_group_id"]["value"] = str(radar_group_id)

    # Bolt lookups for Cast
    cast_list = []
    if radar_group_id != "":
        # TODO: add Bolt support for Specials
        if program_type_str.lower() == "series":
            try:
                bolt_series = bolt_get_series_from_group_id(
                    bolt_bearer_token, radar_group_id
                )
                bolt_series_id = bolt_series["id"]

                bolt_season = bolt_get_season_from_series_id(
                    bolt_bearer_token, bolt_series_id, season_num_str
                )
                bolt_season_id = bolt_season["id"]

                bolt_cast = bolt_get_cast_from_season_id(
                    bolt_bearer_token, bolt_season_id
                )

                for this_cast in bolt_cast:
                    this_cast_character = this_cast.get("character", {}).get("name")
                    this_cast_firstname = this_cast.get("talent", {}).get(
                        "firstName", ""
                    )
                    this_cast_lastname = this_cast.get("talent", {}).get("lastName", "")
                    this_cast_json = {
                        "first": this_cast_firstname,
                        "last": this_cast_lastname,
                        "character": this_cast_character,
                    }
                    cast_list.append(this_cast_json)
                    # print('{}, {}: {}'.format(this_cast_lastname, this_cast_firstname, this_cast_character))
                adi_json["actors"]["value"] = cast_list
            except:
                pass

        if program_type_str.lower() == "movie":
            try:
                bolt_movie = bolt_get_movie_from_group_id("", radar_group_id)
                bolt_movie_id = bolt_movie["id"]

                bolt_cast = bolt_get_cast_from_feature_id("", bolt_movie_id)
                for this_cast in bolt_cast:
                    this_cast_character = this_cast["character"]["name"]
                    this_cast_firstname = this_cast["talent"]["firstName"]
                    this_cast_lastname = this_cast["talent"]["lastName"]
                    this_cast_json = {
                        "first": this_cast_firstname,
                        "last": this_cast_lastname,
                        "character": this_cast_character,
                    }
                    cast_list.append(this_cast_json)
                adi_json["actors"]["value"] = cast_list
            except:
                pass

    return adi_json


def parse_normal_series(
    data, default_partners="default_partners_and_categories.json", settings=""
):
    logger_service.info("STARTING - PARSE: {}".format(data))

    try:
        config_dict = config_parse(settings.get("secrets"))
        result = main(data, config_dict)
        logger_service.info("FINISHED - PARSE: %s", data)
        return result

    except Exception as e:
        logger_service.error(
            "ERROR during parsing for data %s: %s - %s", data, type(e).__name__, str(e)
        )

        raise
