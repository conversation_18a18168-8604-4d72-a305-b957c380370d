from typing import Optional

from .DynamoUtils import DynamoDBClient
from .LoggerService import logger_service
from .schemas.dynamodb.core import DynamoDBSettings


class SettingsManagerService:
    def __init__(self, config_table) -> None:
        self.dynamo_client = DynamoDBClient(config_table)
        self.settings: Optional[DynamoDBSettings] = None

    def initialize(
        self, event_project_name: str, readonly: bool = False
    ) -> DynamoDBSettings:
        logger_service.debug("Grabbing configuration")

        items = self.dynamo_client.scan_items()
        filtered_item = next(
            filter(
                lambda item: item["event_project_name"] == event_project_name, items
            ),
            None,
        )

        if filtered_item is None:
            logger_service.error("We don't have a config for %s", event_project_name)
            raise ValueError(f"We don't have a config for {event_project_name}")

        try:
            # tag nested config with project_type for discriminated union
            filtered_item["configuration_rules"]["config"][
                "project_type"
            ] = event_project_name
            # Initialize DynamoDBSettings with the filtered item
            self.settings = DynamoDBSettings(
                **filtered_item,
                debug_readonly=readonly,
            )

            logger_service.debug("DynamoDBSettings loaded from configuration")
            return self.settings

        except Exception as e:
            logger_service.error("Schema validation failed: %s", str(e))
            raise

    def reload(self) -> str:
        return self.initialize(self.settings.event_project_name)
