import time
from datetime import datetime, timedelta


# TimeoutHandler for managing operations with a timeout
class TimeoutHandler:
    def __init__(self, timeout_minutes: int = 10, wait_in_seconds: int = 30):
        self.timeout_minutes = timeout_minutes
        self.wait_in_seconds = wait_in_seconds

    def execute_with_timeout(self, operation, *args, **kwargs):
        start_time = datetime.now()
        timeout_time = start_time + timedelta(minutes=self.timeout_minutes)

        while datetime.now() < timeout_time:
            result = operation(*args, **kwargs)
            if result:
                return result
            time.sleep(self.wait_in_seconds)

        raise TimeoutError("Operation timed out.")
