"""
Token service for Reach API authentication
"""

import base64
import json
import ssl
import time
from typing import Dict

import requests
from requests.adapters import HTT<PERSON><PERSON>pter

from .LoggerService import logger_service  # Correct relative import
from .RequestsHandler import Request<PERSON>and<PERSON>
from .SecretsManagerUtils import Secrets<PERSON>anagerUtils  # Correct relative import


class CustomHttpAdapter(HTTPAdapter):
    """Custom HTTP adapter to enforce TLS 1.2"""

    def init_poolmanager(self, *args, **kwargs):
        context = ssl.create_default_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        context.maximum_version = ssl.TLSVersion.TLSv1_2
        context.set_ciphers("DEFAULT:@SECLEVEL=0")
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE

        kwargs["ssl_context"] = context
        return super(CustomHttpAdapter, self).init_poolmanager(*args, **kwargs)


class ReachTokenService:
    """Service for managing Reach API authentication tokens"""

    def __init__(self):
        """Initialize the token service"""
        self.secrets_manager = SecretsManagerUtils()  # Store the instance
        self.reach_secrets = self.secrets_manager.get_secret_value("reach-secrets")
        self.request_handler = RequestHandler(
            base_url=self.reach_secrets["reach_url"], use_custom_adapter=True
        )

    def create_token(self) -> str:
        """
        Create a new authentication token from Reach API

        Returns:
            str: Authentication token
        """
        token = self.find_token()
        if not token:
            logger_service.info("Getting token..")
            try:
                response = self.request_handler.post(
                    endpoint="/reachengine/api/security/users/login",
                    json={
                        "auth_user": self.reach_secrets["username"],
                        "auth_password": self.reach_secrets["password"],
                    },
                )
                token = response.headers.get("authorization").replace("Bearer ", "")
                self.update_secrets(token)
                return token
            except Exception as error:
                logger_service.error("Error creating token: %s", error)
                raise error
        return token

    def update_secrets(self, token: str) -> None:
        """
        Update token in secrets manager

        Args:
            token: New token to store
        """
        logger_service.info("Saving updated token in secrets manager")
        token_payload = self.decode_token(token)
        exp = token_payload["exp"]

        self.reach_secrets["exp"] = exp
        self.reach_secrets["token"] = token
        secrets = json.dumps(self.reach_secrets)

        try:
            self.secrets_manager.client.put_secret_value(
                SecretId="reach-secrets", SecretString=secrets
            )
        except Exception as error:
            logger_service.error(
                "Something went wrong trying to save token in secrets manager %s", error
            )

    def find_token(self) -> str:
        """
        Find existing valid token

        Returns:
            str: Valid token if found, empty string otherwise
        """
        logger_service.info("Searching for token")
        if "token" in self.reach_secrets:
            logger_service.info("Found token in secrets manage, checking exp")
            exp = int(self.reach_secrets["exp"])
            if exp - time.time() > 10:
                return self.reach_secrets["token"]
            logger_service.info("Token will expire soon creating new one")
        else:
            logger_service.info(
                "Token not available in secrets manager. Creating new one"
            )
        return ""

    @staticmethod
    def decode_token(token: str) -> Dict:
        """
        Decode JWT token

        Args:
            token: JWT token to decode

        Returns:
            Dict containing decoded token payload
        """
        header, payload, signature = token.split(".")
        payload_decoded = base64.urlsafe_b64decode(payload + "==")
        payload_json = json.loads(payload_decoded)
        return payload_json
