import json
from typing import Any, Literal, Optional

from pydantic import BaseModel


class S3ObjectSchema(BaseModel):
    key: str
    size: Optional[int] = None


class S3BucketSchema(BaseModel):
    name: str


class S3Schema(BaseModel):
    bucket: S3BucketSchema
    object: S3ObjectSchema


class S3RecordSchema(BaseModel):
    eventSource: Optional[Literal["aws:s3"]] = None
    eventName: Optional[str] = None
    s3: S3Schema


class DynamoDBAttributeValue(BaseModel):
    """Schema for DynamoDB attribute values"""

    S: Optional[str] = None
    N: Optional[str] = None
    B: Optional[str] = None


class DynamoDBKeysSchema(BaseModel):
    S: Optional[str] = None
    N: Optional[str] = None
    B: Optional[str] = None


class DynamoDBStreamData(BaseModel):
    """Schema for DynamoDB stream data"""

    ApproximateCreationDateTime: Optional[float] = None
    Keys: Optional[dict[str, DynamoDBAttributeValue]] = None
    NewImage: Optional[dict[str, DynamoDBAttributeValue]] = None
    OldImage: Optional[dict[str, DynamoDBAttributeValue]] = None
    SequenceNumber: Optional[str] = None
    SizeBytes: Optional[int] = None
    StreamViewType: Optional[str] = None


class DynamoDBRecordSchema(BaseModel):
    eventSource: Literal["aws:dynamodb"]
    eventName: str
    eventVersion: Optional[str] = None
    eventID: Optional[str] = None
    awsRegion: Optional[str] = None
    dynamodb: Optional[DynamoDBStreamData] = None
    eventSourceARN: Optional[str] = None


class SQSSchema(BaseModel):
    bucket: S3BucketSchema
    object: S3ObjectSchema


class SQSRecordSchema(BaseModel):
    eventSource: Optional[Literal["aws:sqs"]] = None
    attributes: Optional[dict] = None
    messageAttributes: Optional[dict] = None
    body: str


class DynamoDBStreamRecord(BaseModel):
    """Schema for individual DynamoDB stream record data"""

    sidecar_id: Optional[str] = None
    status: Optional[str] = None
    reach_package_id: Optional[str] = None
    updated_at: Optional[str] = None
    created_at: Optional[str] = None
    wonderland_id: Optional[str] = None
    details: Optional[str] = None
    deleted_at: Optional[str] = None


class EventSchema(BaseModel):
    """Main lambda event model class."""

    Records: list[S3RecordSchema | DynamoDBRecordSchema | SQSRecordSchema]

    def get_file_name_from_s3(self) -> str:
        event_info = self.Records[0]
        if isinstance(event_info, S3RecordSchema):
            return event_info.s3.object.key
        return ""

    def get_bucket_name(self) -> str:
        event_info = self.Records[0]
        if isinstance(event_info, S3RecordSchema):
            return event_info.s3.bucket.name
        return ""

    def get_sqs_body(self) -> dict[str, str]:
        event_info = self.Records[0]
        if isinstance(event_info, SQSRecordSchema):
            event_body_dict = event_info.body
            event_body_dict = json.loads(event_body_dict)
            return event_body_dict
        return {}

    def _extract_dynamodb_attribute_value(
        self, attributes: dict[str, DynamoDBAttributeValue], key: str
    ) -> Optional[str]:
        """
        Extract string value from DynamoDB attribute.

        Args:
            attributes: DynamoDB NewImage attributes
            key: Attribute key to extract

        Returns:
            String value or None if not found
        """
        attribute = attributes.get(key)
        return attribute.S if attribute else None

    def _find_dynamodb_record(self) -> Optional[DynamoDBRecordSchema]:
        """
        Find the first DynamoDB record in the event.

        Returns:
            DynamoDBRecordSchema or None if not found
        """
        for record in self.Records:
            if isinstance(record, DynamoDBRecordSchema):
                return record
        return None

    def _extract_new_image_data(
        self, new_image: dict[str, DynamoDBAttributeValue]
    ) -> dict[str, Optional[str]]:
        """
        Extract all relevant fields from DynamoDB NewImage.

        Args:
            new_image: DynamoDB NewImage data

        Returns:
            Dictionary with extracted field values
        """
        fields = [
            "sidecar_id",
            "status",
            "reach_package_id",
            "updated_at",
            "created_at",
            "wonderland_id",
            "details",
            "deleted_at",
        ]

        return {
            field: self._extract_dynamodb_attribute_value(new_image, field)
            for field in fields
        }

    def get_dynamodb_stream_record(self) -> DynamoDBStreamRecord:
        """
        Extract DynamoDB stream record from event NewImage.

        Returns:
            DynamoDBStreamRecord with extracted data or empty record if not found
        """
        # Early return if no DynamoDB record found
        dynamodb_record = self._find_dynamodb_record()
        if not dynamodb_record:
            return DynamoDBStreamRecord()

        # Early return if no dynamodb data
        if not dynamodb_record.dynamodb:
            return DynamoDBStreamRecord()

        # Early return if no NewImage
        new_image = dynamodb_record.dynamodb.NewImage
        if not new_image:
            return DynamoDBStreamRecord()

        # Extract all data and create record using model_validate
        extracted_data = self._extract_new_image_data(new_image)

        return DynamoDBStreamRecord.model_validate(extracted_data)


# class ContextSchema(BaseModel):
#     function_name: str
#     function_version: str
#     invoked_function_arn: str
#     memory_limit_in_mb: int
#     aws_request_id: str
#     log_group_name: str
#     log_stream_name: str
#     identity: Optional[dict] = None
#     client_context: Optional[dict] = None
