# pylint: disable=E0402
from typing import Any, Literal, Union

from pydantic import BaseModel, Field

from .akamai import AkamaiConfig
from .bebanjo import BebanjoConfig
from .express_lane import ExpressLaneConfig
from .gam_connector import GamConnectorConfig
from .reach import ReachConfig
from .wonderland import WonderlandConfig


class ConfigurationRules(BaseModel):
    config: Union[
        ExpressLaneConfig,
        AkamaiConfig,
        WonderlandConfig,
        BebanjoConfig,
        ReachConfig,
        GamConnectorConfig,
    ] = Field(discriminator="project_type")
    rules: dict[str, Any]


class DynamoDBSettings(BaseModel):
    id: int
    debug_readonly: bool = False
    event_project_name: str
    configuration_rules: ConfigurationRules
    frequency: Literal["online"]
    time: str
    retries: int

    created_at: str
    updated_at: str
    deleted_at: str
    status: Literal["active"]
    description: str

    def get_config(
        self,
    ) -> (
        AkamaiConfig
        | ExpressLaneConfig
        | BebanjoConfig
        | WonderlandConfig
        | BebanjoConfig
        | ReachConfig
        | GamConnectorConfig
    ):
        return self.configuration_rules.config

    def get_rules(self) -> dict[str, Any]:
        return self.configuration_rules.rules

    def get_status_table(self) -> str:
        return self.get_config().status_table_name
