from typing import Any, Literal, Optional

from pydantic import AnyHttpUrl, BaseModel


class SqsConfig(BaseModel):
    message_group_id: str
    desired_keys: list[str]
    query: dict[str, Any]
    sqs_topic: str


class ApiValidations(BaseModel):
    status: str
    update_status: Optional[str] = None


class ApiConfig(BaseModel):
    wonderland_validations: ApiValidations
    api_url: str
    main_url: AnyHttpUrl
    token_url: AnyHttpUrl
    # TODO: Verify next field is able to come as None from remote settings (it should not, right?)
    tacdev_reach_table: Optional[str] = None


class WonderlandConfig(BaseModel):
    project_type: Literal["wonderland"]
    secret_key: str
    status_table_name: str
    filename_prefix: str
    dynamo_table: str
    destination_bucket: str
    source_folder: str
    sqs_config: SqsConfig
    api_config: ApiConfig
    landing_bucket: str
    destination_folder: str
