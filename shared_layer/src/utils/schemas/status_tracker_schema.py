from datetime import datetime as dt
from enum import Enum
from typing import Optional
from uuid import uuid4

from pydantic import BaseModel, Field


def current_time():
    return dt.now().isoformat()


class StatusTrackerSchema(BaseModel):
    client_id: str = Field(default_factory=lambda: str(uuid4()))
    created_at: str = Field(default_factory=current_time)
    updated_at: str = Field(default_factory=current_time)
    landing_bucket: Optional[str] = ""
    process_status: Optional[str] = ""
    more_details: Optional[str] = ""


class UpdateItemSchema(BaseModel):
    process_status: str = ""
    more_details: str = ""
    updated_at: str = Field(default_factory=current_time)


class StatusTrackerEnum(Enum):
    RUNNING = "Running"
    COMPLETED = "Completed"
    FAILED = "Failed"
    READY_PROCESS = "Ready to process"
