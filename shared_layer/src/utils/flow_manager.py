import inspect
import json
from typing import Any, Callable, Optional, Protocol, TypeVar

from .LoggerService import logger_service
from .schemas.lambdas import EventSchema


class FlowManager:
    def __init__(
        self,
        name: str,
        tasks: list["TaskProtocol"],
        event: EventSchema,
        global_variables: dict[str, Any],
        successful_message: str = "",
    ) -> None:
        self.name = name
        self.tasks = tasks
        self.event = event
        self.global_variables = global_variables
        self.successful_message = (
            successful_message or f"Flow {name} successfully finished."
        )

    def prepare(self):
        logger_service.info("********** IZMA / %s flow started **********", self.name)
        logger_service.info(
            "IZMA / %s flow with args: \n %s", self.name, self.global_variables
        )

        for task in self.tasks:
            task.prepare(self.event, self.global_variables)  # event, context
        return self

    def perform_task(self):
        result = None
        for task in self.tasks:
            result = task.perform_task(previous=result)
        return result

    def end(self) -> dict[str, Any]:
        logger_service.info("IZMA / %s", self.successful_message)

        return {"statusCode": 200, "body": json.dumps(self.successful_message)}

    def __call__(self) -> dict[str, Any]:
        self.prepare()
        self.perform_task()
        return self.end()


TaskType = TypeVar("TaskType")


class TaskProtocol(Protocol):
    args: tuple[Any, ...] = ()
    kwargs: dict[str, Any] = {}
    task: Callable[..., Any]

    def prepare(self, event: EventSchema, context: dict[str, Any]) -> "TaskProtocol":
        return self

    def perform_task(self, previous: Optional[Any] = None) -> Any:
        raise NotImplementedError


class GenericTask(TaskProtocol):
    def perform_task(self, previous: Optional[Any] = None) -> Any:

        sig = inspect.signature(self.__class__.task)
        param_names = list(sig.parameters.keys())

        if param_names and param_names[0] == "prev":
            return self.__class__.task(previous, *self.args, **self.kwargs)

        return self.__class__.task(*self.args, **self.kwargs)
