"""
Python config file to get ENV vars
"""

import os
import sys

from dotenv import load_dotenv
from pydantic import BaseModel, Field

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
load_dotenv(".env")

AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY")
AWS_SECRET_KEY = os.getenv("AWS_SECRET_KEY")
REGION_NAME = os.getenv("REGION_NAME", "us-west-2")
STAGE = os.getenv("STAGE")
ENV = os.getenv("ENV", "development")
KMS_KEY_ID = os.getenv("KMS_KEY_ID")


# TODO: Refactor this class load variables defined above (AWS_ACCESS_KEY, REGION_NAME, ENV, ...)
#       Also this class should be using pydantic.BaseSettings rather than BaseModel
#       See more: https://docs.pydantic.dev/latest/concepts/pydantic_settings/
class Config(BaseModel):
    TACDEV_EVENT_CONFIG: str = Field(f"tacdev-event-config-{ENV}")
    LOG_TABLE_NAME: str = Field(f"tacdev-logs-{ENV}")


settings_config = Config()  # type: ignore[call-arg]
