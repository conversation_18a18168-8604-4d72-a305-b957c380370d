import uuid
from typing import Dict, Optional

import boto3

from .LoggerService import logger_service


class SQSHandler:
    """
    SQSHandler for managing SQS operations
    args:
        sqs_queue_url: str
    """

    def __init__(self, sqs_queue_url: str) -> None:
        self.sqs_queue_url = sqs_queue_url
        self.sqs_client = boto3.client(
            "sqs",
        )

    def send_message(self, message: Dict, message_group_id: Optional[str] = None):
        try:
            params = {
                "QueueUrl": self.sqs_queue_url,
                "MessageBody": str(message),
                "MessageDeduplicationId": str(uuid.uuid4()),
            }

            # Only include MessageGroupId if it's a FIFO queue
            if message_group_id:
                params["MessageGroupId"] = message_group_id

            response = self.sqs_client.send_message(**params)
            logger_service.info("SQS completed successfully.")
            return response
        except Exception as e:
            logger_service.error(f"Failed to send SQS message: {e}")
            raise RuntimeError(f"Failed to send SQS message: {e}")
