"""
Requests Handler
"""

import base64
import ssl
from typing import Any, Dict, Optional

import requests
from requests.adapters import HTT<PERSON>dapter
from requests.exceptions import HTTPError, RequestException
from requests.models import Response

from .LoggerService import logger_service
from .SecretsManagerUtils import SecretsManagerUtils


class CustomHttpAdapter(HTTPAdapter):
    """Custom HTTP adapter to enforce TLS 1.2"""

    def init_poolmanager(self, *args, **kwargs):
        context = ssl.create_default_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        context.maximum_version = ssl.TLSVersion.TLSv1_2
        context.set_ciphers("DEFAULT:@SECLEVEL=0")
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE

        kwargs["ssl_context"] = context
        return super(CustomHttpAdapter, self).init_poolmanager(*args, **kwargs)


class RequestHandler:
    """
    Class to handle HTTP requests with custom TLS configuration
    """

    def __init__(self, base_url: str, use_custom_adapter: bool = False) -> None:
        """
        Initialize RequestHandler

        Args:
            base_url: Base URL for requests
            use_custom_adapter: Whether to use CustomHttpAdapter for TLS 1.2
        """
        self.base_url = base_url
        self.session = requests.Session()
        if use_custom_adapter:
            self.session.mount("https://", CustomHttpAdapter())
            self.session.verify = False

    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Response:
        """Handles GET requests."""
        try:
            response = self.session.get(
                f"{self.base_url}{endpoint}", params=params, headers=headers
            )

            return response
        except HTTPError as http_err:
            logger_service.error("HTTP error occurred: %s", http_err)
            raise Exception(f"HTTP error occurred: {http_err}")
        except RequestException as req_err:
            logger_service.error("Request error occurred: %s", req_err)
            raise Exception(f"Request error occurred: {req_err}")

    def post(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        json: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Response:
        """Handles POST requests."""
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                params=params,
                data=data,
                json=json,
                headers=headers,
            )
            response.raise_for_status()
            return response
        except HTTPError as http_err:
            logger_service.error("HTTP error occurred: %s", http_err)
            raise Exception(f"HTTP error occurred: {http_err}")
        except RequestException as req_err:
            logger_service.error("Request error occurred: %s", req_err)
            raise Exception(f"Request error occurred: {req_err}")

    def put(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Response:
        """Handles PUT requests."""
        try:
            response = self.session.put(
                f"{self.base_url}{endpoint}", params=params, data=data, headers=headers
            )
            response.raise_for_status()
            return response
        except HTTPError as http_err:
            logger_service.error("HTTP error occurred: %s", http_err)
            raise Exception(f"HTTP error occurred: {http_err}")
        except RequestException as req_err:
            logger_service.error("Request error occurred: %s", req_err)
            raise Exception(f"Request error occurred: {req_err}")


def create_auth_token(url: str, secrets: str) -> Dict:
    try:
        secretsManagerUtils = SecretsManagerUtils()
        secrets_managerUtils = secretsManagerUtils.get_secret_value(secrets)
        username = secrets_managerUtils["username"]
        password = secrets_managerUtils["password"]
        credentials = f"{username}:{password}"
        basic_auth_string = base64.b64encode(credentials.encode()).decode()
        headers = {
            "Authorization": f"Basic {basic_auth_string}",
            "Content-Type": "application/x-www-form-urlencoded",
        }
        payload = f"grant_type=password&username={username}&password={password}"
        response = requests.post(url, headers=headers, data=payload)
        response_json = response.json()
        token = response_json["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        return headers

    except requests.exceptions.RequestException as e:
        logger_service.error("HTTP request error: %s", e)
        raise Exception(f"HTTP request error: {e}")

    except ValueError as e:
        logger_service.error("Value error: %s", e)
        raise Exception(f"Value error: {e}")

    except KeyError as e:
        logger_service.error("Key error: %s", e)
        raise Exception(f"Key error: {e}")

    except Exception as e:
        logger_service.error("Unexpected error: %s", e)
        raise Exception(f"Unexpected error: {e}")
