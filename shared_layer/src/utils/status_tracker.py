from typing import Any
from uuid import uuid4

from .DynamoUtils import DynamoDBClient
from .schemas.status_tracker_schema import (
    StatusTrackerEnum,
    StatusTrackerSchema,
    UpdateItemSchema,
)


class ProcessStatusTracker:
    def __init__(self, status_table_name: str):
        self.dynamo_client = DynamoDBClient(status_table_name)

    def _uuid_obj(self) -> str:
        return str(uuid4())

    def create_initial_status(self, create_item: StatusTrackerSchema) -> None:
        create_item.process_status = StatusTrackerEnum.READY_PROCESS.value
        create = create_item.model_dump()
        self.dynamo_client.create_item(create)

    def mark_as_running(self, client_id: str, details: str = "Event Running") -> None:
        self._update_status(client_id, StatusTrackerEnum.RUNNING.value, details)

    def mark_as_completed(
        self, client_id: str, details: str = "Event Completed"
    ) -> None:
        self._update_status(client_id, StatusTrackerEnum.COMPLETED.value, details)

    def mark_as_failed(self, client_id: str, error_message: str) -> None:
        self._update_status(client_id, StatusTrackerEnum.FAILED.value, error_message)

    def _update_status(self, client_id: str, status: str, details: Any) -> None:
        updates = UpdateItemSchema(
            key={"key": client_id},
            process_status=status,
            more_details=details,
        )
        self.dynamo_client.update_item(
            key={"client_id": client_id}, updates=updates.model_dump()
        )
