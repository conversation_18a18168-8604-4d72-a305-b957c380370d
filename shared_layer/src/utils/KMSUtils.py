"""
KMS Utils file
"""

import boto3
from botocore.exceptions import ClientError

from . import config


class KMSUtils:
    """
    A utility class to interact with AWS KMS for encryption and decryption.

    This class provides methods to encrypt and decrypt data using AWS KMS.

    Attributes:
        client (boto3.client): A Boto3 client for interacting with AWS KMS.
    """

    def __init__(self) -> None:
        """
        Initializes the KMSUtils class with necessary AWS credentials and region.
        """
        aws_region_name = config.REGION_NAME
        self.client = boto3.client(service_name="kms", region_name=aws_region_name)

    def encrypt_data(self, key_id: str, plain_text: str) -> str:
        """
        Encrypts the given plaintext using the specified KMS key.

        Args:
            key_id (str): The KMS key ID or ARN to use for encryption.
            plain_text (str): The plaintext data to encrypt.

        Returns:
            str: The base64-encoded ciphertext.

        Raises:
            ValueError: If the encryption fails.
        """
        try:
            response = self.client.encrypt(
                KeyId=key_id, Plaintext=plain_text.encode("utf-8")
            )
            ciphertext_blob = response["CiphertextBlob"]
            return ciphertext_blob
        except ClientError as e:
            raise ValueError(f"Failed to encrypt data: {e}") from e

    def decrypt_data(self, ciphertext_blob: bytes) -> str:
        """
        Decrypts the given ciphertext using KMS.

        Args:
            ciphertext_blob (bytes): The encrypted ciphertext data.

        Returns:
            str: The decrypted plaintext.

        Raises:
            ValueError: If the decryption fails.
        """
        try:
            response = self.client.decrypt(CiphertextBlob=ciphertext_blob)
            plaintext = response["Plaintext"].decode("utf-8")
            return plaintext
        except ClientError as e:
            raise ValueError(f"Failed to decrypt data: {e}") from e

    def __repr__(self) -> str:
        """
        Returns a string representation of the KMSUtils instance.

        Returns:
            str: String representation of the KMSUtils instance.
        """
        return f"KMSUtils(region_name={self.client.meta.region_name})"
