import logging
import traceback
from typing import Any, Optional

import psutil


class LoggerService:
    LOG_FORMAT = (
        "%(asctime)s - %(levelname)s - %(pathname)s - Line %(lineno)d - %(message)s"
    )
    DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

    def __init__(
        self, log_file_name: Optional[str] = None, log_level: int = logging.DEBUG
    ):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)

        if not self.logger.hasHandlers():
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)

            formatter = logging.Formatter(self.LOG_FORMAT, datefmt=self.DATE_FORMAT)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

            # File handler (optional)
            if log_file_name:
                file_handler = logging.FileHandler(log_file_name)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)

    def _get_resource_usage(self):
        cpu_usage = psutil.cpu_percent(interval=0)
        memory_info = psutil.virtual_memory()
        return (
            f"CPU Usage: {cpu_usage}%, "
            f"Memory Usage: {memory_info.percent}% "
            f"(Used: {memory_info.used / (1024**3):.2f} GB, "
            f"Total: {memory_info.total / (1024**3):.2f} GB)"
        )

    def log(self, level: int, message: str, *args, include_traceback=False, **kwargs):
        try:
            # Format the message with args
            formatted_message = message % args if args else message
        except TypeError:
            # In case of mismatched placeholders and args
            formatted_message = message

        # Add resource usage info
        resource_info = self._get_resource_usage()
        log_message = f"{formatted_message} | {resource_info}"

        # Include traceback if requested
        if include_traceback:
            log_message += f"\n{traceback.format_exc()}"

        # Log the message
        self.logger.log(level, log_message, stacklevel=3, **kwargs)

    def debug(self, message: str, *args: Any) -> None:
        self.log(logging.DEBUG, message, *args)

    def info(self, message: str, *args: Any) -> None:
        self.log(logging.INFO, message, *args)

    def warning(self, message: str, *args: Any) -> None:
        self.log(logging.WARNING, message, *args)

    def error(self, message: str, *args: Any, include_traceback=False) -> None:
        self.log(logging.ERROR, message, *args, include_traceback=include_traceback)

    def critical(self, message: str, *args: Any, include_traceback=False) -> None:
        self.log(logging.CRITICAL, message, *args, include_traceback=include_traceback)


logger_service = LoggerService()
