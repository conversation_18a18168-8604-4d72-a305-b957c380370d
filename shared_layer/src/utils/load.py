import importlib
import os
import re
from datetime import datetime
from typing import Dict, Optional

import pytz

from .LoggerService import logger_service

map_rules = {
    "s3_upload": {
        "allowed_extensions": ["xml", "json", "xlsx", "txt", "pdf", "tar", "yml"],
        "class": "S3Utils",
        "module": "utils.S3Utils",
        "method": "put_object_to_s3",
        "expected_params": ["Bucket", "Key", "Body"],
    },
    "dynamo_upload": {
        "class": "DynamoDBClient",
        "module": "utils.DynamoUtils",
        "method": "",
        "expected_params": ["table", "key", "method", "value"],
    },
}


def load(task: Dict) -> Optional[bool]:
    logger_service.info("load started")

    process = task["load"]["task"]
    rule = map_rules.get(process, None)
    if not rule:
        logger_service.warning("Task %s not allowed", process)
        raise ValueError(f"Task {process} not allowed")
    params = task["load"]["params"]

    # Check that all expected parameters are present (more flexible approach)
    expected_params = rule["expected_params"]
    missing_params = [param for param in expected_params if param not in params]
    if missing_params:
        logger_service.warning(
            "Missing required params in the request: %s", missing_params
        )
        raise KeyError(f"Missing required params in the request: {missing_params}")

    if process == "s3_upload" and "Key" in params:
        file_name = params["Key"]
        _, file_extension = os.path.splitext(file_name)
        file_extension = (
            file_extension[1:] if file_extension.startswith(".") else file_extension
        )

        if file_extension not in rule.get("allowed_extensions", []):
            logger_service.warning(
                "File extension %s is not allowed. Allowed extensions are: %s",
                file_extension,
                rule.get("allowed_extensions", []),
            )
            raise ValueError(
                f"File extension {file_extension} is not allowed. Allowed extensions are: "
                f"{rule.get('allowed_extensions', [])}"
            )

        # Handle filename pattern if provided
        pattern = params.get("filename_pattern")
        if pattern:
            # Extract old name without extension
            old_name = os.path.splitext(os.path.basename(file_name))[0]

            # Process date format pattern if it exists
            date_pattern_match = re.search(r"{current_date:([^}]+)}", pattern)
            if date_pattern_match:
                date_format = date_pattern_match.group(1)
                if "/" in date_format or ":" in date_format:
                    logger_service.warning(
                        "Date format cannot contain '/' or ':' characters as they create unwanted folders in S3"
                    )
                    raise ValueError(
                        "Date format cannot contain '/' or ':' characters as they create unwanted folders in S3. "
                        "Use other separators like '-' or '_' instead."
                    )

                # Get timezone from params (default is already set in lambda_express_lane.py)
                timezone_value = params.pop("timezone")
                timezone_tz = pytz.timezone(timezone_value)
                current_date = datetime.now(timezone_tz)
                pattern = re.sub(
                    r"{current_date:[^}]+}",
                    current_date.strftime(date_format),
                    pattern,
                )

            pattern = pattern.replace("{old_name}", old_name)
            dir_path = os.path.dirname(file_name)
            new_filename = f"{pattern}.{file_extension}"
            params["Key"] = os.path.join(dir_path, new_filename)
            logger_service.info("Updated filename to: %s", params["Key"])

        # Remove filename_pattern from params regardless of the outcome
        params.pop("filename_pattern", None)

    module = rule["module"]
    class_str = rule["class"]
    method_name = rule["method"]

    module_import = importlib.import_module(module)
    cls = getattr(module_import, class_str)

    if process == "s3_upload":
        instance = cls(params["Bucket"])
        method = getattr(instance, method_name)
        method(**params)
    elif process == "dynamo_upload":
        method_name = params["method"]
        instance = cls(params["table"])

        # Handle different DynamoDB update methods
        if method_name == "update_item":
            if "value" not in params:
                raise KeyError("Missing 'value' parameter for update_item method")
            result = getattr(instance, method_name)(params["key"], params["value"])
            return result
        elif method_name == "create_item":
            # For create_item, the value contains the full item
            result = getattr(instance, method_name)(params["value"])
            return result
        elif method_name == "delete_item":
            # For delete_item, only key is needed
            result = getattr(instance, method_name)(params["key"])
            return result
        else:
            raise ValueError(f"Unsupported DynamoDB method: {method_name}")

    logger_service.info("load finished successfully")
    return True


if __name__ == "__main__":
    task_def = {}
    task_def["load"] = {
        "task": "s3_upload",
        "params": {
            "Key": "uploads2/2024_11_28-2.txt",
            "Bucket": "test-express-lane",
            "Body": b"Hola Mundo",
        },
    }
    logger_service.info(load(task_def))
