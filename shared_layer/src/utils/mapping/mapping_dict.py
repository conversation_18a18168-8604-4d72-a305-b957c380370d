from typing import Dict

from pydantic import BaseModel


class MappingDict(BaseModel):
    mapping_values: Dict
    dict_object: (
        Dict  # The outer dict holds the workflows, inner dict holds key-value pairs
    )

    def apply_mapping_logic(self):
        """
        This method applies the mapping logic to dict_object based on the mapping_values.
        If a key in dict_object matches a key in mapping_values, the corresponding value is replaced.
        """
        # Apply mapping logic with a dictionary comprehension
        self.dict_object = {
            workflow: {
                self.mapping_values.get(key, key): value
                for key, value in workflow_data.items()
            }
            for workflow, workflow_data in self.dict_object.items()
            if isinstance(workflow_data, dict)
        }
        return self.dict_object
