from datetime import datetime
from typing import Any
from uuid import uuid4

from pydantic import BaseModel


class ConfigObj(BaseModel):
    object_paths: Any
    object_data: Any = None
    filter_values: Any = None
    mapping_values: Any = None
    data_obj_conn: Any = None
    date_process: datetime = None

    def to_obj(self) -> "ConfigObj":
        """
        Serializes the instance into ConfigObj models,
        """
        rule_id = str(uuid4())
        # Returning a list of ConfigObj instances with dynamically set rule_id
        return ConfigObj(
            rule_id=rule_id,
            object_paths=self.object_paths,
            object_data=self.object_data,
            filter_values=self.filter_values,
            mapping_values=self.mapping_values,
            data_obj_conn=self.data_obj_conn,
            date_process=datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
        )
