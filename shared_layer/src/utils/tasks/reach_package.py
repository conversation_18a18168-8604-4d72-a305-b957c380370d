"""
Task functions for creating reach packages from ADI JSON format.
Implements the business rules from REACH_ADI_JSON_BUSINESS_RULES.md
"""

import json
from typing import Any, Dict, Optional, Union

from utils.LoggerService import logger_service
from utils.rascl.reach_series_toolbox import (
    reach_create_package,
    reach_login,
    reach_search_for_existing_package,
    reach_search_for_series_or_special_or_movie,
)


def create_reach_package_from_adi_json(
    prev: Union[str, Dict[str, Any]],
    reach_engine_login: str,
    reach_engine_password: str,
    reach_engine_address: str,
    bolt_config: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create a reach package from ADI JSON format using RASCL toolbox.
    
    This function implements the business rules defined in REACH_ADI_JSON_BUSINESS_RULES.md
    to create content, seasons, and packages in Reach Engine.
    
    Args:
        prev: ADI JSON data as string or dictionary from previous task
        reach_engine_login: Reach Engine username
        reach_engine_password: Reach Engine password  
        reach_engine_address: Reach Engine server address
        bolt_config: Optional Bolt API configuration for cast/crew data
        
    Returns:
        Dict containing the result of the reach package creation
        
    Raises:
        ValueError: If input data is invalid or required fields are missing
        ConnectionError: If unable to connect to Reach Engine
        Exception: If package creation fails
    """
    try:
        # Parse input data
        if isinstance(prev, str):
            adi_json = json.loads(prev)
        elif isinstance(prev, dict):
            adi_json = prev
        else:
            raise ValueError(f"Invalid input type: {type(prev)}. Expected str or dict.")
            
        logger_service.info("Starting reach package creation from ADI JSON")
        
        # Validate required fields according to business rules
        _validate_required_fields(adi_json)
        
        # Login to Reach Engine
        logger_service.info("Logging into Reach Engine at %s", reach_engine_address)
        reach_token = reach_login(
            reach_engine_login, 
            reach_engine_password, 
            f"https://{reach_engine_address}",
        )
        
        # Check if content already exists
        content_name = adi_json.get("content_name", {}).get("value", "")
        program_type = adi_json.get("program_type", {}).get("value", "")
        network = adi_json.get("network", {}).get("value", "")
        
        # Map program type to Reach content type
        content_type = _map_program_type_to_content_type(program_type)
        
        logger_service.info(
            "Searching for existing content: %s (type: %s, network: %s)",
            content_name, content_type, network
        )
        
        existing_content = reach_search_for_series_or_special_or_movie(
            content_name, content_type, network, reach_token, reach_engine_address
        )
        
        # Check if package already exists
        existing_packages = reach_search_for_existing_package(
            adi_json, reach_token, reach_engine_address
        )

        if existing_packages and existing_packages != "error":
            logger_service.warning(
                "Package already exists for material_id: %s",
                adi_json.get("material_id", {}).get("value", "")
            )
            return {
                "status": "package_exists",
                "package_id": existing_packages[0].get("id"),
                "message": "Package already exists in Reach Engine"
            }

        # Create the reach package directly using the reach_create_package function
        # This is a more focused approach that just creates the package
        logger_service.info("Creating reach package using reach_create_package")

        # First ensure content exists by searching for it
        content_name = adi_json.get("content_name", {}).get("value", "")
        program_type = adi_json.get("program_type", {}).get("value", "")
        network = adi_json.get("network", {}).get("value", "")
        content_type = _map_program_type_to_content_type(program_type)

        existing_content = reach_search_for_series_or_special_or_movie(
            content_name, content_type, network, reach_token, reach_engine_address
        )

        if not existing_content:
            raise Exception(f"Content '{content_name}' not found in Reach Engine. Content must be created first.")

        # Create the package
        new_package = reach_create_package(adi_json, reach_token, reach_engine_address)
        package_id = new_package.get("id")

        logger_service.info("Successfully created reach package with ID: %s", package_id)

        result = {
            "package_id": package_id,
            "package_name": new_package.get("name"),
            "content_id": existing_content[0].get("id") if existing_content else None,
        }
        
        logger_service.info("Successfully created reach package")
        
        return {
            "status": "success",
            "result": result,
            "message": "Reach package created successfully"
        }
        
    except json.JSONDecodeError as e:
        error_msg = f"Invalid JSON format in ADI data: {str(e)}"
        logger_service.error(error_msg)
        raise ValueError(error_msg)
        
    except Exception as e:
        error_msg = f"Error creating reach package: {str(e)}"
        logger_service.error(error_msg)
        raise Exception(error_msg)


def _validate_required_fields(adi_json: Dict[str, Any]) -> None:
    """
    Validate required fields according to REACH_ADI_JSON_BUSINESS_RULES.md
    
    Args:
        adi_json: The ADI JSON data to validate
        
    Raises:
        ValueError: If required fields are missing or invalid
    """
    # Critical fields that cause system failure if missing
    critical_fields = [
        "program_type",
        "content_name", 
        "network",
        "material_id"
    ]
    
    for field in critical_fields:
        if field not in adi_json or not adi_json[field].get("value"):
            raise ValueError(f"Critical field '{field}' is missing or empty")
    
    # Important fields that should generate warnings
    important_fields = [
        "episode_c_type",
        "episode_d_type", 
        "country",
        "content_type"
    ]
    
    for field in important_fields:
        if field not in adi_json or not adi_json[field].get("value"):
            logger_service.warning(f"Important field '{field}' is missing or empty")
    
    # Validate program type
    program_type = adi_json.get("program_type", {}).get("value", "").lower()
    valid_program_types = ["series", "movie", "special", "acquired"]
    if program_type not in valid_program_types:
        raise ValueError(f"Invalid program_type: {program_type}. Must be one of {valid_program_types}")


def _map_program_type_to_content_type(program_type: str) -> str:
    """
    Map ADI JSON program_type to Reach Engine content type.
    
    Args:
        program_type: The program type from ADI JSON
        
    Returns:
        The corresponding Reach Engine content type
    """
    program_type_lower = program_type.lower()
    
    if program_type_lower in ["series", "acquired"]:
        return "Series"
    elif program_type_lower in ["movie", "special"]:
        return "Movie/Special"
    else:
        # Default to Series for unknown types
        logger_service.warning(f"Unknown program_type: {program_type}, defaulting to Series")
        return "Series"


def _prepare_rascl_config(bolt_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare configuration dictionary for RASCL toolbox.
    
    Args:
        bolt_config: Bolt API configuration
        
    Returns:
        Configuration dictionary for RASCL toolbox
    """
    return {
        "bolt_bearer_token": bolt_config.get("bearer_token", ""),
        "auth_z_client_id": bolt_config.get("client_id", ""),
        "auth_z_client_secret": bolt_config.get("client_secret", ""),
        "auth_z_grant_type": bolt_config.get("grant_type", "client_credentials"),
        "auth_z_scope": bolt_config.get("scope", ""),
        "gracenote_api_key": bolt_config.get("gracenote_api_key", ""),
    }
