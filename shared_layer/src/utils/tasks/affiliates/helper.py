from typing import Dict, List, Tuple

from utils.tasks.affiliates.nested_obj import FlattenObj, FlattenRules


class AffiliateTasksHelper:
    def __init__(self, raw_data: Dict) -> None:
        self.raw_data = raw_data

    def _flatten_raw_obj_helper(self) -> List[Tuple]:
        flatten_obj = FlattenObj(self.raw_data)
        flatten_raw_data = flatten_obj.flatten_structure_obj()
        
        return flatten_raw_data
 
    def _flatten_obj_helper(self, matched_affiliate: Dict) -> List[Tuple]:
        flatten_obj = FlattenObj(matched_affiliate)
        flatten_data = flatten_obj.flatten_structure_obj()
        
        return flatten_data
    
    def flatten_raw_data_rules_helper(self) -> FlattenRules:
        flatten_obj_data = self._flatten_raw_obj_helper()
        flatten_raw_data_rules = FlattenRules(flatten_obj_data)
        
        return flatten_raw_data_rules
    
    def flatten_rules_helper(self, matched_affiliate: Dict) -> FlattenRules:
        flatten_obj_data = self._flatten_obj_helper(matched_affiliate)
        flatten_rules = FlattenRules(flatten_obj_data)
        
        return flatten_rules
