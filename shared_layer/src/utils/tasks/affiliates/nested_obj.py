from collections import OrderedDict, deque
from typing import Any, List, Tuple, Union

from glom import glom, assign, delete


class GlomObj:
    def __init__(self):
        pass
    
    def glom_obj(self, data: Any, path: str, default=None) -> Any:
        return glom(data, path, default=default)
    
    def glom_delete_obj(self, data: Any, path: str, index: Any) -> Any:
        return delete(data, f'{path}.{index}')
    
    def glom_filter_by_list(self, value_by: Any, value_in: List) -> Any:
        return [
            entry['value'] for entry in value_by
            if entry.get('@name') in value_in
        ]
        
    def glom_filter_by_value(self, value_by: Any, value_in: str) -> Any:
        # Use a generator to grab the first match, with None as a fallback
        return next(
            (entry['value'] for entry in value_by
            if entry.get('@name') == value_in),
            None
    )
        
    def glom_assign_obj(self, data, update_path, obj_index, update_value, new_updated_value):
        assign(data, f'{update_path}.{obj_index}.{update_value}', new_updated_value)   

    def glom_add_obj(self, app_data_list: glom, value):
        new_entry = OrderedDict([('@App', 'MOD'), ('@Name', 'Category'), ('@Value', value)])
        app_data_list.append(new_entry)
    

class FlattenObj:
    def __init__(self, data):
        self.data = data
        
    def flatten_structure_obj(self, bfs=False):
        result = []
        stack = deque([([], self.data)])  # deque instead of list

        while stack:
            if bfs:
                path, current = stack.popleft()  # BFS
            else:
                path, current = stack.pop()      # DFS (original behavior)

            if isinstance(current, dict):
                for k in reversed(list(current.keys())):
                    stack.append((path + [k], current[k]))
            elif isinstance(current, list):
                for idx in reversed(range(len(current))):
                    elem = current[idx]
                    stack.append((path + [idx], elem))
            else:
                result.append((path, current))

        result.sort(key=lambda x: x[0])
        return result

class FlattenRules:
    def __init__(self, flatten_data):
        self.flattened_data = flatten_data

    def filter_sibling_tuples_by_value(self, target_value):
        """
        Returns all tuples (path, value) from flattened_data that share the same parent path
        (excluding the last key) with any tuple whose value matches target_value.

        For example, if one tuple is:
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Name'], 'Category')
        and value == 'Category',
        this will also return:
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Value'], ...)
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Mod'], ...)

        :param target_value: the value to match exactly
        :return: list of tuples (path, value) that share the same parent as any matching tuple
        """
        # Step 1: Identify all parent paths that contain the target value
        matching_parent_paths = set()
        for path, value in self.flattened_data:
            if value == target_value:
                parent_path = tuple(path[:-1])  # path minus the last key
                matching_parent_paths.add(parent_path)

        # Step 2: Return all entries that share the same parent path
        result = [
            (path, value)
            for path, value in self.flattened_data
            if tuple(path[:-1]) in matching_parent_paths
        ]

        return result          

    def filter_sibling_tuples_by_key(self, target_key):
        """
        Returns all tuples (path, value) from flattened_data that share the same parent path
        (excluding the last key) with any tuple whose key matches target_key.

        For example, if one tuple is:
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Name'], 'Category')
        and key == '@Name',
        this will also return:
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Value'], ...)
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Mod'], ...)

        :param target_key: the key to match exactly (last element of path)
        :return: list of tuples (path, value) that share the same parent as any matching tuple
        """
        # Step 1: Identify all parent paths that contain the target key
        matching_parent_paths = set()
        for path, _ in self.flattened_data:
            if path[-1] == target_key:
                parent_path = tuple(path[:-1])  # path minus the last key
                matching_parent_paths.add(parent_path)

        # Step 2: Return all entries that share the same parent path
        result = [
            (path, value)
            for path, value in self.flattened_data
            if tuple(path[:-1]) in matching_parent_paths
        ]

        return result

    def filter_single_path_by_key(self, target_key):
        """
        Returns only the tuples (path, value) from flattened_data whose last path element
        matches the target_key exactly.

        For example, if target_key == '@Name',
        this returns only tuples like:
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Name'], 'Category')

        No siblings or other keys are returned.

        :param target_key: the key to match exactly (last element of path)
        :return: list of tuples (path, value) that match target_key exactly
        """
        return [(path, value) for path, value in self.flattened_data if path[-1] == target_key]

    def filter_single_path_by_value(self, target_value):
        """
        Returns only the tuples (path, value) from flattened_data whose value
        matches the target_value exactly.

        For example, if target_value == 'Category',
        this returns only tuples like:
        (['master', 'ADI', 'Asset', 'Metadata', 'App_Data', 25, '@Name'], 'Category')

        :param target_value: the value to match exactly
        :return: list of tuples (path, value) that match target_value exactly
        """
        return [(path, value) for path, value in self.flattened_data if value == target_value]

    def replace_tuples_value(self, t_list, new_value, filter_path: str = None):
        if filter_path:
            return [(path, new_value if filter_path in path  else value) for path, value in t_list]
        else:
            return [(path, new_value) for path, _ in t_list]
    
    def extract_tuple_value(self, t_list, filter_path: str):
        for path, value in t_list:
            if filter_path in path:
                return value
        return None
    
    def extract_specific_tuple(self, t_list, key: str):
        """
        Returns a list of (path, value) tuples where 'key' is found in the path.
        Path can be a string, list, or tuple.
        """
        return [
            (path, value)
            for path, value in t_list
            if (isinstance(path, (list, tuple)) and key in path) or
            (isinstance(path, str) and key in path)
        ]
    
    def update_obj(self, data: Union[dict, list], updates: List[Tuple[List[Union[str, int]], Any]]) -> None:
        for path, new_value in updates:
            current = data
            for key in path[:-1]:
                if isinstance(current, dict):
                    current = current.get(key)
                elif isinstance(current, list) and isinstance(key, int) and 0 <= key < len(current):
                    current = current[key]
                else:
                    raise KeyError(f"Invalid path segment: {key} in {path}")
            
            # Set the value at the last key
            last_key = path[-1]
            if isinstance(current, dict) and isinstance(last_key, str):
                current[last_key] = new_value
            elif isinstance(current, list) and isinstance(last_key, int) and 0 <= last_key < len(current):
                current[last_key] = new_value
            else:
                raise KeyError(f"Invalid final key: {last_key} in {path}")
