import json
from typing import Any

from pydantic import BaseModel

from ..LoggerService import logger_service
from .transforms import update_dynamo_record_status


def validate_and_update_dynamodb_record_status(
    record: dict[str, Any], table_name: str, key: dict[str, Any], sidecar: str
) -> dict[str, Any]:
    """
    Validate a DynamoDB record for required fields and status, updating status to 'Error' if validation fails.

    Args:
        record (dict[str, Any]): The DynamoDB record to validate.
        table_name (str): Name of the DynamoDB table.
        key (dict[str, Any]): Key to identify the record in DynamoDB.
        sidecar (str): Sidecar ID for logging and status updates.

    Returns:
        dict[str, Any]: The validated record.

    Raises:
        KeyError: If the record is None or missing required fields.
        ValueError: If the record status is not 'wl_successful_ingestion'.
    """
    if not record:
        logger_service.error("No sidecar %s record found in dynamo", sidecar)
        update_dynamo_record_status(table_name, key, "Error", sidecar)
        raise KeyError(f"No sidecar record found in dynamo for sidecar {sidecar}")

    required_fields = [
        "status",
        "attribute_name",
        "reach_package_id",
        "wonderland_id",
    ]
    if any(field not in record for field in required_fields):
        missing = [f for f in required_fields if f not in record]
        logger_service.error("Missing fields in record: %s", missing)
        update_dynamo_record_status(table_name, key, "Error", sidecar)
        raise KeyError(f"Missing required fields: {missing}")

    if record["status"] != "wl_successful_ingestion":
        logger_service.error(
            "Status of sidecar %s is not completed. Current status: %s",
            record["reach_package_id"],
            record["status"],
        )
        raise ValueError(
            f"Status of sidecar {record['reach_package_id']} is not 'wl_successful_ingestion', current status: {record['status']}"
        )

    return record


def should_skip_affiliate_content(
    affiliate: str, global_variables: dict[str, Any]
) -> bool:
    """
    Determine if content for a given affiliate should be skipped based on configuration and content value.

    Args:
        affiliate (str): The affiliate name.
        global_variables (dict[str, Any]): Global configuration and content context.

    Returns:
        bool: True if content should NOT be skipped, False if it should be skipped.
    """
    configurations = global_variables.get("affiliate_settings", {})
    if not configurations:
        logger_service.warning("No configurations found for %s affiliate", affiliate)

    original_source = global_variables.get("original_source", {})
    package_info = original_source.get("master", {}).get("package_info", {})

    skip_content = configurations.get("skip_content", {})
    validation = skip_content.get("validation")
    skip_content_value = skip_content.get("skip_content_value")
    values_skip = skip_content.get("values_skip", [])

    content_value = package_info.get(skip_content_value)

    if not validation or content_value not in values_skip:
        logger_service.warning(
            "for the affiliate %s, found values to skip: %s, content value: %s ",
            values_skip,
            content_value,
            affiliate,
        )
        return True
    logger_service.info("not found metadata to skip for affiliate: %s", affiliate)
    return False


def validate_data_against_pydantic_schema(
    prev: str | dict[str, Any], *, model_schema: type[BaseModel]
) -> dict[str, Any]:
    """
    Validate input data against a provided Pydantic schema, returning the validated data as a dictionary.

    Args:
        prev (str | dict[str, Any]): The data to validate, as a JSON string or dictionary.
        model_schema (type[BaseModel]): The Pydantic model class to validate against.

    Returns:
        dict[str, Any]: The validated data as a dictionary.

    Raises:
        pydantic.ValidationError: If validation fails.
        json.JSONDecodeError: If data is a string and cannot be parsed as JSON.
        ValueError: If model_schema is not provided.
    """
    if not model_schema:
        raise ValueError("model_schema must be provided")

    parsed_data: dict[str, Any]
    if isinstance(prev, str):
        parsed_data = json.loads(prev)
    else:
        parsed_data = prev

    validated_data = model_schema.model_validate(parsed_data)
    logger_service.info(
        "Data successfully validated against schema: %s",
        model_schema.__name__,
    )
    return validated_data.model_dump()
