import json
from typing import Any, Dict, List, Optional

import boto3

from ..LoggerService import logger_service
from ..schemas.dynamodb.gam_connector import GamConnectorConfig
from ..SecretsManagerUtils import SecretsManagerUtils
from ..SQSHandler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def upload_file_to_linode(prev: Dict[str, Any], secret_key: str, filename: str) -> bool:
    """
    Upload a file to a Linode S3-compatible storage bucket.

    Args:
        prev (Dict[str, Any]): Previous step data containing the file to upload in the 'data' key.
        secret_key (str): The key to retrieve secret credentials from AWS Secrets Manager.
        filename (str): The destination filename (object key) in the Linode bucket.

    Returns:
        bool: True if the upload was successful.

    Raises:
        Exception: If the upload fails due to invalid credentials or any other S3 client error.
    """
    secret_manager = SecretsManagerUtils()
    credentials = secret_manager.get_secret_value(secret_key)
    linode_session = boto3.session.Session()
    linode_obj_config = {
        "aws_access_key_id": credentials.get("access_key"),
        "aws_secret_access_key": credentials.get("secret_key"),
        "endpoint_url": f"https://{credentials.get('endpoint')}",
    }
    client = linode_session.client("s3", **linode_obj_config)
    client.upload_fileobj(prev.get("data"), credentials.get("bucket_name"), filename)
    return True


def get_linode_file_metadata(
    secret_key: str, filename: str
) -> Optional[Dict[str, Any]]:
    """
    Retrieve metadata for a file in the Linode bucket using credentials from SecretsManager.

    Args:
        secret_key (str): The key to retrieve secret credentials from AWS Secrets Manager.
        filename (str): The file name (object key) to look up in the bucket.

    Returns:
        Optional[Dict[str, Any]]: Metadata of the file if found, None if the file does not exist.

    Raises:
        Exception: If the operation fails due to invalid credentials or any other S3 client error
                  besides 404 (file not found).
    """
    secret_manager = SecretsManagerUtils()
    credentials = secret_manager.get_secret_value(secret_key)
    linode_session = boto3.session.Session()
    linode_obj_config: dict[str, Any] = {
        "aws_access_key_id": credentials.get("access_key"),
        "aws_secret_access_key": credentials.get("secret_key"),
        "endpoint_url": f"https://{credentials.get('endpoint')}",
    }
    client = linode_session.client("s3", **linode_obj_config)
    try:
        metadata = client.head_object(
            Bucket=credentials.get("bucket_name"), Key=filename
        )
        return metadata
    except client.exceptions.ClientError as e:
        error_code = e.response["Error"].get("Code")
        if error_code in ("404", "NoSuchKey"):
            return None
        raise e


def send_message_to_sqs(
    global_variables: Dict[str, Any], sqs_messages: List[Dict[str, Any]]
) -> None:
    """
    Send a message to an AWS SQS queue with specified content.

    Args:
        global_variables (Dict[str, Any]): Configuration dict containing:
            - 'sqs_topic': The SQS queue URL or ARN
            - 'desired_keys': Keys to extract from sqs_messages for logging
            - 'message_group_id': The FIFO queue message group ID
        sqs_messages (List[Dict[str, Any]]): Message content to send to SQS.

    Returns:
        None

    Raises:
        Exception: If sending the message fails for any reason.
    """
    sqs_handler = SQSHandler(
        global_variables.get("config").sqs_config.sqs_topic,
    )
    if not sqs_messages:
        logger_service.debug(
            "No messages to send to SQS (empty sqs_messages): %s", sqs_messages
        )
        return
    try:
        if isinstance(global_variables.get("config"), GamConnectorConfig):

            sqs_message_json = json.dumps(sqs_messages)
        else:
            sqs = {
                key: sqs_messages[key]
                for key in global_variables.get("desired_keys")
                if key in sqs_messages
            }
            sqs_message_json = json.dumps(sqs_messages)

        sqs_handler.send_message(
            sqs_message_json, global_variables.get("config").sqs_config.message_group_id
        )
        logger_service.info(f"Sending message: {sqs_message_json}.")

    except Exception as e:
        logger_service.error("Failure: %s.", e)
        raise Exception(f"Failure: {e}.")
