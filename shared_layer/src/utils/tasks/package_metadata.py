from typing import Any

import requests

from ..LoggerService import logger_service
from ..schemas.lambdas import DynamoDBStreamRecord
from ..token_service import <PERSON><PERSON>ttp<PERSON>dapter


def fetch_reach_package_metadata(
    record: DynamoDBStreamRecord, token: str, url: str
) -> dict[str, Any]:
    """
    Retrieve package metadata from Reach API for a DynamoDB stream record

    Args:
        record: DynamoDB stream record containing reach_package_id
        token: Authentication token for Reach API
        url: Base URL for the Reach API

    Returns:
        Dict containing the package metadata for the record

    Raises:
        ValueError: If the API response status code is not 200
    """
    # Extract reach_package_id from DynamoDB stream record
    reach_package_id = record.reach_package_id

    if not reach_package_id:
        logger_service.warning("No reach_package_id found in record: %s", record)
        return {}

    logger_service.info("Getting package %s metadata", reach_package_id)
    logger_service.info("url: %s", url)

    url_get = f"{url}/reachengine/api/abc/packages/{reach_package_id}"
    session = requests.Session()
    session.mount("https://", CustomHttpAdapter())

    response = session.get(
        url_get, verify=False, headers={"Authorization": f"Bearer {token}"}
    )

    if response.status_code != 200:
        logger_service.error(
            "Something went wrong trying to get package metadata response from reach: %s",
            response.text,
        )
        raise ValueError(f"Response from reach is not success {response.status_code}")

    return response.json()


def update_reach_package_metadata(
    record: DynamoDBStreamRecord,
    token: str,
    url: str,
    metadata: dict[str, Any],
    wonderland_delivery_status_mapping: dict[str, Any],
) -> bool:
    """
    Update package metadata attribute in Reach API for a record

    Args:
        record: DynamoDB stream record containing reach_package_id
        token: Authentication token for Reach API
        url: Base URL for the Reach API
        metadata: Package metadata dictionary from previous task
        wonderland_delivery_status_mapping: Mapping of status values to wonderland IDs from configuration

    Returns:
        bool: True if update was successful, False otherwise
    """
    if not metadata:
        logger_service.error("No metadata provided for updating package")
        return False

    reach_package_id = record.reach_package_id

    if not reach_package_id:
        logger_service.warning("No reach_package_id found in record: %s", record)
        return False

    logger_service.info("Updating package %s", reach_package_id)

    url_post = f"{url}/reachengine/api/abc/packages/{reach_package_id}"

    metadata["metadata"]["properties"]["wonderlandDeliveryStatus"]["value"] = (
        wonderland_delivery_status_mapping[record.status]
    )

    session = requests.Session()
    session.mount("https://", CustomHttpAdapter())

    response = session.post(
        url_post,
        json=metadata,
        verify=False,
        headers={"Authorization": f"Bearer {token}"},
    )

    if response.status_code != 200:
        logger_service.error(
            "Status code from reach endpoint is %s for package %s",
            response.status_code,
            reach_package_id,
        )
        logger_service.error("Reason %s", response.text)
        return False

    return True
