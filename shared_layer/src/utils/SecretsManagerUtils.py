"""
Secrets manager class
"""

import json
from typing import Any, Dict

import boto3
from botocore.exceptions import ClientError

from . import config
from .LoggerService import logger_service


class SecretsManagerUtils:
    """
    Class to interact with AWS Secrets Manager.

    This class provides methods to retrieve secrets stored in AWS Secrets Manager
    using specific access keys and secret keys for accessing the AWS services.

    Attributes:
        client (boto3.client): A Boto3 client for interacting with AWS Secrets Manager.
    """

    def __init__(self) -> None:
        """
        Initializes the SecretsManagerUtils class with necessary AWS credentials and region.
        """
        region_name = config.REGION_NAME
        self.client = boto3.client(
            service_name="secretsmanager", region_name=region_name
        )

    def get_secret_value(self, secret_name: str) -> Dict[str, Any]:
        """
        Retrieves the value of a secret from AWS Secrets Manager.

        Args:
            secret_name (str): The name of the secret to retrieve.
            secret_value (str): The name of the value from the secret to retrieve.

        Returns:
            Any: A variable containing the secret value.

        Raises:
            ValueError: If the secret retrieval fails.
        """
        try:
            secret_value_response = self.client.get_secret_value(SecretId=secret_name)
            if "SecretString" in secret_value_response:
                return json.loads(secret_value_response["SecretString"])
            logger_service.warning(
                "SecretString not found in response for secret: %s", secret_name
            )
            raise ValueError(
                f"SecretString not found in response for secret: {secret_name}"
            )
        except (ClientError, ValueError) as e:
            logger_service.error("Failed to retrieve secret %s: %s", secret_name, e)
            raise ValueError(f"Failed to retrieve secret {secret_name}: {e}") from e
