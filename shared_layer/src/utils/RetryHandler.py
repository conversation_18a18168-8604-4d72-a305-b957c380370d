import time
from typing import Any, Callable, Dict, Optional, Union

from .LoggerService import logger_service
from .mapping.retry_object_record import RetryRecord


class RetryHandler:
    def __init__(self, max_retries: int = 1, delay: int = 2) -> None:
        """
        Initialize the retry handler.

        :param max_retries: Maximum number of retries allowed
        :param delay: Initial delay between retries in seconds
        """
        self.max_retries = max_retries
        self.delay = delay
        self.records: Dict[str, RetryRecord] = {}

    def _handle_retry(self, retries: int) -> None:
        """Handles retry logic including delay and backoff."""
        if retries < self.max_retries:
            logger_service.info(
                f"--------------- Retrying... Attempt {retries + 1} with delay {self.delay:.2f}s. ---------------"
            )
            time.sleep(self.delay)
        else:
            logger_service.warning(
                f"Failed after {self.max_retries} retries with records."
            )

    def execute(self, func: Callable, *args, **kwargs) -> Union[Optional[Any], None]:
        """
        Execute the given function with retry logic.

        :param func: Callable function or method
        :param args: Positional arguments to pass to the function
        :param kwargs: Keyword arguments to pass to the function
        :return: The result of the function if successful
        """
        logger_service.info(
            f"--------------- 1st Attempt... with delay {self.delay:.2f}s. ---------------"
        )
        retries = 1
        while retries <= self.max_retries:
            try:
                result = func(*args, **kwargs)
                self.records[func.__name__] = RetryRecord(retries=retries, status=True)
                logger_service.info(
                    f"Successful attempt: {self.records[func.__name__]}"
                )
                return result
            except Exception:
                self.records[func.__name__] = RetryRecord(
                    retries=retries,
                    status=False,
                )
                self._handle_retry(retries)
                retries += 1

        logger_service.warning(f"Final retry result: {self.records[func.__name__]}")
        return None


# ***************
