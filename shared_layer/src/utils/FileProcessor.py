import io
import tarfile
import tempfile
import zipfile
from pathlib import Path
from typing import List, Optional, Union

from .LoggerService import logger_service


class FileProcessor:
    """
    A class to handle compressed file formats like .tar and .zip.
    """

    def __init__(self):
        """
        Initialize the file processor.
        :param base_dir: Base directory for file operations.
        """

    def extract_tar(
        self, tar_path: Union[str, Path], extract_to: Optional[Union[str, Path]] = None
    ):
        """
        Extracts a .tar file.
        :param tar_path: Path to the .tar file.
        :param extract_to: Directory to extract the contents to. Defaults to the tar file name without extension.
        """
        tar_path = Path(tar_path)
        self._validate_file_exists(tar_path, ".tar")

        # Default extraction path is the tar filename without extension
        extract_to = Path(extract_to) if extract_to else tar_path.with_suffix("")

        # Ensure the extraction directory exists
        extract_to.mkdir(parents=True, exist_ok=True)

        try:
            with tarfile.open(tar_path, "r") as tar:
                tar.extractall(path=extract_to)
            logger_service.info("Extracted %s to %s", tar_path, extract_to)
        except tarfile.TarError as e:
            logger_service.error("Failed to extract %s: %s", tar_path, e)
        except Exception as e:
            logger_service.error(
                "An unexpected error occurred while extracting %s: %s", tar_path, e
            )

    def create_tar(self, source_dir: Union[str, Path], output_file: Union[str, Path]):
        source_dir = Path(source_dir)
        self._validate_directory_exists(source_dir)
        output_file = Path(output_file)

        try:
            with tarfile.open(output_file, "w") as tar:
                for file in source_dir.iterdir():
                    tar.add(
                        file, arcname=file.name
                    )  # Avoids including the parent directory
            logger_service.info("Created tar file %s", output_file)
        except Exception as e:
            logger_service.error("Failed to create tar file %s: %s", output_file, e)

    def create_tar_from_file(self, xml_data: str, file_key: str) -> bytes:
        """
        Converts XML string data into a tar file.

        Args:
            xml_data (str): The XML data as a string.
            file_key (str): The name of the XML file to be stored in the tar.

        Returns:
            bytes: The tar file as a byte stream.
        """
        tar_stream = io.BytesIO()

        # Create a tarfile in memory
        with tarfile.open(fileobj=tar_stream, mode="w") as tar:
            # Create an in-memory file object for the XML data
            xml_file = io.BytesIO(xml_data.encode("utf-8"))
            # Add the XML data to the tarfile
            tarinfo = tarfile.TarInfo(name=file_key)
            tarinfo.size = len(xml_file.getvalue())
            tar.addfile(tarinfo, fileobj=xml_file)

        # Reset the stream position to the beginning
        tar_stream.seek(0)
        return tar_stream.read()

    def extract_zip(
        self, zip_path: Union[str, Path], extract_to: Optional[Union[str, Path]] = None
    ):
        """
        Extracts a .zip file.
        :param zip_path: Path to the .zip file.
        :param extract_to: Directory to extract the contents to. Defaults to the zip file name without extension.
        """
        zip_path = Path(zip_path)
        self._validate_file_exists(zip_path, ".zip")
        extract_to = Path(extract_to) if extract_to else zip_path.with_suffix("")

        try:
            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                files = zip_ref.namelist()
                if not files:
                    logger_service.warning("The zip file %s is empty.", zip_path)
                zip_ref.extractall(path=extract_to)
                logger_service.info(
                    "Extracted %s files from %s to %s", len(files), zip_path, extract_to
                )
        except Exception as e:
            logger_service.error("Failed to extract %s: %s", zip_path, e)

    def create_zip(self, source_dir: Union[str, Path], output_file: Union[str, Path]):
        """
        Creates a .zip file from a directory.
        :param source_dir: Directory to compress.
        :param output_file: Path to the output .zip file.
        """
        source_dir = Path(source_dir)
        self._validate_directory_exists(source_dir)
        output_file = Path(output_file)

        try:
            with zipfile.ZipFile(output_file, "w", zipfile.ZIP_DEFLATED) as zipf:
                # Use rglob to find all files recursively
                for file_path in source_dir.rglob("*"):
                    if file_path.is_file():  # Ensure it's a file and not a directory
                        zipf.write(file_path, arcname=file_path.relative_to(source_dir))
            logger_service.info("Created zip file %s", output_file)
        except Exception as e:
            logger_service.error("Failed to create zip file %s: %s", output_file, e)

    def is_compressed_file(self, file_path: Union[str, Path]) -> bool:
        """
        Checks if a file is a .tar or .zip file.
        :param file_path: Path to the file.
        :return: True if file is .tar or .zip, otherwise False.
        """
        file_path = Path(file_path)
        return file_path.suffix in [".tar", ".zip"]

    def _validate_file_exists(
        self, file_path: Path, expected_suffix: Optional[str] = None
    ):
        if not file_path.is_file():
            logger_service.error("File not found: %s", file_path)
        if expected_suffix and file_path.suffix != expected_suffix:
            logger_service.error(
                "Expected a %s file, got: %s", expected_suffix, file_path.suffix
            )

    def _validate_directory_exists(self, dir_path: Path):
        if not dir_path.is_dir():
            logger_service.error("Directory not found: %s", dir_path)

    def extract_multiple_files(
        self,
        file_paths: List[Union[str, Path]],
        extract_to: List[Optional[Union[str, Path]]],
    ):
        """
        Extracts multiple .tar or .zip files to specific directories.
        :param file_paths: List of file paths to extract.
        :param extract_to: List of directories to extract the files to. Each index corresponds to a file path.
        """
        logger_service.info("extract_multiple_files started")
        for file_path, extract_to_dir in zip(file_paths, extract_to):
            if str(file_path).endswith(".tar"):
                self.extract_tar(file_path, extract_to_dir)
            elif str(file_path).endswith(".zip"):
                self.extract_zip(file_path, extract_to_dir)
        logger_service.info("extract_multiple_files finished")

    def create_multiple_files(
        self, source_dirs: List[Union[str, Path]], output_files: List[Union[str, Path]]
    ):
        logger_service.info("create_multiple_files started")
        for source_dir, output_file in zip(source_dirs, output_files):
            if str(output_file).endswith(".tar"):
                self.create_tar(source_dir, output_file)
            elif str(output_file).endswith(".zip"):
                self.create_zip(source_dir, output_file)
        logger_service.info("create_multiple_files finished")
