"""
S3 utils file
"""

from typing import Any, Dict, Iterator, Optional

import boto3
import botocore

from . import config
from .LoggerService import logger_service


class S3Utils:
    """
    A class to interact with AWS S3 to get objects from a bucket.

    Attributes:
        bucket_name (str): The name of the S3 bucket.
        s3_client (boto3.client): The Boto3 S3 client.
    """

    def __init__(self, bucket_name) -> None:
        """
        Initialize an S3Client.
        """
        region_name = config.REGION_NAME
        self.bucket_name = bucket_name
        self.s3_client = boto3.client("s3", region_name=region_name)

    def get_object(
        self, key: str, range_header: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get an object from the S3 bucket.

        Args:
            key (str): The key of the object to retrieve.
            range_header (str): the range to retrieve the object by chunks.

        Returns:
            Dict[str, Any]: The S3 object metadata and data.

        Raises:
            botocore.exceptions.ClientError: If the object does not exist or any other error occurs.
        """
        try:
            if range_header:
                response = self.s3_client.get_object(
                    Bucket=self.bucket_name, Key=key, Range=range_header
                )
            else:
                response = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
            return {"metadata": response["Metadata"], "data": response["Body"]}
        except botocore.exceptions.ClientError as e:
            logger_service.error(
                "Failed to get object %s from %s: %s", key, self.bucket_name, e
            )
            raise

    def list_objects(self, prefix: Optional[str] = None) -> Iterator[str]:
        """
        List objects in the S3 bucket.

        Args:
            prefix (Optional[str]): If specified, only objects with keys
            that start with the prefix will be listed.

        Yields:
            Iterator[str]: The keys of the listed objects.
        """
        paginator = self.s3_client.get_paginator("list_objects_v2")
        operation_parameters = {"Bucket": self.bucket_name}
        if prefix:
            operation_parameters["Prefix"] = prefix

        for page in paginator.paginate(**operation_parameters):
            if "Contents" in page:
                for obj in page["Contents"]:
                    yield obj["Key"]

    def download_file(self, key: str, file_path: str) -> None:
        """
        Download a file from the S3 bucket.

        Args:
            key (str): The key of the object to download.
            file_path (str): The local file path where the object should be saved.

        Raises:
            botocore.exceptions.ClientError: If the object does not exist or any other error occurs.
        """
        try:
            self.s3_client.download_file(self.bucket_name, key, file_path)
            logger_service.error(
                "File %s downloaded successfully to %s", key, file_path
            )
        except botocore.exceptions.ClientError as e:
            logger_service.error(
                "Failed to download file %s from %s: %s", key, self.bucket_name, e
            )
            raise

    def get_file_size(self, key: str) -> int:
        """
        Get the size of an object in the S3 bucket.

        Args:
            key (str): The key of the object whose size you want to retrieve.

        Returns:
            int: The size of the object in bytes.

        Raises:
            botocore.exceptions.ClientError: If the object does not exist or any other error occurs.
        """
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            size = response["ContentLength"]
            logger_service.info("Size of file %s is %s bytes", key, size)
            return size
        except botocore.exceptions.ClientError as e:
            logger_service.error(
                "Failed to get size of file %s from %s: %s", key, self.bucket_name, e
            )
            raise

    def upload_file_to_s3(
        self, file_data: bytes, bucket_name: str, file_key: str
    ) -> bool:
        """
        Uploads a file (or file-like object) to the specified S3 bucket.

        Args:
            file_data: A file-like object (BytesIO or file) to upload.
            bucket_name (str): The name of the S3 bucket where the file will be uploaded.
            file_key (str): The key (name or path) to store the file under in the bucket.

        Returns:
            bool: True if the upload was successful, False otherwise.
        """

        try:
            # Upload the file-like object directly to S3
            self.s3_client.upload_fileobj(file_data, bucket_name, file_key)
            logger_service.info("File uploaded to %s/%s.", bucket_name, file_key)
            return True
        except botocore.exceptions.ClientError as e:
            logger_service.error(
                "Failed to upload to %s/%s: %s", bucket_name, file_key, e
            )
            return False

    def put_object_to_s3(self, **kwargs) -> bool:
        """
        Uploads a file (or file-like object) to the specified S3 bucket.

        Args:


        Returns:
            bool: True if the upload was successful, False otherwise.
        """

        try:
            # Upload the file-like object directly to S3
            self.s3_client.put_object(**kwargs)
            logger_service.info("File uploaded to %s/%s.", kwargs["Bucket"], "Key")
            return True
        except botocore.exceptions.ClientError as e:
            logger_service.error("Failed to upload to %s/%s: %s", "Bucket", "Key", e)
            return False
