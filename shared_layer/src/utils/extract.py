import importlib
from typing import Any, Optional

from .LoggerService import logger_service

map_rules: dict[str, dict[str, str | list[str]]] = {
    "s3_extract": {
        "class": "S3Utils",
        "module": "utils.S3Utils",
        "method": "get_object",
        "expected_params": ["bucket", "key"],
    },
    "dynamo_extract": {
        "class": "DynamoDBClient",
        "module": "utils.DynamoUtils",
        "method": "",
        "expected_params": ["table", "key", "method"],
    },
}


def extract(task: dict[str, Any]) -> bytes:
    logger_service.info("extract started")
    logger_service.info("extract with args: \n task: %s", task)

    process = task["extract"]["task"]
    rule: Optional[dict[str, Any]] = map_rules.get(process, None)
    if not rule:
        raise ValueError(f"Task {process} not allowed")
    params = task["extract"]["params"]
    expected_params = rule["expected_params"]

    # Check that all expected parameters are present
    missing_params = [param for param in expected_params if param not in params]
    if missing_params:
        raise KeyError(f"Missing required params in the request: {missing_params}")

    module: str = rule.get("module", "")
    class_str: str = rule.get("class", "")
    method_name: str = rule.get("method", "")

    module_import = importlib.import_module(module)
    cls = getattr(module_import, class_str)

    if process == "s3_extract":
        instance = cls(params["bucket"])
        method = getattr(instance, method_name)
        data = method(f"{params['key']}")
        data = data["data"].read().decode("utf-8")
        return data

    elif process == "dynamo_extract":
        method = params["method"]
        instance = cls(params["table"])

        # Only support read operations in extract
        if (
            method == "read_item"
            or method == "scan_items"
            or method == "query_items"
            or method == "grl_query_items"
            or method == "paginated_grl_query_items"
        ):
            data = getattr(instance, method)(params["key"])
            return data
        else:
            # For write operations, redirect to load module
            raise ValueError(
                f"Method {method} is not a read operation. Use the appropriate load operation instead."
            )

    logger_service.info("extract finished")
    return bytes()


def plain_extract(task: dict[str, Any]) -> bytes:
    logger_service.info("extract started")
    logger_service.info("extract with args: \n task: %s", task)

    process = task["extract"]["task"]
    rule: Optional[dict[str, Any]] = map_rules.get(process, None)
    if not rule:
        raise ValueError(f"Task {process} not allowed")
    params = task["extract"]["params"]
    expected_params = sorted(rule["expected_params"])
    if sorted(list(params.keys())) != expected_params:
        raise KeyError(
            f"Missing some params in the request, received: {params} expected {expected_params}"
        )

    module: str = rule.get("module", "")
    class_str: str = rule.get("class", "")
    method_name: str = rule.get("method", "")

    module_import = importlib.import_module(module)
    cls = getattr(module_import, class_str)

    instance = cls(params["bucket"])
    method = getattr(instance, method_name)
    data = method(f"{params['key']}")
    logger_service.info("extract finished")
    return data


if __name__ == "__main__":
    task_def = {}
    task_def["extract"] = {
        "task": "s3_extract",
        "params": {
            "key": "uploads/aly_5abu17_tomdooley_fullepisode_10918173.xml",
            "bucket": "test-express-lane",
        },
    }
    logger_service.info(extract(task_def))
