from datetime import datetime as dt
from typing import Any, Optional

from .LoggerService import logger_service
from .RequestsHandler import <PERSON>quest<PERSON>and<PERSON>
from .serializers import serialize_json
from .tasks.external import send_message_to_sqs
from .tasks.transforms import update_dynamo_extract


class WonderlandContainerRules:
    def __init__(self, context: dict[str, Any]):
        self.context = context

        # Extract API configuration from nested structure
        api_config = context.get("api_config", {})
        wonderland_validations = api_config.get("wonderland_validations", {})

        # Status configuration from wonderland_validations
        self.valid_status = wonderland_validations.get("status")
        self.update_status = wonderland_validations.get("update_status")

        # URL configuration from api_config
        self.main_url = str(api_config.get("main_url", ""))
        self.api_url = api_config.get("api_url", "")
        self.table = context.get("tacdev_reach_table", None)

        # Query configuration
        self.query = context.get("query")

    def __call__(self, previous: Any = None) -> Any:
        sqs_messages, auth_token = previous
        if not sqs_messages:
            self._log_and_raise_empty_messages()

        for item in sqs_messages:
            self._process_item(item, auth_token)

    def _log_and_raise_empty_messages(self):
        logger_service.warning("No query to apply %s", self.query)
        return

    def _process_item(self, item: dict[str, Any], auth_token: str):
        sidecar_id = item.get("sidecar_id")
        response = self._fetch_sidecar_details(sidecar_id, auth_token)
        if not response:
            logger_service.warning("No data found for Sidecar ID: %s", sidecar_id)
            return

        data = serialize_json(response)
        sidecar_details = data.get("sidecar_details", {})
        self._handle_rule(sidecar_details, item)

    def _fetch_sidecar_details(
        self, sidecar_id: str, auth_token: str
    ) -> Optional[dict[str, Any]]:
        request_handler = RequestHandler(self.main_url)
        return request_handler.get(f"{self.api_url}{sidecar_id}", headers=auth_token)

    def _handle_rule(self, sidecar_details: dict[str, Any], item: dict[str, Any]):
        sidecar_status = sidecar_details.get("status")
        sidecar_id = sidecar_details.get("uuid")
        wonderland_id = sidecar_details.get("id")
        timestamp = dt.now().isoformat()

        if sidecar_status == self.valid_status:
            logger_service.info("sqs trigger send message started")

            logger_service.info(
                "wonderland status COMPLETED and update datetime: %s, with sidecarID: %s",
                sidecar_id,
                timestamp,
            )

            key = {"sidecar_id": sidecar_id}
            value = {
                "updated_at": timestamp,
                "status": self.update_status,
                "wonderland_id": wonderland_id,
            }

            send_message_to_sqs(self.context, item)
            update_dynamo_extract(self.table, key, value)

            logger_service.info(
                "wonderland status update datetime completed: %s, with sidecarID: %s",
                timestamp,
                sidecar_id,
            )
        else:
            # If not status of sidecar is Completed
            key = {"sidecar_id": sidecar_id}
            value = {
                "updated_at": timestamp,
            }

            update_dynamo_extract(self.table, key, value)
            logger_service.warning(
                "sidecar status is not equal to COMPLETED: %s", sidecar_status
            )
