"""
Dynamo module to perform CRUD Operations
"""

from typing import Any, Dict, List, Optional, Union

import boto3
from boto3.dynamodb.conditions import Attr, Key

from . import config


class DynamoDBClient:
    """
    Class
    """

    def __init__(self, table_name: str) -> None:
        """
        Initializes the DynamoDB client.

        Args:
            table_name (str): The name of the DynamoDB table.
        """
        aws_region_name = config.REGION_NAME
        self.dynamodb = boto3.resource("dynamodb", region_name=aws_region_name)
        self.table = self.dynamodb.Table(table_name)

    def create_item(self, item: Dict[str, Any]) -> None:
        """
        Creates an item in the DynamoDB table.

        Args:
            item (Dict[str, Any]): The item to create.
        """
        self.table.put_item(Item=item)

    def read_item(self, key: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Reads an item from the DynamoDB table.

        Args:
            key (Dict[str, Any]): The key of the item to read.

        Returns:
            Optional[Dict[str, Any]]: The item if found, otherwise None.
        """
        response = self.table.get_item(Key=key)
        return response.get("Item")

    def update_item(self, key: Dict[str, Any], updates: Dict[str, Any]) -> None:
        """
        Updates an item in the DynamoDB table.

        Args:
            key (Dict[str, Any]): The key of the item to update.
            updates (Dict[str, Any]): A dictionary of attribute updates.
        """
        # Use attribute names with # prefix to handle reserved keywords
        expression_parts = []
        expression_attribute_names = {}
        expression_attribute_values = {}

        for k, v in updates.items():
            # Use placeholder for attribute name (for handling reserved words)
            attr_placeholder = f"#{k.replace('.', '.#')}"
            value_placeholder = f":{k}"

            expression_parts.append(f"{attr_placeholder} = {value_placeholder}")

            # Add attribute name mapping
            expression_attribute_names[attr_placeholder] = k

            # Add value mapping
            expression_attribute_values[value_placeholder] = v

        update_expression = "SET " + ", ".join(expression_parts)

        params = {
            "Key": key,
            "UpdateExpression": update_expression,
            "ExpressionAttributeValues": expression_attribute_values,
        }

        # Only add ExpressionAttributeNames if we have entries
        if expression_attribute_names:
            params["ExpressionAttributeNames"] = expression_attribute_names

        self.table.update_item(**params)

    def delete_item(self, key: Dict[str, Any]) -> None:
        """
        Deletes an item from the DynamoDB table.

        Args:
            key (Dict[str, Any]): The key of the item to delete.
        """
        self.table.delete_item(Key=key)

    def query_items(
        self, conditions: Dict[str, Union[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Queries items in the DynamoDB table based on conditions.

        Args:
            conditions (Dict[str, Union[str, Any]]): A dictionary where keys are attribute names
                                                    and values are tuples of (operator, value).

        Returns:
            List[Dict[str, Any]]: The list of items that match the query.
        """
        key_condition_expression = None
        filter_expression = None

        for attr, (op, val) in conditions.items():
            condition = self._build_condition(attr, op, val)
            # Assuming 'id' is the partition key
            if attr == "id":
                key_condition_expression = condition
            else:
                filter_expression = (
                    condition
                    if filter_expression is None
                    else filter_expression & condition
                )

        query_params = {"KeyConditionExpression": key_condition_expression}

        if filter_expression:
            query_params["FilterExpression"] = filter_expression

        response = self.table.query(**query_params)
        return response.get("Items", [])

    def grl_query_items(
        self, conditions: Dict[str, Union[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Queries items in the DynamoDB table based on conditions.

        Args:
            conditions (Dict[str, Union[str, Any]]): A dictionary where keys are attribute names
                                                    and values are tuples of (operator, value).

        Returns:
            List[Dict[str, Any]]: The list of items that match the query.
        """
        key_condition_expression = None
        filter_expression = None

        for attr, (op, val) in conditions.items():
            condition = self._build_condition(attr, op, val)
            key_schema = {key["AttributeName"] for key in self.table.key_schema}
            if attr in key_schema:
                key_condition_expression = condition
            else:
                filter_expression = (
                    condition
                    if filter_expression is None
                    else filter_expression & condition
                )

        query_params = {}

        if key_condition_expression:
            query_params["KeyConditionExpression"] = key_condition_expression
            if filter_expression:
                query_params["FilterExpression"] = filter_expression
            response = self.table.query(**query_params)
        else:
            # Use scan if no partition key is provided
            query_params["FilterExpression"] = filter_expression
            response = self.table.scan(**query_params)

        return response.get("Items", [])

    def paginated_grl_query_items(
        self, conditions: Dict[str, Union[str, Any]], limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Queries items in the DynamoDB table based on conditions with pagination support.

        This method continuously queries or scans the table until all matching items are retrieved.

        Args:
            conditions (Dict[str, Union[str, Any]]): A dictionary where keys are attribute names and values are tuples of (operator, value).
            limit (Optional[int]): An optional limit for number of items per request.

        Returns:
            List[Dict[str, Any]]: The complete list of items that match the query.
        """
        items: List[Dict[str, Any]] = []
        last_evaluated_key = None
        key_condition_expression = None
        filter_expression = None

        for attr, (op, val) in conditions.items():
            condition = self._build_condition(attr, op, val)
            key_schema = {key["AttributeName"] for key in self.table.key_schema}
            if attr in key_schema:
                if key_condition_expression is None:
                    key_condition_expression = condition
                else:
                    key_condition_expression = key_condition_expression & condition
            else:
                if filter_expression is None:
                    filter_expression = condition
                else:
                    filter_expression = filter_expression & condition

        query_params: Dict[str, Any] = {}
        if key_condition_expression:
            query_params["KeyConditionExpression"] = key_condition_expression
            if filter_expression:
                query_params["FilterExpression"] = filter_expression
            query_method = self.table.query
        else:
            query_params["FilterExpression"] = filter_expression
            query_method = self.table.scan

        if limit is not None:
            query_params["Limit"] = limit

        while True:
            if last_evaluated_key:
                query_params["ExclusiveStartKey"] = last_evaluated_key
            response = query_method(**query_params)
            items.extend(response.get("Items", []))
            last_evaluated_key = response.get("LastEvaluatedKey")
            if not last_evaluated_key:
                break

        return items

    def scan_items(self) -> List[Dict[str, Any]]:
        """
        Scans the DynamoDB table.

        Returns:
            List[Dict[str, Any]]: The list of all items in the table.
        """
        response = self.table.scan()
        return response.get("Items", [])

    @staticmethod
    def _build_condition(attr: str, operator: str, value: Any) -> Any:
        """
        Builds a DynamoDB condition expression.

        Args:
            attr (str): The attribute name.
            operator (str): The comparison operator (`=`, `!=`, `<`, `<=`, `>`, `>=`, `contains`).
            value (Any): The value to compare.

        Returns:
            Any: The condition expression.
        """
        op_map = {
            "=": Key(attr).eq,
            "!=": Attr(attr).ne,
            "<": Key(attr).lt,
            "<=": Key(attr).lte,
            ">": Key(attr).gt,
            ">=": Key(attr).gte,
            "contains": Attr(attr).contains,
        }
        if operator not in op_map:
            raise ValueError(f"Unsupported operator: {operator}")

        return op_map[operator](value)
