import logging
import unittest
from io import String<PERSON>
from unittest.mock import MagicMock, patch

from src.utils.LoggerService import LoggerService


class TestLoggerService(unittest.TestCase):

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def setUp(self, mock_virtual_memory, mock_cpu_percent):
        # Mocking psutil data for CPU and Memory
        mock_cpu_percent.return_value = 25.5
        mock_virtual_memory.return_value = MagicMock()
        mock_virtual_memory.return_value.percent = 60.5
        mock_virtual_memory.return_value.total = 16 * (1024**3)  # 16 GB
        mock_virtual_memory.return_value.used = 9.5 * (1024**3)  # 9.5 GB used
        mock_virtual_memory.return_value.available = 6.5 * (1024**3)  # 6.5 GB available

        # Creating an instance of LoggerService
        self.logger_service = LoggerService(log_file_name=None, log_level=logging.DEBUG)

        # Capture log output
        self.log_output = StringIO()
        stream_handler = logging.StreamHandler(self.log_output)
        self.logger_service.logger.addHandler(stream_handler)

    def tearDown(self):
        # Resetting the logger to avoid side effects in other tests
        for handler in self.logger_service.logger.handlers:
            self.logger_service.logger.removeHandler(handler)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_log_debug(self, mock_virtual_memory, mock_cpu_percent):
        mock_cpu_percent.return_value = 25.5
        mock_virtual_memory.return_value.percent = 60.5
        mock_virtual_memory.return_value.total = 16 * (1024**3)  # 16 GB
        mock_virtual_memory.return_value.used = 9.5 * (1024**3)  # 9.5 GB used

        self.logger_service.debug("Test debug message")
        log_content = self.log_output.getvalue().strip()

        self.assertIn("Test debug message", log_content)
        self.assertIn("CPU Usage: 25.5%", log_content)
        self.assertIn("Memory Usage: 60.5%", log_content)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_log_info(self, mock_virtual_memory, mock_cpu_percent):
        mock_cpu_percent.return_value = 25.5
        mock_virtual_memory.return_value.percent = 60.5
        mock_virtual_memory.return_value.total = 16 * (1024**3)  # 16 GB
        mock_virtual_memory.return_value.used = 9.5 * (1024**3)  # 9.5 GB used

        self.logger_service.info("Test info message")
        log_content = self.log_output.getvalue().strip()

        # Fixed: Matching the actual logged message
        self.assertIn("Test info message", log_content)
        self.assertIn("CPU Usage: 25.5%", log_content)
        self.assertIn("Memory Usage: 60.5%", log_content)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_log_warning(self, mock_virtual_memory, mock_cpu_percent):
        mock_cpu_percent.return_value = 45.0
        mock_virtual_memory.return_value.percent = 80.0
        mock_virtual_memory.return_value.total = 16 * (1024**3)  # 16 GB
        mock_virtual_memory.return_value.used = 13 * (1024**3)  # 13 GB used

        self.logger_service.warning("Test warning message")
        log_content = self.log_output.getvalue().strip()

        self.assertIn("Test warning message", log_content)
        self.assertIn("CPU Usage: 45.0%", log_content)
        self.assertIn("Memory Usage: 80.0%", log_content)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    @patch("traceback.format_exc", return_value="Mocked traceback")
    def test_log_error_with_traceback(
        self, mock_format_exc, mock_virtual_memory, mock_cpu_percent
    ):
        mock_cpu_percent.return_value = 55.0
        mock_virtual_memory.return_value.percent = 90.0
        mock_virtual_memory.return_value.total = 16 * (1024**3)  # 16 GB
        mock_virtual_memory.return_value.used = 14.5 * (1024**3)  # 14.5 GB used

        self.logger_service.error("Test error message", include_traceback=True)
        log_content = self.log_output.getvalue().strip()

        self.assertIn("Test error message", log_content)
        self.assertIn("CPU Usage: 55.0%", log_content)
        self.assertIn("Memory Usage: 90.0%", log_content)
        self.assertIn("Mocked traceback", log_content)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    def test_log_critical_without_traceback(
        self, mock_virtual_memory, mock_cpu_percent
    ):
        mock_cpu_percent.return_value = 65.0
        mock_virtual_memory.return_value.percent = 95.0
        mock_virtual_memory.return_value.total = 16 * (1024**3)  # 16 GB
        mock_virtual_memory.return_value.used = 15 * (1024**3)  # 15 GB used

        self.logger_service.critical("Test critical message", include_traceback=False)
        log_content = self.log_output.getvalue().strip()

        self.assertIn("Test critical message", log_content)
        self.assertIn("CPU Usage: 65.0%", log_content)
        self.assertIn("Memory Usage: 95.0%", log_content)
