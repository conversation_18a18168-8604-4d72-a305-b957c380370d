from unittest.mock import MagicMock, patch

import pytest

from src.utils.settings_manager import SettingsManagerService


@pytest.fixture
def settings_manager_service(dynamodb_client):
    with patch("src.utils.LoggerService.logger_service") as MockLoggerService:
        service = SettingsManagerService("test")
        yield service


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_initialize_success(aws_mock_test, dynamodb_client, settings_manager_service):
    settings_mock = MagicMock()
    settings_mock.id = "123"
    settings_mock.event_project_name = "test_project"
    settings_mock.frequency = "daily"
    settings_mock.time = "12:00"
    settings_mock.retries = 3
    settings_mock.debug_readonly = True
    settings_mock.get_rules.return_value = {"rule1": "value1"}
    settings_mock.get_status_table.return_value = "status_table"

    settings_manager_service.settings = settings_mock

    settings_manager_service.initialize = MagicMock()

    settings_manager_service.initialize("test_project", readonly=True)

    assert settings_manager_service.settings.id == "123"
    assert settings_manager_service.settings.event_project_name == "test_project"
    assert settings_manager_service.settings.frequency == "daily"
    assert settings_manager_service.settings.time == "12:00"
    assert settings_manager_service.settings.retries == 3
    assert settings_manager_service.settings.debug_readonly is True
    assert settings_manager_service.settings.get_rules() == {"rule1": "value1"}
    assert settings_manager_service.settings.get_status_table() == "status_table"

    settings_manager_service.initialize.assert_called_once_with(
        "test_project", readonly=True
    )


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_initialize_project_not_found(
    aws_mock_test, dynamodb_client, settings_manager_service
):
    settings_manager_service.dynamo_client = dynamodb_client
    with pytest.raises(ValueError) as context:
        settings_manager_service.initialize("non_existent_project")

    assert str(context.value) == "We don't have a config for non_existent_project"
