import pytest
from botocore.exceptions import ClientError
from moto import mock_aws


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_get_object(aws_mock_test, s3_utils):
    s3_mock = aws_mock_test
    # Upload a mock object to S3
    s3_mock.put_object(Bucket="test-bucket", Key="test-key", Body=b"hello world")

    # Retrieve the full object using S3Utils
    full_response = s3_utils.get_object("test-key")

    # Assert that the full object was retrieved successfully
    assert full_response["data"].read() == b"hello world"

    # Test the range retrieval
    range_header = "bytes=0-4"  # Retrieving the first 5 bytes (0-4 range)
    partial_response = s3_utils.get_object("test-key", range_header=range_header)

    # Assert that the partial object was retrieved successfully
    assert partial_response["data"].read() == b"hello"

    # Check if the metadata is included
    assert "metadata" in partial_response


@pytest.mark.parametrize(
    "aws_mock_test, prefix, expected_keys",
    [
        ("s3", None, ["test-key-1", "test-key-2"]),  # No prefix case
        ("s3", "test-key-1", ["test-key-1"]),  # Prefix case
        ("s3", "non-existent", []),  # Case where prefix doesn't match any keys
    ],
    indirect=["aws_mock_test"],
)
def test_list_objects(aws_mock_test, s3_utils, prefix, expected_keys):
    s3_mock = aws_mock_test

    # Upload mock objects to S3
    s3_mock.put_object(Bucket="test-bucket", Key="test-key-1", Body=b"data")
    s3_mock.put_object(Bucket="test-bucket", Key="test-key-2", Body=b"data")

    # List objects using S3Utils with or without prefix
    objects = list(s3_utils.list_objects(prefix=prefix))

    # Assert that the keys are listed correctly
    assert set(objects) == set(expected_keys)


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_download_file(aws_mock_test, s3_utils, tmp_path):
    s3_mock = aws_mock_test
    # Upload a mock object to S3
    s3_mock.put_object(Bucket="test-bucket", Key="test-key", Body=b"hello world")

    # Define the local file path to download the object
    file_path = tmp_path / "test-file.txt"

    # Download the file using S3Utils
    s3_utils.download_file("test-key", str(file_path))

    # Assert that the file was downloaded successfully
    with open(file_path, "rb") as f:
        assert f.read() == b"hello world"


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
def test_get_file_size(aws_mock_test, s3_utils):
    s3_mock = aws_mock_test
    # Upload a mock object to S3
    s3_mock.put_object(Bucket="test-bucket", Key="test-key", Body=b"hello world")

    # Get the file size using S3Utils
    size = s3_utils.get_file_size("test-key")

    # Assert that the file size is correct
    assert size == len(b"hello world")


# ---------------------------
# Error Tests for S3Utils
# ---------------------------


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
@mock_aws
def test_get_object_error(aws_mock_test, s3_utils):
    # Create an S3 bucket and ensure it is empty
    s3_mock = aws_mock_test

    # Attempt to get a non-existent object, which should raise a ClientError
    with pytest.raises(ClientError) as exc_info:
        s3_utils.get_object("non-existent-key")

    assert exc_info.value.response["Error"]["Code"] == "NoSuchKey"
    assert (
        exc_info.value.response["Error"]["Message"]
        == "The specified key does not exist."
    )


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
@mock_aws
def test_list_objects_error(aws_mock_test, s3_utils):
    # Create an S3 bucket but do not put any objects
    s3_mock = aws_mock_test

    # Attempt to list objects in an empty bucket, which should not raise an error but return an empty list
    objects = list(s3_utils.list_objects())
    assert objects == []  # Ensure no objects are listed


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
@mock_aws
def test_download_file_error(aws_mock_test, s3_utils, tmp_path):
    # Define the local file path
    file_path = tmp_path / "test-file.txt"

    # Attempt to download a non-existent file, which should raise a ClientError
    with pytest.raises(ClientError) as exc_info:
        s3_utils.download_file("non-existent-key", str(file_path))

    assert exc_info.value.response["Error"]["Code"] == "404"
    assert exc_info.value.response["Error"]["Message"] == "Not Found"


@pytest.mark.parametrize("aws_mock_test", ["s3"], indirect=True)
@mock_aws
def test_get_file_size_error(aws_mock_test, s3_utils):
    # Attempt to get the size of a non-existent object, which should raise a ClientError
    with pytest.raises(ClientError) as exc_info:
        s3_utils.get_file_size("non-existent-key")

    assert exc_info.value.response["Error"]["Code"] == "404"
    assert exc_info.value.response["Error"]["Message"] == "Not Found"
