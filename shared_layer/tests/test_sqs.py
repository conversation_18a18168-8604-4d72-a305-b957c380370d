import ast

import pytest

from src.utils.SQSHandler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


@pytest.mark.parametrize("aws_mock_test", ["sqs"], indirect=True)
def test_sqs(aws_mock_test):

    sqs_topic = "test-queue"
    filtered_messages = [
        {
            "sidecar_id": "jhk754-kjh6-00ou-234-kjhgvcfd345",
            "attribute_name": "wonderlandEpisodeArtID",
            "process_status": "wl_in_progress",
            "reach_package_id": "123456789",
            "wonderland_id": "0987654321",
        }
    ]

    # Create the SQS queue
    queue_url = aws_mock_test.create_queue(QueueName=sqs_topic)["QueueUrl"]

    # Initialize SQS handler
    sqs_handler = SQSHandler(queue_url)

    # Send messages
    for message in filtered_messages:
        sqs_handler.send_message(message)

    response = aws_mock_test.receive_message(
        QueueUrl=queue_url, MaxNumberOfMessages=10, WaitTimeSeconds=1
    )

    body_value = ast.literal_eval(response["Messages"][0]["Body"])

    assert body_value == filtered_messages[0]
