from unittest.mock import patch

import pytest

from src.utils.schemas.status_tracker_schema import (
    StatusTrackerEnum,
    StatusTrackerSchema,
)
from src.utils.status_tracker import ProcessStatusTracker


@pytest.fixture
def mock_dynamo_client():
    with patch("src.utils.status_tracker.DynamoDBClient") as mock:
        yield mock.return_value


@pytest.fixture
def tracker(mock_dynamo_client):
    return ProcessStatusTracker(status_table_name="mock-table")


def test_create_initial_status(tracker, mock_dynamo_client):
    schema = StatusTrackerSchema(landing_bucket="test-bucket")
    tracker.create_initial_status(schema)

    assert schema.process_status == "Ready to process"
    mock_dynamo_client.create_item.assert_called_once()
    dumped = mock_dynamo_client.create_item.call_args[0][0]
    assert dumped["landing_bucket"] == "test-bucket"
    assert dumped["process_status"] == "Ready to process"


def test_mark_as_running(tracker, mock_dynamo_client):
    client_id = "abc123"
    tracker.mark_as_running(client_id)

    mock_dynamo_client.update_item.assert_called_once()
    kwargs = mock_dynamo_client.update_item.call_args.kwargs
    assert kwargs["key"] == {"client_id": client_id}
    assert kwargs["updates"]["process_status"] == StatusTrackerEnum.RUNNING.value
    assert kwargs["updates"]["more_details"] == "Event Running"


def test_mark_as_completed(tracker, mock_dynamo_client):
    client_id = "abc123"
    tracker.mark_as_completed(client_id)

    mock_dynamo_client.update_item.assert_called_once()
    args, kwargs = mock_dynamo_client.update_item.call_args
    assert kwargs["updates"]["process_status"] == StatusTrackerEnum.COMPLETED.value
    assert kwargs["updates"]["more_details"] == "Event Completed"


def test_mark_as_failed(tracker, mock_dynamo_client):
    client_id = "abc123"
    error_msg = "Something went wrong"
    tracker.mark_as_failed(client_id, error_msg)

    mock_dynamo_client.update_item.assert_called_once()
    args, kwargs = mock_dynamo_client.update_item.call_args
    assert kwargs["updates"]["process_status"] == StatusTrackerEnum.FAILED.value
    assert kwargs["updates"]["more_details"] == error_msg
