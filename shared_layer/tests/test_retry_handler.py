from unittest.mock import Mock, patch

import pytest

from src.utils.RetryHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_successful_execution():
    retry_handler = <PERSON><PERSON><PERSON>and<PERSON>(max_retries=3, delay=1)
    mock_function = Mock(return_value="Success")
    mock_function.__name__ = "mock_function"

    result = retry_handler.execute(mock_function)

    assert result == "Success"
    assert mock_function.call_count == 1

    record = retry_handler.records[mock_function.__name__]
    assert record.retries == 1
    assert record.status is True


def test_failure_after_retries():
    retry_handler = <PERSON>tryHandler(max_retries=3, delay=1)
    mock_function = Mock(side_effect=Exception("Fail"))
    mock_function.__name__ = "mock_function"

    result = retry_handler.execute(mock_function)

    assert result is None
    assert mock_function.call_count == 3
    record = retry_handler.records[mock_function.__name__]
    assert record.retries == 3
    assert record.status is False


def test_retry_with_backoff():
    retry_handler = <PERSON><PERSON><PERSON>andler(max_retries=2, delay=1)
    mock_function = Mock(side_effect=[Exception("Fail"), "Success"])
    mock_function.__name__ = "mock_function"

    with patch("time.sleep", return_value=None) as mock_sleep:
        result = retry_handler.execute(mock_function)

    assert result == "Success"
    assert mock_function.call_count == 2
    assert mock_sleep.call_count == 1
    record = retry_handler.records[mock_function.__name__]
    assert record.retries == 2
    assert record.status is True


@pytest.fixture
def retry_handler_with_custom_values():
    return RetryHandler(max_retries=5, delay=3)


def test_custom_retry_handler_values(retry_handler_with_custom_values):
    assert retry_handler_with_custom_values.max_retries == 5
    assert retry_handler_with_custom_values.delay == 3
