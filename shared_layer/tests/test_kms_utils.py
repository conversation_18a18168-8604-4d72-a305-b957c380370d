from unittest.mock import patch

import boto3
import pytest
from botocore.exceptions import Client<PERSON>rror
from moto import mock_aws

from src.utils.KMSUtils import KMSUtils


@pytest.fixture
@mock_aws
def kms_utils_client():
    """Fixture to create a KMSUtils with specific client instance."""
    client = boto3.client("kms", region_name="us-east-2")
    kms_utils = KMSUtils()
    kms_utils.client = client
    return kms_utils


@mock_aws
def test_encrypt_data(kms_utils_client):
    print(kms_utils_client)
    kms_utils = kms_utils_client
    key = kms_utils.client.create_key(Description="Test key")
    key_id = key["KeyMetadata"]["KeyId"]
    plaintext = "Hello, World!"
    encrypted_data = kms_utils.encrypt_data(key_id, plaintext)

    assert encrypted_data is not None
    assert isinstance(encrypted_data, bytes)


@mock_aws
def test_decrypt_data(kms_utils_client):
    kms_utils = kms_utils_client
    key = kms_utils.client.create_key(Description="Test key")
    key_id = key["KeyMetadata"]["KeyId"]
    plaintext = "Hello, World!"
    encrypted_data = kms_utils.encrypt_data(key_id, plaintext)
    decrypted_data = kms_utils.decrypt_data(encrypted_data)

    assert decrypted_data == plaintext


@mock_aws
@patch.object(
    KMSUtils,
    "encrypt_data",
    side_effect=ValueError("Failed to encrypt data: Encryption failed"),
)
def test_encrypt_data_failure(mock_encrypt):
    kms_utils = KMSUtils()
    with pytest.raises(ValueError, match="Failed to encrypt data: Encryption failed"):
        kms_utils.encrypt_data("invalid_key_id", "Hello")


@mock_aws
@patch.object(
    KMSUtils,
    "decrypt_data",
    side_effect=ValueError("Failed to decrypt data: Decryption failed"),
)
def test_decrypt_data_failure(mock_decrypt):
    kms_utils = KMSUtils()
    with pytest.raises(ValueError, match="Failed to decrypt data: Decryption failed"):
        kms_utils.decrypt_data(b"invalid_ciphertext")


@mock_aws
def test_encrypt_data_client_error(kms_utils_client):
    kms_utils = kms_utils_client
    with patch.object(
        kms_utils.client,
        "encrypt",
        side_effect=ClientError(
            {
                "Error": {
                    "Code": "AccessDenied",
                    "Message": "You are not authorized to perform this action.",
                }
            },
            "Encrypt",
        ),
    ):
        with pytest.raises(ValueError, match="Failed to encrypt data:"):
            kms_utils.encrypt_data("invalid_key_id", "Hello")


@mock_aws
def test_decrypt_data_client_error(kms_utils_client):
    kms_utils = kms_utils_client
    with patch.object(
        kms_utils.client,
        "decrypt",
        side_effect=ClientError(
            {
                "Error": {
                    "Code": "AccessDenied",
                    "Message": "You are not authorized to perform this action.",
                }
            },
            "Decrypt",
        ),
    ):
        with pytest.raises(ValueError, match="Failed to decrypt data:"):
            kms_utils.decrypt_data(b"invalid_ciphertext")


@mock_aws
def test_repr(kms_utils_client):
    kms_utils = kms_utils_client
    repr_output = repr(kms_utils)
    assert "KMSUtils(region_name=us-east-2)" in repr_output
