from pathlib import Path
from unittest.mock import MagicMock

import pytest

from src.utils.FileProcessor import (
    FileProcessor,  # Assuming the class is in a file named `file_processor.py`
)


# Mock methods to avoid real file system operations
@pytest.fixture
def mock_file_processor():
    # Create an instance of FileProcessor but mock out file operations
    file_processor = FileProcessor()

    # Mock the methods that interact with the filesystem
    file_processor._validate_file_exists = MagicMock()
    file_processor._validate_directory_exists = MagicMock()
    file_processor.create_tar = MagicMock()
    file_processor.extract_tar = MagicMock()
    file_processor.create_zip = MagicMock()
    file_processor.extract_zip = MagicMock()

    return file_processor


def test_extract_tar(mock_file_processor):
    # Mock the internal methods and simulate a tar extraction
    mock_file_processor.extract_tar(Path("mock_tar.tar"), Path("mock_extract_dir"))

    # Check that the extraction method was called with correct arguments
    mock_file_processor.extract_tar.assert_called_once_with(
        Path("mock_tar.tar"), <PERSON>("mock_extract_dir")
    )


def test_create_tar(mock_file_processor):
    # Mock the internal methods and simulate tar creation
    mock_file_processor.create_tar(Path("mock_source_dir"), Path("mock_output.tar"))

    # Check that the create_tar method was called with correct arguments
    mock_file_processor.create_tar.assert_called_once_with(
        Path("mock_source_dir"), Path("mock_output.tar")
    )


def test_extract_zip(mock_file_processor):
    # Mock the internal methods and simulate zip extraction
    mock_file_processor.extract_zip(Path("mock_zip.zip"), Path("mock_extract_dir"))

    # Check that the extraction method was called with correct arguments
    mock_file_processor.extract_zip.assert_called_once_with(
        Path("mock_zip.zip"), Path("mock_extract_dir")
    )


def test_create_zip(mock_file_processor):
    # Mock the internal methods and simulate zip creation
    mock_file_processor.create_zip(Path("mock_source_dir"), Path("mock_output.zip"))

    # Check that the create_zip method was called with correct arguments
    mock_file_processor.create_zip.assert_called_once_with(
        Path("mock_source_dir"), Path("mock_output.zip")
    )


def test_is_compressed_file_tar(mock_file_processor):
    # Test is_compressed_file method for .tar file
    result = mock_file_processor.is_compressed_file("mock_file.tar")
    assert result is True


def test_is_compressed_file_zip(mock_file_processor):
    # Test is_compressed_file method for .zip file
    result = mock_file_processor.is_compressed_file("mock_file.zip")
    assert result is True


def test_is_compressed_file_other(mock_file_processor):
    # Test is_compressed_file method for a non-compressed file
    result = mock_file_processor.is_compressed_file("mock_file.txt")
    assert result is False


def test_extract_multiple_files(mock_file_processor):
    # Mock extraction of multiple files
    file_paths = ["mock_tar1.tar", "mock_zip1.zip"]
    extract_to = ["mock_extract1", "mock_extract2"]

    mock_file_processor.extract_multiple_files(file_paths, extract_to)

    # Check that both extract methods were called with correct arguments
    mock_file_processor.extract_tar.assert_called_with("mock_tar1.tar", "mock_extract1")
    mock_file_processor.extract_zip.assert_called_with("mock_zip1.zip", "mock_extract2")


def test_create_multiple_files(mock_file_processor):
    # Mock creation of multiple files
    source_dirs = ["mock_source1", "mock_source2"]
    output_files = ["mock_output1.tar", "mock_output2.zip"]

    mock_file_processor.create_multiple_files(source_dirs, output_files)

    # Check that both create methods were called with correct arguments
    mock_file_processor.create_tar.assert_called_with(
        "mock_source1", "mock_output1.tar"
    )
    mock_file_processor.create_zip.assert_called_with(
        "mock_source2", "mock_output2.zip"
    )
