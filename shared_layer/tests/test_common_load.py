import unittest
from unittest.mock import MagicMock, patch

from src.utils.load import load

MOCK_RULES = {
    "s3_upload": {
        "allowed_extensions": ["xml", "json", "xlsx", "txt", "pdf", "tar", "yml"],
        "class": "S3Utils",
        "module": "mock_module",
        "method": "put_object_to_s3",
        "expected_params": ["Bucket", "Key", "Body"],
    },
    "dynamo_upload": {
        "class": "DynamoDBClient",
        "module": "mock_module_dynamo",
        "method": "",
        "expected_params": ["table", "key", "method", "value"],
    },
}


class TestLoadFunction(unittest.TestCase):
    @patch("src.utils.load.importlib.import_module")
    @patch("src.utils.load.map_rules", new_callable=lambda: MOCK_RULES)
    def test_successful_s3_upload(self, mock_rules, mock_import):
        # Mock the class and method for s3_upload
        mock_class = MagicMock()
        mock_instance = MagicMock()
        mock_method = MagicMock()
        mock_instance.put_object_to_s3 = mock_method
        mock_class.return_value = mock_instance
        mock_import.return_value.S3Utils = mock_class

        # Input task for s3_upload
        task = {
            "load": {
                "task": "s3_upload",
                "params": {
                    "Bucket": "test_bucket",
                    "Key": "test.txt",
                    "Body": "test content",
                },
            }
        }

        # Call the function
        result = load(task)

        # Assertions
        mock_import.assert_called_once_with("mock_module")
        mock_method.assert_called_once_with(
            Bucket="test_bucket", Key="test.txt", Body="test content"
        )
        self.assertTrue(result)

    @patch("src.utils.load.importlib.import_module")
    @patch("src.utils.load.map_rules", new_callable=lambda: MOCK_RULES)
    def test_successful_dynamo_update_item(self, mock_rules, mock_import):
        # Mock the class and method for dynamo_upload with update_item
        mock_class_dynamo = MagicMock()
        mock_instance_dynamo = MagicMock()
        mock_update_item = MagicMock()
        mock_instance_dynamo.update_item = mock_update_item
        mock_update_item.return_value = {"status": "updated"}
        mock_class_dynamo.return_value = mock_instance_dynamo
        mock_import.return_value.DynamoDBClient = mock_class_dynamo

        # Input task for dynamo_upload with update_item
        task = {
            "load": {
                "task": "dynamo_upload",
                "params": {
                    "table": "test_table",
                    "key": {"id": "123"},
                    "method": "update_item",
                    "value": {"status": "complete"},
                },
            }
        }

        # Call the function
        result = load(task)

        # Assertions
        mock_import.assert_called_once_with("mock_module_dynamo")
        mock_update_item.assert_called_once_with({"id": "123"}, {"status": "complete"})
        self.assertEqual(result, {"status": "updated"})

    @patch("src.utils.load.importlib.import_module")
    @patch("src.utils.load.map_rules", new_callable=lambda: MOCK_RULES)
    def test_successful_dynamo_create_item(self, mock_rules, mock_import):
        # Mock the class and method for dynamo_upload with create_item
        mock_class_dynamo = MagicMock()
        mock_instance_dynamo = MagicMock()
        mock_create_item = MagicMock()
        mock_create_item.return_value = None  # Explicitly set return value to None
        mock_instance_dynamo.create_item = mock_create_item
        mock_class_dynamo.return_value = mock_instance_dynamo
        mock_import.return_value.DynamoDBClient = mock_class_dynamo

        # Input task for dynamo_upload with create_item
        task = {
            "load": {
                "task": "dynamo_upload",
                "params": {
                    "table": "test_table",
                    "key": {"id": "123"},  # Key still needed in expected_params
                    "method": "create_item",
                    "value": {"id": "123", "status": "new", "title": "Test Item"},
                },
            }
        }

        # Call the function
        result = load(task)

        # Assertions
        mock_import.assert_called_once_with("mock_module_dynamo")
        mock_create_item.assert_called_once_with(
            {"id": "123", "status": "new", "title": "Test Item"}
        )
        self.assertEqual(result, None)  # create_item doesn't return anything

    @patch("src.utils.load.importlib.import_module")
    @patch("src.utils.load.map_rules", new_callable=lambda: MOCK_RULES)
    def test_successful_dynamo_delete_item(self, mock_rules, mock_import):
        # Mock the class and method for dynamo_upload with delete_item
        mock_class_dynamo = MagicMock()
        mock_instance_dynamo = MagicMock()
        mock_delete_item = MagicMock()
        mock_delete_item.return_value = None  # Explicitly set return value to None
        mock_instance_dynamo.delete_item = mock_delete_item
        mock_class_dynamo.return_value = mock_instance_dynamo
        mock_import.return_value.DynamoDBClient = mock_class_dynamo

        # Input task for dynamo_upload with delete_item
        task = {
            "load": {
                "task": "dynamo_upload",
                "params": {
                    "table": "test_table",
                    "key": {"id": "123"},
                    "method": "delete_item",
                    "value": {},  # Empty but required by expected_params
                },
            }
        }

        # Call the function
        result = load(task)

        # Assertions
        mock_import.assert_called_once_with("mock_module_dynamo")
        mock_delete_item.assert_called_once_with({"id": "123"})
        self.assertEqual(result, None)  # delete_item doesn't return anything

    def test_task_not_allowed(self):
        # Input task with an invalid task
        task = {
            "load": {
                "task": "invalid_task",
                "params": {"Bucket": "test_bucket", "Key": "file_key", "Body": "test"},
            }
        }
        # Call the function and expect an exception
        with self.assertRaises(ValueError) as context:
            load(task)
        self.assertIn("Task", str(context.exception))

    def test_missing_params(self):
        # Test for missing required parameters
        task = {
            "load": {
                "task": "s3_upload",
                "params": {
                    "Bucket": "test_bucket",
                    # Missing "Key" and "Body"
                },
            }
        }

        with self.assertRaises(KeyError) as context:
            load(task)
        self.assertIn("Missing required params", str(context.exception))

    def test_dynamo_update_item_missing_value(self):
        # Test for missing value in update_item
        task = {
            "load": {
                "task": "dynamo_upload",
                "params": {
                    "table": "test_table",
                    "key": {"id": "123"},
                    "method": "update_item",
                    # 'value' is missing
                },
            }
        }

        with self.assertRaises(KeyError) as context:
            load(task)
        self.assertIn("Missing required params", str(context.exception))
        self.assertIn("value", str(context.exception))

    def test_invalid_file_extension(self):
        # Test for invalid file extension
        task = {
            "load": {
                "task": "s3_upload",
                "params": {
                    "Bucket": "test_bucket",
                    "Key": "test.invalid_extension",
                    "Body": "content",
                },
            }
        }

        with self.assertRaises(ValueError) as context:
            load(task)
        self.assertIn("File extension", str(context.exception))

    def test_unsupported_dynamo_method(self):
        # Test for unsupported DynamoDB method
        task = {
            "load": {
                "task": "dynamo_upload",
                "params": {
                    "table": "test_table",
                    "key": {"id": "123"},
                    "method": "unsupported_method",
                    "value": {"data": "test"},
                },
            }
        }

        with self.assertRaises(ValueError) as context:
            load(task)
        self.assertIn("Unsupported DynamoDB method", str(context.exception))
