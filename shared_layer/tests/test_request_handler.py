from unittest.mock import Mock, patch

import pytest
from requests import HTTP<PERSON>rror, RequestException, Response

from src.utils.RequestsHandler import Request<PERSON><PERSON><PERSON>


@pytest.fixture
def request_handler():
    """Fixture to create a RequestHandler instance."""
    return RequestHandler(base_url="https://dummyjson.com")


def test_get_success(request_handler):
    """Test successful GET request."""
    with patch("requests.Session.get") as mock_get:
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        mock_response.json.return_value = {"method": "GET", "status": "ok"}
        mock_get.return_value = mock_response

        response = request_handler.get("/test")

        assert response.status_code == mock_response.status_code
        assert response.json() == mock_response.json()
        mock_get.assert_called_once_with(
            "https://dummyjson.com/test", params=None, headers=None
        )


def test_get_http_error(request_handler):
    """Test GET request handling HTTPError."""
    with patch("requests.Session.get") as mock_get:
        mock_get.side_effect = HTTPError("HTTP error occurred")

        with pytest.raises(Exception) as excinfo:
            request_handler.get("/test")
        assert "HTTP error occurred" in str(excinfo.value)


def test_get_request_exception(request_handler):
    """Test GET request handling RequestException."""
    with patch("requests.Session.get") as mock_get:
        mock_get.side_effect = RequestException("Request error occurred")

        with pytest.raises(Exception) as excinfo:
            request_handler.get("/test")
        assert "Request error occurred" in str(excinfo.value)


def test_post_success(request_handler):
    """Test successful POST request."""
    with patch("requests.Session.post") as mock_post:
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        mock_response.json.return_value = {"key": "value"}
        mock_post.return_value = mock_response

        response = request_handler.post("/test", json={"data": "test"})

        assert response == mock_response
        mock_post.assert_called_once_with(
            "https://dummyjson.com/test",
            params=None,
            data=None,
            json={"data": "test"},
            headers=None,
        )


def test_post_http_error(request_handler):
    """Test POST request handling HTTPError."""
    with patch("requests.Session.post") as mock_post:
        mock_post.side_effect = HTTPError("HTTP error occurred")

        with pytest.raises(Exception) as excinfo:
            request_handler.post("/test")
        assert "HTTP error occurred" in str(excinfo.value)


def test_post_request_exception(request_handler):
    """Test POST request handling RequestException."""
    with patch("requests.Session.post") as mock_post:
        mock_post.side_effect = RequestException("Request error occurred")

        with pytest.raises(Exception) as excinfo:
            request_handler.post("/test")
        assert "Request error occurred" in str(excinfo.value)


def test_put_success(request_handler):
    """Test successful PUT request."""
    with patch("requests.Session.put") as mock_put:
        mock_response = Mock(spec=Response)
        mock_response.status_code = 200
        mock_response.json.return_value = {"key": "value"}
        mock_put.return_value = mock_response

        response = request_handler.put("/test", data={"data": "test"})

        assert response == mock_response
        mock_put.assert_called_once_with(
            "https://dummyjson.com/test",
            params=None,
            data={"data": "test"},
            headers=None,
        )


def test_put_http_error(request_handler):
    """Test PUT request handling HTTPError."""
    with patch("requests.Session.put") as mock_put:
        mock_put.side_effect = HTTPError("HTTP error occurred")

        with pytest.raises(Exception) as excinfo:
            request_handler.put("/test")
        assert "HTTP error occurred" in str(excinfo.value)


def test_put_request_exception(request_handler):
    """Test PUT request handling RequestException."""
    with patch("requests.Session.put") as mock_put:
        mock_put.side_effect = RequestException("Request error occurred")

        with pytest.raises(Exception) as excinfo:
            request_handler.put("/test")
        assert "Request error occurred" in str(excinfo.value)
