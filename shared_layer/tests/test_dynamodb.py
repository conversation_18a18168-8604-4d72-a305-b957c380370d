import pytest

from src.utils.DynamoUtils import DynamoDBClient


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_create_item(aws_mock_test, dynamodb_client):

    item = {"id": "1", "name": "Test Item"}
    dynamodb_client.create_item(item)

    result = dynamodb_client.read_item({"id": "1"})
    assert result == item


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_read_item(aws_mock_test, dynamodb_client):

    item = {"id": "1", "name": "Test Item"}
    dynamodb_client.create_item(item)

    result = dynamodb_client.read_item({"id": "1"})
    assert result == item


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_update_item(aws_mock_test, dynamodb_client):
    item = {"id": "1", "item_name": "Test Item"}  # Changed from 'name' to 'item_name'
    dynamodb_client.create_item(item)

    updates = {"item_name": "Updated Item"}  # Changed from 'name' to 'item_name'

    dynamodb_client.update_item({"id": "1"}, updates)

    result = dynamodb_client.read_item({"id": "1"})

    assert result is not None, "Item should exist after update"
    assert (
        result["item_name"] == "Updated Item"
    ), "Item name should be updated"  # Changed from 'name' to 'item_name'


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_delete_item(aws_mock_test, dynamodb_client):

    item = {"id": "1", "name": "Test Item"}
    dynamodb_client.create_item(item)

    dynamodb_client.delete_item({"id": "1"})

    result = dynamodb_client.read_item({"id": "1"})
    assert result is None


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_query_items(aws_mock_test, dynamodb_client):
    # Create test items
    item1 = {"id": "1", "item_name": "Item 1"}
    item2 = {"id": "2", "item_name": "Item 2"}
    dynamodb_client.create_item(item1)
    dynamodb_client.create_item(item2)

    # Use '=' operator to query items
    conditions = {"id": ("=", "1")}  # Query for the item with id = "1"
    results = dynamodb_client.query_items(conditions)

    # Verify the results
    assert len(results) == 1
    assert results[0]["item_name"] == "Item 1"

    # Test querying with a filter condition
    conditions_with_filter = {"id": ("=", "1"), "item_name": ("=", "Item 1")}
    results_with_filter = dynamodb_client.query_items(conditions_with_filter)

    # Verify the results
    assert len(results_with_filter) == 1
    assert results_with_filter[0]["item_name"] == "Item 1"


@pytest.mark.parametrize("aws_mock_test", ["dynamodb"], indirect=True)
def test_scan_items(aws_mock_test, dynamodb_client):

    item1 = {"id": "1", "name": "Item 1"}
    item2 = {"id": "2", "name": "Item 2"}
    dynamodb_client.create_item(item1)
    dynamodb_client.create_item(item2)

    results = dynamodb_client.scan_items()
    assert len(results) == 2
    assert any(item["name"] == "Item 1" for item in results)
    assert any(item["name"] == "Item 2" for item in results)


# New Test for _build_condition method
@pytest.mark.parametrize(
    "operator, value",
    [
        ("=", "some_value"),
        ("!=", "some_value"),
        ("<", 10),
        ("<=", 10),
        (">", 10),
        (">=", 10),
    ],
)
def test_build_condition_valid_operators(operator, value):
    result = DynamoDBClient._build_condition("client_id", operator, value)

    assert result is not None  # Check that a condition object is returned


@pytest.mark.parametrize("operator", ["unsupported", "%", "&"])
def test_build_condition_invalid_operator(operator):
    with pytest.raises(ValueError, match="Unsupported operator:"):
        DynamoDBClient._build_condition("id", operator, "value")
