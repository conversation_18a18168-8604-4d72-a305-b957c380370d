import unittest
from unittest.mock import MagicMock, patch

from src.utils.extract import extract

MOCK_RULES = {
    "s3_extract": {
        "expected_params": ["bucket", "key"],
        "module": "mock_module",
        "class": "MockClass",
        "method": "mock_method",
    },
    "dynamo_extract": {
        "expected_params": ["table", "key", "method"],
        "module": "mock_module_dynamo",
        "class": "MockClassDynamo",
        "method": "",
    },
}


class TestExtractFunction(unittest.TestCase):
    @patch("src.utils.load.importlib.import_module")
    @patch("src.utils.extract.map_rules", new_callable=lambda: MOCK_RULES)
    def test_successful_extract(self, mock_rules, mock_import):
        # Mock the class and method for s3_extract
        mock_class = MagicMock()
        mock_instance = MagicMock()
        mock_method = MagicMock()
        mock_instance.mock_method = mock_method
        mock_method.return_value = {"data": MagicMock(read=lambda: b"mocked data")}
        mock_class.return_value = mock_instance
        mock_import.return_value.MockClass = mock_class

        # Input task for s3_extract
        task = {
            "extract": {
                "task": "s3_extract",
                "params": {"bucket": "test_bucket", "key": "file_key"},
            }
        }

        # Call the function
        result = extract(task)

        # Assertions
        mock_import.assert_called_once_with("mock_module")
        mock_method.assert_called_once_with("file_key")
        self.assertEqual(result, "mocked data")

    @patch("src.utils.load.importlib.import_module")
    @patch("src.utils.extract.map_rules", new_callable=lambda: MOCK_RULES)
    def test_successful_dynamo_extract(self, mock_rules, mock_import):
        # Mock the class and method for dynamo_extract
        mock_class_dynamo = MagicMock()
        mock_instance_dynamo = MagicMock()
        mock_method_dynamo = MagicMock()
        mock_instance_dynamo.read_item = mock_method_dynamo
        mock_method_dynamo.return_value = {"data": "mocked dynamo data"}
        mock_class_dynamo.return_value = mock_instance_dynamo
        mock_import.return_value.MockClassDynamo = mock_class_dynamo

        # Input task for dynamo_extract
        task = {
            "extract": {
                "task": "dynamo_extract",
                "params": {
                    "table": "test_table",
                    "key": "item_key",
                    "method": "read_item",
                },
            }
        }

        # Call the function
        result = extract(task)

        # Assertions
        mock_import.assert_called_once_with("mock_module_dynamo")
        mock_method_dynamo.assert_called_once_with("item_key")
        self.assertEqual(result, {"data": "mocked dynamo data"})

    @patch("src.utils.extract.map_rules", new_callable=lambda: MOCK_RULES)
    def test_task_not_allowed(self, mock_open_file):
        # Input task with an invalid task
        task = {
            "extract": {
                "task": "invalid_task",
                "params": {"bucket": "test_bucket", "key": "file_key"},
            }
        }
        # Call the function and expect an exception
        with self.assertRaises(Exception) as context:
            extract(task)
        self.assertIn("Task", str(context.exception))

    def test_extract_invalid_task(self):
        task = {
            "extract": {
                "task": "invalid_task",
                "params": {"bucket": "test-bucket", "key": "test/key"},
            }
        }

        with self.assertRaises(ValueError) as context:
            extract(task)
        self.assertIn("Task invalid_task not allowed", str(context.exception))

    def test_extract_missing_params(self):
        task = {
            "extract": {
                "task": "s3_extract",
                "params": {
                    "bucket": "test-bucket"
                    # 'key' is missing
                },
            }
        }

        with self.assertRaises(KeyError) as context:
            extract(task)
        self.assertIn("Missing required params in the request", str(context.exception))

    def test_dynamo_update_item_not_supported(self):
        task = {
            "extract": {
                "task": "dynamo_extract",
                "params": {
                    "table": "test_table",
                    "key": {"id": "123"},
                    "method": "update_item",
                },
            }
        }

        with self.assertRaises(ValueError) as context:
            extract(task)
        self.assertIn("not a read operation", str(context.exception))

    def test_extract_with_extra_params(self):
        # This test verifies that extra parameters are allowed (different from old behavior)
        task = {
            "extract": {
                "task": "s3_extract",
                "params": {
                    "bucket": "test-bucket",
                    "key": "test/key",
                    "extra_param": "extra_value",  # Extra parameter
                },
            }
        }

        # Mock the necessary components to prevent actual execution
        with patch("src.utils.extract.importlib.import_module") as mock_import:
            mock_class = MagicMock()
            mock_instance = MagicMock()
            mock_method = MagicMock()
            mock_instance.get_object = mock_method
            mock_method.return_value = {"data": MagicMock(read=lambda: b"mocked data")}
            mock_class.return_value = mock_instance
            mock_import.return_value.S3Utils = mock_class

            # This should not raise an exception with the new validation logic
            try:
                extract(task)
            except KeyError:
                self.fail(
                    "extract() raised KeyError unexpectedly with extra parameters!"
                )
