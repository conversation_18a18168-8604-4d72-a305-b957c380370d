import json
from unittest.mock import patch

import pytest
from botocore.exceptions import ClientError

from src.utils.SecretsManagerUtils import SecretsManagerUtils


@pytest.mark.parametrize("aws_mock_test", ["secretsmanager"], indirect=True)
def test_get_secret_value(aws_mock_test, secret_manager):
    secrets_manager_utils = aws_mock_test

    # Test retrieval of the secret value
    secret_name = "test-secret"
    secret_value = secret_manager.get_secret_value(secret_name)

    assert secret_value["username"] == "admin"
    assert secret_value["password"] == "password123"


@pytest.mark.parametrize("aws_mock_test", ["secretsmanager"], indirect=True)
def test_get_secret_value_not_found(aws_mock_test, secret_manager):
    secrets_manager_utils = aws_mock_test

    # Test retrieval of a non-existent secret value, should raise a ValueError
    with pytest.raises(ValueError, match="Failed to retrieve secret"):
        secret_manager.get_secret_value("non-existent-secret")


@pytest.mark.parametrize("aws_mock_test", ["secretsmanager"], indirect=True)
def test_get_secret_value_missing_secret_string(aws_mock_test):
    secrets_manager_utils = SecretsManagerUtils()

    # Mock the response to simulate missing SecretString
    with patch.object(
        secrets_manager_utils.client, "get_secret_value", return_value={}
    ):
        with pytest.raises(
            ValueError, match="SecretString not found in response for secret:"
        ):
            secrets_manager_utils.get_secret_value("test-secret")
