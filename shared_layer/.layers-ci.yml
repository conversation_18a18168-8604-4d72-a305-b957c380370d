image: python:3.12.6

workflow:
  rules:
    - changes:
      - shared_layer/**/*
      when: always
    - when: never

variables:
  PACKAGE: "layer.zip"
  BUCKET: "tacdev-artifacts-${ENV}"
  FOLDER: "layers"
  LAYER: "shared-layer-${ENV}"

stages:
  - test
  - build
  - deploy

# Test job for layer
test-code-layer:
  stage: test
  script:
    - pip install uv
    - uv sync --all-groups
  allow_failure: false
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push" || $PARENT_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - shared_layer/**/*

# Build job for layer
build-layer:
  stage: build
  before_script:
    - apt-get update && apt-get install -y zip
  script:
    - echo "building layer"
    - mkdir python/ && cp -r shared_layer/src/utils/ python/utils/
    - pip install uv
    - uv lock
    - uv export --frozen --no-dev --no-editable -o requirements.txt
    - uv pip install -r requirements.txt --target python/
    - echo "Creating zip layer"
    - zip -r $PACKAGE python/
  artifacts:
    paths:
      - $PACKAGE
  rules:
    - if: $PARENT_PIPELINE_SOURCE == "push" || $PARENT_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - shared_layer/**/*

# template to deploy AWS Layer
.deploy-template:
  stage: deploy
  before_script:
    - bash scripts/setup_aws.sh
  script:
    - echo "Deploying Layer function for $CI_ENVIRONMENT_NAME"
    - bash scripts/deploy_layer.sh $PACKAGE $LAYER $BUCKET $FOLDER
  rules:
    - changes:
        - shared_layer/**/*

# Deploy job for the sandbox environment
deploy-layer-sbx:
  extends: .deploy-template
  environment:
    name: sbx
  rules:
    - if: $CI_COMMIT_TAG =~ /^release\/[0-9]+\.[0-9]+\.[0-9]+-sbx$/
      changes:
        - shared_layer/**/*

# Deploy job for the qa environment
deploy-layer-qa:
  extends: .deploy-template
  environment:
    name: qa
  rules:
    - if: $CI_COMMIT_BRANCH == "qa" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - shared_layer/**/*

# Deploy job for the production environment
deploy-layer-prod:
  extends: .deploy-template
  environment:
    name: prod
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $PARENT_PIPELINE_SOURCE == "push"
      changes:
        - shared_layer/**/*
