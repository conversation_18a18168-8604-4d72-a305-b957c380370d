from utils.config import settings_config
from utils.LoggerService import logger_service
from utils.schemas.dynamodb.akamai import AkamaiConfig
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .flow import flow


def main() -> None:
    """
    This is the entry point for akamai linode container
    Simulates the lambda handler functionality for local execution
    """
    logger_service.info(
        "************ IZMA / Running Akamai Linode Container ************"
    )
    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    event_settings = settings_service.initialize("akamai-linode")
    config: AkamaiConfig = event_settings.get_config()  # type: ignore
    status_table_name = event_settings.get_status_table()
    landing_bucket = config.landing_bucket
    destination_bucket = config.destination_bucket
    secret_key = config.secret_key
    prefix = config.filename_prefix

    status_tracker = StatusTrackerSchema(landing_bucket=landing_bucket)
    process_status_tracker = ProcessStatusTracker(status_table_name)
    process_status_tracker.create_initial_status(create_item=status_tracker)

    global_variables = {
        "landing_bucket": landing_bucket,
        "destination_bucket": destination_bucket,
        "secret_key": secret_key,
        "status_table_name": status_table_name,
        "filename_prefix": prefix,
    }

    try:
        process_status_tracker.mark_as_running(status_tracker.client_id)

        flow(global_variables)

        process_status_tracker.mark_as_completed(status_tracker.client_id)

        logger_service.info(
            "************ IZMA / Akamai Linode Container Finished ************"
        )

    except BaseException as error:
        logger_service.error(
            "IZMA ERROR: %s, event_project: %s",
            error,
            event_settings.event_project_name,
        )
        error_message = f"{error}"
        process_status_tracker.mark_as_failed(status_tracker.client_id, error_message)


if __name__ == "__main__":
    main()
