from datetime import datetime as dt
from datetime import timedelta
from typing import Any, Dict

from utils.config import ENV, settings_config
from utils.expresslane_utils import logger_service
from utils.extract import extract, plain_extract
from utils.load import load
from utils.tasks.data_processing import download_s3_object
from utils.tasks.external import get_linode_file_metadata, upload_file_to_linode


def flow(global_variables: Dict[str, Any]) -> None:
    """
    Queries and retries loading failed reach-elements files to Linode.

    This method combines searching for failed records and retrying process
    in a single function, improving code cohesion.

    Args:
        event_settings (Dict[str, Any]): Event configuration settings
        global_variables (Dict[str, Any]): Global variables including secret_key and other configs
    """
    logger_service.info("************ IZMA / akamai linode flow started ************")
    logger_service.info("IZMA / akamai linode flow with args: \n %s", global_variables)
    # Query DynamoDB to get failed records
    log_table_name = settings_config.get_log_table_name

    # Get current date for comparison - use midnight for the one_day_ago value numus one day
    one_day_ago = (
        (dt.now() - timedelta(days=1))
        .replace(hour=0, minute=0, second=0, microsecond=0)
        .isoformat()
    )

    # Create the task definition for DynamoDB query operation
    # Using paginated_grl_query_items with support for "contains" operator
    task_def = {
        "extract": {
            "task": "dynamo_extract",
            "params": {
                "table": log_table_name,
                "key": {
                    "process_status": ("=", "FAILED"),
                    "landing_bucket": ("contains", "reach-elements"),
                },
                "method": "paginated_grl_query_items",
            },
        }
    }

    # Execute the query to get failed items
    failed_items = extract(task_def)

    # Query for running items that are older than one day
    running_task_def = {
        "extract": {
            "task": "dynamo_extract",
            "params": {
                "table": log_table_name,
                "key": {
                    "process_status": ("=", "Running"),
                    "created_at": ("<", one_day_ago),
                    "landing_bucket": ("contains", "reach-elements"),
                },
                "method": "paginated_grl_query_items",
            },
        }
    }

    # Execute the query for running items and merge with failed items
    running_items = extract(running_task_def)
    failed_items.extend(running_items)

    # Process the failed items
    secret_key = global_variables.get("secret_key")
    prefix = global_variables.get("filename_prefix", "")
    # Status table name
    status_table_name = global_variables.get("status_table_name")

    for failed_item in failed_items:
        file_name = failed_item.get("client_id")
        if not file_name:
            continue

        # Check if the file exists in Linode
        destination_file_name = f"{prefix}{file_name}"
        file_exists_linode = get_linode_file_metadata(secret_key, destination_file_name)
        current_retries = failed_item.get("retries", 0) if failed_item else 0

        if file_exists_linode is None:  # The file doesn't exist in Linode
            try:
                # Extract data from source bucket
                prev = download_s3_object(
                    data=None,
                    key=file_name,
                    connector=plain_extract,
                    bucket=failed_item.get("landing_bucket"),
                )

                # Retry upload to Linode
                upload_file_to_linode(prev, secret_key, destination_file_name)

                # Get and increment retry counter

                # Update status using load.py instead of direct update_item
                update_task = {
                    "load": {
                        "task": "dynamo_upload",
                        "params": {
                            "table": status_table_name,
                            "key": {"client_id": file_name},
                            "method": "update_item",
                            "value": {
                                "process_status": "COMPLETED",
                                "more_details": "Retry completed successfully on akamai-linode",
                                "updated_at": dt.now().isoformat(),
                                "retries": current_retries + 1,
                            },
                        },
                    }
                }
                load(update_task)

                logger_service.info(
                    "Successfully retried upload for file: %s", file_name
                )

            except Exception as error:
                logger_service.error(
                    "Failed to retry upload for file %s: %s", file_name, str(error)
                )

                # Update error status using load.py
                error_update_task = {
                    "load": {
                        "task": "dynamo_upload",
                        "params": {
                            "table": status_table_name,
                            "key": {"client_id": file_name},
                            "method": "update_item",
                            "value": {
                                "process_status": "FAILED",
                                "more_details": f"Retry failed: {error} on akamai-linode",
                                "updated_at": dt.now().isoformat(),
                                "retries": current_retries + 1,
                            },
                        },
                    }
                }
                load(error_update_task)
                continue
        else:
            logger_service.info("File %s already exists in Linode", file_name)

            # Update status for file that already exists using load.py
            exists_update_task = {
                "load": {
                    "task": "dynamo_upload",
                    "params": {
                        "table": status_table_name,
                        "key": {"client_id": file_name},
                        "method": "update_item",
                        "value": {
                            "process_status": "COMPLETED",
                            "more_details": "File already exists in Linode",
                            "updated_at": dt.now().isoformat(),
                            "retries": current_retries + 1,
                        },
                    },
                }
            }
            load(exists_update_task)

    logger_service.info("IZMA / akamai linode flow finished")
