# Image
FROM python:3.12

# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1

# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1

# Set working directory
WORKDIR /app

# Copy the project dependencies
COPY pyproject.toml /app/
COPY /shared_layer/ /app/common_lib
COPY . /app

# Install uv
RUN pip install uv
# Automatically creates a virtual environment (.venv/) if it doesn't exist
# Reads pyproject.toml file if present
# Installs all dependencies for all groups defined in pyproject.toml
RUN uv sync --all-groups

# Creates a non-root user with an explicit UID and adds permission to access the /app folder
RUN adduser -u 5678 --disabled-password --gecos "" appuser && chown -R appuser /app
USER appuser

# Use the default virtual environment
# ENV PATH="/app/.venv/bin:$PATH"

# Runs the application's main module (src/main.py) when the container starts
# CMD ["python", "-m", "src.main"]
CMD ["uv", "run", "python", "-m", "src.main"]
