from unittest import TestCase
from unittest.mock import MagicMock, patch

from bebanjo.src.bebanjo_flow import flow

# NOTE: Why Bebanjo and reach are here?
from src.lambda_reach import lambda_handler


class TestReachLambda(TestCase):
    @patch("utils.tasks.package_metadata.fetch_reach_package_metadata")
    @patch("utils.tasks.package_metadata.update_reach_package_metadata")
    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.token_service")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_happy_path(
        self,
        mock_logger_service,
        mock_token_service,
        mock_dynamo_client,
        mock_update_metadata,
        mock_get_metadata,
    ):
        # Setup mocks
        mock_dynamo_client.read_item.return_value = {
            "status": "wl_successful_ingestion",
            "attribute_name": "mock_attr",
            "reach_package_id": "mock_id",
            "wonderland_id": "mock_id",
        }
        mock_token_service.reach_secrets = {"reach_url": "http://test.com"}
        mock_token_service.create_token.return_value = "test_token"
        mock_get_metadata.return_value = {
            "metadata": {"properties": {"mock_attr": {"value": ""}}}
        }
        mock_update_metadata.return_value = True

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute
        lambda_handler(event, "")

        # Verify
        mock_dynamo_client.read_item.assert_called_once()
        mock_token_service.create_token.assert_called_once()
        mock_get_metadata.assert_called_once()
        mock_update_metadata.assert_called_once()
        mock_dynamo_client.update_item.assert_called_once_with(
            {"sidecar_id": "mock_id"}, {"status": "wl_reach_completed"}
        )

    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_no_record_in_dynamo(
        self, mock_logger_service, mock_dynamo_client
    ):
        # Setup
        mock_dynamo_client.read_item.return_value = None

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute and verify
        with self.assertRaises(KeyError) as context:
            lambda_handler(event, "")

        self.assertIn("No sidecar", str(context.exception))
        mock_dynamo_client.read_item.assert_called_once()
        mock_dynamo_client.update_item.assert_called_once_with(
            {"sidecar_id": "mock_id"}, {"status": "Error"}
        )

    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_missing_key_record(
        self, mock_logger_service, mock_dynamo_client
    ):
        # Setup
        mock_dynamo_client.read_item.return_value = {"abcd": ""}

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute and verify
        with self.assertRaises(KeyError) as context:
            lambda_handler(event, "")

        self.assertIn("not found in record", str(context.exception))
        mock_dynamo_client.read_item.assert_called_once()
        mock_dynamo_client.update_item.assert_called_once_with(
            {"sidecar_id": "mock_id"}, {"status": "Error"}
        )

    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_missing_wonderland_id(
        self, mock_logger_service, mock_dynamo_client
    ):
        # Setup
        mock_dynamo_client.read_item.return_value = {
            "status": "wl_successful_ingestion",
            "attribute_name": "mock_attr",
            "reach_package_id": "mock_id",
        }

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute and verify
        with self.assertRaises(KeyError) as context:
            lambda_handler(event, "")

        self.assertIn("wonderland_id is not", str(context.exception))
        mock_dynamo_client.read_item.assert_called_once()
        mock_dynamo_client.update_item.assert_called_once_with(
            {"sidecar_id": "mock_id"}, {"status": "Error"}
        )

    @patch("utils.tasks.package_metadata.fetch_reach_package_metadata")
    @patch("utils.tasks.package_metadata.update_reach_package_metadata")
    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.token_service")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_metadata_not_found(
        self,
        mock_logger_service,
        mock_token_service,
        mock_dynamo_client,
        mock_update_metadata,
        mock_get_metadata,
    ):
        # Setup
        mock_dynamo_client.read_item.return_value = {
            "status": "wl_successful_ingestion",
            "attribute_name": "mock_attr",
            "reach_package_id": "mock_id",
            "wonderland_id": "mock_id",
        }
        mock_token_service.reach_secrets = {"reach_url": "http://test.com"}
        mock_token_service.create_token.return_value = "test_token"
        mock_get_metadata.side_effect = ValueError(
            "Response from reach is not success 404"
        )

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute and verify
        with self.assertRaises(ValueError) as context:
            lambda_handler(event, "")

        self.assertEqual(
            "Response from reach is not success 404", str(context.exception)
        )
        mock_dynamo_client.read_item.assert_called_once()
        mock_token_service.create_token.assert_called_once()
        mock_get_metadata.assert_called_once()
        mock_dynamo_client.update_item.assert_called_once_with(
            {"sidecar_id": "mock_id"}, {"status": "Error"}
        )

    @patch("utils.tasks.package_metadata.fetch_reach_package_metadata")
    @patch("utils.tasks.package_metadata.update_reach_package_metadata")
    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.token_service")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_update_reach_is_not_success(
        self,
        mock_logger_service,
        mock_token_service,
        mock_dynamo_client,
        mock_update_metadata,
        mock_get_metadata,
    ):
        # Setup
        mock_dynamo_client.read_item.return_value = {
            "status": "wl_successful_ingestion",
            "attribute_name": "mock_attr",
            "reach_package_id": "mock_id",
            "wonderland_id": "mock_id",
        }
        mock_token_service.reach_secrets = {"reach_url": "http://test.com"}
        mock_token_service.create_token.return_value = "test_token"
        mock_get_metadata.return_value = {
            "metadata": {"properties": {"mock_attr": {"value": ""}}}
        }
        mock_update_metadata.return_value = False

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute
        lambda_handler(event, "")

        # Verify
        mock_dynamo_client.read_item.assert_called_once()
        mock_token_service.create_token.assert_called_once()
        mock_get_metadata.assert_called_once()
        mock_update_metadata.assert_called_once()
        mock_dynamo_client.update_item.assert_called_once_with(
            {"sidecar_id": "mock_id"}, {"status": "Error"}
        )

    @patch("src.lambda_reach.dynamo_client")
    @patch("src.lambda_reach.logger_service")
    def test_reachlambda_invalid_status(self, mock_logger_service, mock_dynamo_client):
        # Setup
        mock_dynamo_client.read_item.return_value = {
            "status": "invalid",
            "attribute_name": "mock_attr",
            "reach_package_id": "mock_id",
            "wonderland_id": "mock_id",
        }

        # Test input
        event = {"wonderland": {"sidecar_id": "mock_id"}}

        # Execute and verify
        with self.assertRaises(ValueError) as context:
            lambda_handler(event, "")

        self.assertIn("is not completed", str(context.exception))
        mock_dynamo_client.read_item.assert_called_once()
