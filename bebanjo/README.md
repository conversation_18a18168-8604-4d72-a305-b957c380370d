# Bebanjo to Reach

## Architecture
```
tree
```

## Test and Deploy
- ./shared_layer
    - pip install -e .
    - pytest

## Tools
- Python 3.12
- Boto3
- AWS
- Pycharm CE
- SAM CLI


## ENVIRONMENT VARIABLES
### Define de following env variables in you .env (local):
- **STAGE**: The stage where you are running this (production, development or localyournamelastname)
- **AWS_ACCESS_KEY_ID**: Your aws secrete
- **AWS_SECRET_ACCESS_KEY**: Your aws access key
- **AWS_DEFAULT_REGION**: Your aws region

