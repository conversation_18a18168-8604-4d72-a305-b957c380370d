import os

import pytest


@pytest.fixture
def secrets_managerUtils() -> dict:
    return {
        "Cablevision_Altice_Workflow": {
            "host": "**************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100M",
            "target": "/DATG_VOD",
        },
        "Alta_Fiber_Workflow": {
            "host": "**************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/storage/aspera/drop",
        },
        "Armstrong_Workflow": {
            "host": "************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/",
        },
        "Astound_RCN_Workflow": {
            "host": "***********",
            "port": "22",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/",
        },
        "ATT_Deluxe_Workflow": {
            "host": "aspera-live.deluxeone.com",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "300000",
            "target": "/ATT/STB",
        },
        "Blue_Ridge_Workflow": {
            "host": "************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/DATG_VOD",
        },
        "BlueStream_Fiber_Workflow": {
            "host": "**************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/",
        },
        "Breezeline_Workflow": {
            "host": "**************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/vod/abcipvod",
        },
        "Buckeye_Workflow": {
            "host": "*************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/",
        },
        "C-Spire_Workflow": {
            "host": "cspire-vod-prod.cspire.com",
            "port": "22",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "40000",
            "target": "/",
        },
        "COX_Workflow": {
            "host": "*************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "/opt/aspera/var/config/orchestrator/certs/cox_asp-disney",
            "authentication": "Public Key",
            "target_rate": "100000",
            "target": "/",
        },
        "CSI_Digital_Workflow": {
            "host": "**************",
            "port": "33001",
            "username": "TBD",
            "pwd_or_key": "TBD",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/home/<USER>",
        },
        "Frontier_Workflow": {
            "host": "*************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/",
        },
        "GCI_Workflow": {
            "host": "***********",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "50000",
            "target": "/ingested",
        },
        "HBC_Workflow": {
            "host": "**************",
            "port": "22",
            "username": "",
            "pwd_or_key": "TBD",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/mnt/vod_backup/fx_ngc",
        },
        "Logic_Workflow": {
            "host": "*************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "50000",
            "target": "/",
        },
        "Mediacom_Workflow": {
            "host": "**************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "50000",
            "target": "/",
        },
        "MCTV_Workflow": {
            "host": "************",
            "port": "22",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/fxngc",
        },
        "OzarksGo_Workflow": {
            "host": "*************",
            "port": "33001",
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": "100000",
            "target": "/",
        },
        "Vermont_Telephone_Workflow": {
            "host": "**************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/DATG_VOD",
        },
        "Verizon_Lab_Workflow": {
            "host": "*************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/DATG_VOD",
        },
        "Verizon_STB_Workflow": {
            "host": "**************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/VOD",
        },
        "Vubiquity_WOW_Workflow": {
            "host": "aspera.vubiquity.com",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/DATG_VOD",
        },
        "MobiTV_Workflow": {
            "host": "**************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/",
        },
        "SECTV_WG": {
            "host": "*************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/DATG_VOD",
        },
        "Tangerine_Allo_Workflow": {
            "host": "************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Password",
            "target_rate": 100000,
            "target": "/",
        },
        "TDS_Broadband_Workflow": {
            "host": "*************",
            "port": 22,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Public Key",
            "target_rate": 100000,
            "target": "/",
        },
        "TDS_Telecom_Workflow": {
            "host": "************",
            "port": 22,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Public Key",
            "target_rate": 100000,
            "target": "/",
        },
        "WideOpenWest_Detroit_Workflow": {
            "host": "**************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Public Key",
            "target_rate": 120000,
            "target": "/",
        },
        "WideOpenWest_WestPoint_Workflow": {
            "host": "**************",
            "port": 33001,
            "username": "",
            "pwd_or_key": "",
            "authentication": "Public Key",
            "target_rate": 120000,
            "target": "/",
        },
    }


@pytest.fixture
def rules() -> dict:
    return {
        "config_rules": {
            "rules_category": {
                "key_split": "@Name",
                "path_split": "ADI.Asset.Metadata.App_Data",
                "value_split": "Category",
            },
            "task_config": {
                "filter_value": "HDContent",
                "key_value": "@Value",
                "metadata_movie_path": "master.metadata_movie.metadata",
                "metadata_season_path": "master.metadata_season.metadata",
                "network_name_path": "master.package_info.network_name",
            },
        },
        "Alta Fiber": {
            "delivery_name": "Alta_Fiber_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "altaFiberMovieCategoryMapping",
            "HD_series_category": "altaFiberSeriesCategoryMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "altaFiberMovieCategoryMapping",
            "SD_series_category": "altaFiberSeriesCategoryMapping",
            "skip_content": {"validation": True, "values_skip": ["FXX", "FXM"]},
            "tar": False,
            "update_attributes": [],
        },
        "Antietam": {
            "delivery_name": "Antietam_Cable_WG",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "Armstrong": {
            "delivery_name": "Armstrong_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "armstrongMovieCategoryMapping",
            "HD_series_category": "armstrongSeriesSeasonCategoryMapping",
            "limit_length": {
                "length": 32,
                "value_filter": "Title",
                "key_filter": "@Value",
                "validation": True,
            },
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "armstrongMovieCategoryMapping",
            "SD_series_category": "armstrongSeriesSeasonCategoryMapping",
            "skip_content": {"validation": True, "values_skip": ["FXM"]},
            "tar": False,
            "update_attributes": [],
        },
        "Astound (RCN)": {
            "delivery_name": "Astound_RCN_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "astoundMovieCategoryMapping",
            "HD_series_category": "astoundSeriesSeasonCategoryMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "astoundMovieCategoryMapping",
            "SD_series_category": "astoundSeriesSeasonCategoryMapping",
            "skip_content": {},
            "tar": True,
            "update_attributes": [],
        },
        "ATT Deluxe": {
            "delivery_name": "ATT_Deluxe_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": [],
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Blue Ridge": {
            "delivery_name": "Blue_Ridge_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "BlueStream Fiber": {
            "delivery_name": "BlueStream_Fiber_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "bluestreamMovieCategoryMapping",
            "HD_series_category": "bluestreamSeriesCategoryMapping",
            "remove_attributes": [],
            "replace_attributes": [],
            "SD_movies_category": "bluestreamMovieCategoryMapping",
            "SD_series_category": "bluestreamSeriesCategoryMapping",
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Breezeline": {
            "delivery_name": "Breezeline_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "breezelineMovieCategoryMapping",
            "HD_series_category": "breezelineSeriesCategoryMapping",
            "remove_attributes": {},
            "SD_movies_category": "breezelineMovieCategoryMapping",
            "SD_series_category": "breezelineSeriesCategoryMapping",
            "tar": False,
            "skip_content": {"values_skip": []},
        },
        "Buckeye": {
            "delivery_name": "Buckeye_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "buckeyeMovieCategoryMapping",
            "HD_series_category": "buckeyeSeriesCategoryMapping",
            "remove_attributes": {
                "filter_key": "@Name",
                "keys": [
                    "Ad_Content_ID",
                    "Season_Number",
                    "Series_Name",
                    "TMS_Episode_ID",
                    "TMS_Series_ID",
                ],
                "path": "ADI.Asset.Metadata.App_Data",
            },
            "replace_attributes": [],
            "SD_movies_category": "buckeyeMovieCategoryMapping",
            "SD_series_category": "buckeyeSeriesCategoryMapping",
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "C-Spire": {
            "delivery_name": "C-Spire_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Cablevision (Altice)": {
            "delivery_name": "Cablevision_Altice_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "cablevisionAlticeMovieHDCategoryMapping",
            "HD_series_category": "cablevisionAlticeSeriesHDCategoryMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "cablevisionAlticeMovieSDCategoryMapping",
            "SD_series_category": "cablevisionAlticeSeriesSDCategoryMapping",
            "skip_content": {},
            "tar": True,
            "update_attributes": [
                {
                    "key_value_filter": "@Value",
                    "value_filter": "Original_Air_Date",
                    "value_update": "T00:00:00",
                }
            ],
        },
        "Cinergy Metronet": {
            "delivery_name": "Cinergy_Metronet_Workflow",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "ClaroTV": {
            "delivery_name": "ClaroTV_WG",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {"validation": True, "values_skip": ["FXX"]},
            "tar": False,
            "update_attributes": [],
        },
        "Cox": {
            "delivery_name": "COX_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "coxMovieCategoryHDMapping",
            "HD_series_category": "coxSeriesSeasonCategoryHDMapping",
            "remove_attributes": {},
            "replace_attributes": [
                {"key_value_filter": "coxProductCode", "value_filter": "@Product"},
                {
                    "filter_by": "@Value",
                    "key_value_filter": "affiliateHDPCT2",
                    "value_filter": "Provider_Content_Tier",
                },
                {"key_value_filter": "coxProvider", "value_filter": "@Provider"},
            ],
            "SD_movies_category": "coxMovieCategorySDMapping",
            "SD_series_category": "coxSeriesSeasonCategorySDMapping",
            "skip_content": {},
            "tar": True,
            "update_attributes": [],
        },
        "CSI Digital": {
            "delivery_name": "CSI_Digital_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "EPB": {
            "delivery_name": "Electric_Power_Board_WG",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Frontier": {
            "delivery_name": "Frontier_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "GCI": {
            "delivery_name": "GCI_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "HBC": {
            "delivery_name": "HBC_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Hotwire": {
            "delivery_name": "Hotwire_Communications_WG",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Logic": {
            "delivery_name": "Logic_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "MCTV": {
            "delivery_name": "MCTV_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "mctvMovieCategoryMapping",
            "HD_series_category": "mctvSeriesSeasonCategoryMapping",
            "remove_attributes": {
                "filter_key": "@Name",
                "keys": [
                    "Ad_Content_ID",
                    "Season_Number",
                    "Series_Name",
                    "TMS_Episode_ID",
                    "Original_Air_Date",
                    "TMS_Series_ID",
                ],
                "path": "ADI.Asset.Metadata.App_Data",
            },
            "replace_attributes": [],
            "SD_movies_category": "mctvMovieCategoryMapping",
            "SD_series_category": "mctvSeriesSeasonCategoryMapping",
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "Mediacom": {
            "delivery_name": "Mediacom_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "mediacomMovieCategoryMapping",
            "HD_series_category": "mediacomSeriesSeasonCategoryMapping",
            "remove_attributes": {},
            "replace_attributes": [
                {"key_value_filter": "mediacomProvider", "value_filter": "@Provider"}
            ],
            "SD_movies_category": "mediacomMovieCategoryMapping",
            "SD_series_category": "mediacomSeriesSeasonCategoryMapping",
            "skip_content": {},
            "tar": False,
            "update_attributes": [],
        },
        "MobiTV": {
            "delivery_name": "MobiTV_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "mobiMovieCategoryMapping",
            "HD_series_category": "mobiSeriesCategoryMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "mobiMovieCategoryMapping",
            "SD_series_category": "mobiSeriesCategoryMapping",
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "OzarksGo": {
            "delivery_name": "OzarksGo_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "SECTV": {
            "delivery_name": "SECTV_WG",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "sectvMovieCategoryHDMapping",
            "HD_series_category": "sectvSeriesSeasonCategoryHDMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "sectvMovieCategorySDMapping",
            "SD_series_category": "sectvSeriesSeasonCategorySDMapping",
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "SECTV LehighValley": {
            "delivery_name": "SECTV_LehighValley_WG",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "SECTV Sparta NJ": {
            "delivery_name": "SECTV_Sparta_WG",
            "faspex": True,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "Tangerine (Allo)": {
            "delivery_name": "Tangerine_Allo_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "tangerineAlloMovieCategoryMapping",
            "HD_series_category": "tangerineAlloSeriesCategoryMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "tangerineAlloMovieCategoryMapping",
            "SD_series_category": "tangerineAlloSeriesCategoryMapping",
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "TDS Broadband": {
            "delivery_name": "TDS_Broadband_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "remove_attributes": {},
            "replace_attributes": [],
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "TDS Telecom": {
            "delivery_name": "TDS_Telecom_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "tdsTelecomMovieCategoryHDMapping",
            "HD_series_category": "tdsTelecomSeriesSeasonCategoryHDMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "tdsTelecomMovieCategorySDMapping",
            "SD_series_category": "tdsTelecomSeriesSeasonCategorySDMapping",
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "Verizon Lab": {
            "delivery_name": "Verizon_Lab_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "verizonMovieCategoryHDMapping",
            "HD_series_category": "verizonSeriesSeasonCategoryHDMapping",
            "remove_attributes": {},
            "replace_attributes": [],
            "SD_movies_category": "verizonMovieCategorySDMapping",
            "SD_series_category": "verizonSeriesSeasonCategorySDMapping",
            "skip_content": {"validation": False, "values_skip": []},
            "tar": False,
            "update_attributes": [],
        },
        "Verizon STB": {
            "delivery_name": "Verizon_STB_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "verizonMovieCategoryHDMapping",
            "HD_series_category": "verizonSeriesSeasonCategoryHDMapping",
            "remove_attributes": {},
            "SD_movies_category": "verizonMovieCategorySDMapping",
            "SD_series_category": "verizonSeriesSeasonCategorySDMapping",
        },
        "Vermont Telephone": {
            "delivery_name": "Vermont_Telephone_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "replace_attributes": [],
            "update_attributes": [],
            "skip_content": {"values_skip": []},
        },
        "Vubiquity WOW": {
            "delivery_name": "Vubiquity_WOW_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "vubiquityWOWMovieCategoryHDMapping",
            "HD_series_category": "vubiquityWOWSeriesCategoryHDMapping",
            "remove_attributes": {
                "filter_key": "@Name",
                "keys": [
                    "Ad_Content_ID",
                    "Season_Number",
                    "Series_Name",
                    "TMS_Episode_ID",
                    "TMS_Series_ID",
                ],
                "path": "ADI.Asset.Metadata.App_Data",
            },
            "replace_attributes": [],
            "SD_movies_category": "vubiquityWOWMovieCategorySDMapping",
            "SD_series_category": "vubiquityWOWSeriesCategorySDMapping",
            "update_attributes": [],
        },
        "WOW Detroit": {
            "delivery_name": "WideOpenWest_Detroit_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "wowDetroitMovieCategoryMapping",
            "HD_series_category": "wowDetroitSeriesCategoryMapping",
            "remove_attributes": {
                "filter_key": "@Name",
                "keys": [
                    "Ad_Content_ID",
                    "Season_Number",
                    "Series_Name",
                    "TMS_Episode_ID",
                    "TMS_Series_ID",
                ],
                "path": "ADI.Asset.Metadata.App_Data",
            },
            "replace_attributes": [],
            "SD_movies_category": "wowDetroitMovieCategoryMapping",
            "SD_series_category": "wowDetroitSeriesCategoryMapping",
            "skip_content": {
                "validation": True,
                "values_skip": ["FXX", "FXM", "Nat Geo Wild"],
            },
            "update_attributes": [],
        },
        "WOW West Point": {
            "delivery_name": "WideOpenWest_WestPoint_Workflow",
            "faspex": False,
            "has_HD": True,
            "has_SD": True,
            "HD_movies_category": "wowWestpointMovieCategoryMapping",
            "HD_series_category": "wowWestpointSeriesCategoryMapping",
            "remove_attributes": {
                "filter_key": "@Name",
                "keys": [
                    "Ad_Content_ID",
                    "Season_Number",
                    "Series_Name",
                    "TMS_Episode_ID",
                    "TMS_Series_ID",
                ],
                "path": "ADI.Asset.Metadata.App_Data",
            },
            "replace_attributes": [],
            "SD_movies_category": "wowWestpointMovieCategoryMapping",
            "SD_series_category": "wowWestpointSeriesCategoryMapping",
            "skip_content": {
                "validation": True,
                "values_skip": ["FXX", "FXM", "Nat Geo Wild"],
            },
            "update_attributes": [],
        },
    }


@pytest.fixture
def global_variables(secrets_managerUtils, rules) -> dict:
    return {
        "id": "CrashCourseCuisinewi_S01E02_D4_10986638_HD",
        "secrets_manager": secrets_managerUtils,
        "event_settings_rules": rules,
        "config_rules": rules.get("config_rules"),
    }


@pytest.fixture
def global_variables_ATT_DELUX(secrets_managerUtils, rules) -> dict:
    return {
        "id": "IncredibleNorthernVe_S01E02_D4_10976545_HD",
        "secrets_manager": secrets_managerUtils,
        "event_settings_rules": rules,
        "config_rules": rules.get("config_rules"),
    }


@pytest.fixture
def root_path():
    return os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))


@pytest.fixture
def target_dir(root_path) -> list:
    project_base_path = root_path
    target_dir = os.path.join(
        project_base_path, "tests", "affiliates", "files", "input"
    )

    return target_dir


@pytest.fixture
def local_bucket(target_dir):
    def file_selection(file_select):
        files = {
            "CrashCourseCuisinewi_S01E02_D4_10986638_HD": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
            "IncredibleNorthernVe_S01E02_D4_10976545_HD": "IncredibleNorthernVe_S01E02_D4_10976545_HD.xml",
        }

        file_path = f"{target_dir}/{files.get(file_select)}"

        with open(file_path, "r", encoding="utf-8") as file:
            return file.read()

    return file_selection
