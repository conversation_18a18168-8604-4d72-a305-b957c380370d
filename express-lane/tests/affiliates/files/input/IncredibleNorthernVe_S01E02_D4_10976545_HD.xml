<?xml version="1.0" encoding="UTF-8"?>
<master>
<ADI>
	<Metadata>
		<AMS Asset_Class="package" Asset_ID="NGWC1097654500001201" Asset_Name="NGWC1097654500001201_package" Creation_Date="2025-02-27" Description="NGWWVNI102_HD_package asset" Product="MOD" Provider="NATGEOWILD_HD" Provider_ID="natgeowild.com" Version_Major="7" Version_Minor="0" Verb=""/>
		<App_Data App="MOD" Name="Provider_Content_Tier" Value="NAT_GEO_WILD_HD"/>
		<App_Data App="MOD" Name="Metadata_Spec_Version" Value="CableLabsVOD1.1"/>
	</Metadata>
	<Asset>
		<Metadata>
			<AMS Asset_Class="title" Asset_ID="NGWC1097654500001202" Asset_Name="NGWC1097654500001202_title" Creation_Date="2025-02-27" Description="NGWWVNI102_HD_title" Product="MOD" Provider="NATGEOWILD_HD" Provider_ID="natgeowild.com" Version_Major="7" Version_Minor="0" Verb=""/>
			<App_Data App="MOD" Name="Type" Value="title"/>
			<App_Data App="MOD" Name="Title" Value="Incredible Northern Vets"/>
			<App_Data App="MOD" Name="Title_Sort_Name" Value="Incredibl 102 HD"/>
			<App_Data App="MOD" Name="Title_Brief" Value="Incredibl 102 HD"/>
			<App_Data App="MOD" Name="Summary_Short" Value="A vet's first day, and a veteran vet cares for a cat with a broken leg."/>
			<App_Data App="MOD" Name="Summary_Long" Value="New vet Dr. Emma has her first day at a busy Calgary shelter, while Dr. Allison cares for an old cat with a broken leg."/>
			<App_Data App="MOD" Name="Series_Name" Value="Incredible Northern Vets"/>
			<App_Data App="MOD" Name="Season_Number" Value="1"/>
			<App_Data App="MOD" Name="Provider_QA_Contact" Value="<EMAIL>"/>
			<App_Data App="MOD" Name="Suggested_Price" Value="0.00"/>
			<App_Data App="MOD" Name="Maximum_Viewing_Length" Value="01:00:00"/>
			<App_Data App="MOD" Name="Episode_Name" Value="The Cat Came Back"/>
			<App_Data App="MOD" Name="Episode_ID" Value="2"/>
			<App_Data App="MOD" Name="Billing_ID" Value="0000"/>
			<App_Data App="MOD" Name="Display_Run_Time" Value="00:23"/>
			<App_Data App="MOD" Name="Run_Time" Value="00:23:25"/>
			<App_Data App="MOD" Name="Original_Air_Date" Value="2025-02-08"/>
			<App_Data App="MOD" Name="Year" Value="2025"/>
			<App_Data App="MOD" Name="Licensing_Window_Start" Value="2025-02-09T00:00:00"/>
			<App_Data App="MOD" Name="Licensing_Window_End" Value="2025-03-15T23:59:59"/>
			<App_Data App="MOD" Name="Preview_Period" Value="0"/>
			<App_Data App="MOD" Name="Display_As_New" Value="4"/>
			<App_Data App="MOD" Name="Display_As_Last_Chance" Value="3"/>
			<App_Data App="MOD" Name="Studio" Value="Nat Geo Wild"/>
			<App_Data App="MOD" Name="Closed_Captioning" Value="Y"/>
			<App_Data App="MOD" Name="Category" Value="TV Shows/By Network/Nat Geo Wild/Northern Vets HD"/>
			<App_Data App="MOD" Name="Genre" Value="Documentary"/>
			<App_Data App="MOD" Name="Rating" Value="TV-PG"/>
			<App_Data App="MOD" Name="TMS_Episode_ID" Value="EP055690200002"/>
			<App_Data App="MOD" Name="Ad_Content_ID" Value="10976545-U"/>
		</Metadata>
		<Asset>
			<Metadata>
				<AMS Asset_Class="movie" Asset_ID="NGWC1097654500001203" Asset_Name="NGWC1097654500001203_movie" Creation_Date="2025-02-27" Description="NGWWVNI102_HD_movie" Product="MOD" Provider="NATGEOWILD_HD" Provider_ID="natgeowild.com" Version_Major="7" Version_Minor="0" Verb=""/>
				<App_Data App="MOD" Name="Type" Value="movie"/>
				<App_Data App="MOD" Name="Audio_Type" Value="Dolby 5.1"/>
				<App_Data App="MOD" Name="Languages" Value="en"/>
				<App_Data App="MOD" Name="Bit_Rate" Value="15001"/>
				<App_Data App="MOD" Name="HDContent" Value="Y"/>
				<App_Data App="MOD" Name="trickModesRestricted" Value="FF"/>
				<App_Data App="MOD" Name="Content_CheckSum" Value="237717ca24b2f446b085b3419695859c"/>
				<App_Data App="MOD" Name="Content_FileSize" Value="**********"/>
			</Metadata>
			<Content Value="IncredibleNorthernVe_S01E02_D4_10976545_HD.mpg"/>
		</Asset>
		<Asset>
			<Metadata>
				<AMS Asset_Class="poster" Asset_ID="NGWC1097654500001204" Asset_Name="NGWC1097654500001204_poster" Creation_Date="2025-02-27" Description="NGWC1097654500001204 still asset" Product="MOD" Provider="NATGEOWILD_HD" Provider_ID="natgeowild.com" Version_Major="7" Version_Minor="0" Verb=""/>
				<App_Data App="MOD" Name="Type" Value="poster"/>
				<App_Data App="MOD" Name="Content_CheckSum" Value="ee8a9d39909c9c83b48ec5258d3edbdb"/>
				<App_Data App="MOD" Name="Content_FileSize" Value="17862"/>
			</Metadata>
			<Content Value="IncredibleNorthernVe_S01E02_D4_10976545_HD.bmp"/>
		</Asset>
	</Asset>
</ADI>
	<affiliate_list>
		<affiliate>ATT Deluxe</affiliate>
	</affiliate_list>
	<package_info>
		<content_name>Incredible Northern Vets</content_name>
		<network_name>Nat Geo Wild</network_name>
	</package_info>	
	<metadata_season>
		<metadata name="affiliateSeriesSeasonHDMappingStrings">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets HD</value>
		</metadata>
		<metadata name="affiliateSeriesSeasonSDMappingStrings">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets</value>
		</metadata>
		<metadata name="coxSeriesSeasonCategorySDMapping">
			<value>TV/TV Networks/Networks N-S/Nat Geo Wild/Incredible Northern Vets|TV/TV Shows/G-I/Incredible Northern Vets</value>
		</metadata>
		<metadata name="coxSeriesSeasonCategoryHDMapping">
			<value>TV/TV Networks/Networks N-S/Nat Geo Wild/Incredible Northern Vets HD|TV/TV Shows/G-I/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="mobiSeriesCategoryMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets HD</value>
		</metadata>
		<metadata name="tangerineAlloSeriesCategoryMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets HD</value>
		</metadata>
		<metadata name="verizonSeriesSeasonCategorySDMapping">
			<value>Free &amp; Premium/TV Shows/By Genre/Science &amp; Nature/Nat Geo Wild/Nat Geo Wild/Incredible Northern Vets|Free &amp; Premium/TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild/Incredible Northern Vets|Free &amp; Premium/TV Shows/By Show/H - J/Incredible Northern Vets|TV Shows/By Genre/Science &amp; Nature/Nat Geo Wild/Nat Geo Wild/Incredible Northern Vets|TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild/Incredible Northern Vets|TV Shows/By Show/H - J/Incredible Northern Vets</value>
		</metadata>
		<metadata name="verizonSeriesSeasonCategoryHDMapping">
			<value>Free &amp; Premium/TV Shows/By Genre/Science &amp; Nature/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|Free &amp; Premium/TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|Free &amp; Premium/TV Shows/By Show/H - J/Incredible Northern Vets|TV Shows/By Genre/Science &amp; Nature/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|TV Shows/By Show/H - J/Incredible Northern Vets</value>
		</metadata>
		<metadata name="vubiquityWOWSeriesCategoryHDMapping">
			<value>HD On Demand/NGW HD/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="cablevisionAlticeSeriesHDCategoryMapping">
			<value>NatGeoWild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="frontierSeriesSeasonCategoryHDMapping">
			<value>Free &amp; Premium/TV Shows/By Genre/Entertainment/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|TV Shows/By Genre/Entertainment/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|Free &amp; Premium/TV Shows/By Network/K - N/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|TV Shows/By Network/K - N/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets</value>
		</metadata>
		<metadata name="frontierSeriesSeasonCategorySDMapping">
			<value>Free &amp; Premium/TV Shows/By Genre/Entertainment/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|TV Shows/By Genre/Entertainment/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|Free &amp; Premium/TV Shows/By Network/K - N/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets|TV Shows/By Network/K - N/Nat Geo Wild/Nat Geo Wild HD/Incredible Northern Vets</value>
		</metadata>
		<metadata name="altaFiberSeriesCategoryMapping">
			<value>Free On Demand/By Channel/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="armstrongSeriesSeasonCategoryMapping">
			<value>TV Network/Nat Geo Wild/Incredible Northern Vets|TV Series/Series G - M/Incredible Northern Vets</value>
		</metadata>
		<metadata name="astoundSeriesSeasonCategoryMapping">
			<value>By Network/NatGeoWild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="bluestreamSeriesCategoryMapping">
			<value>TV/Cable Networks J-N/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="breezelineSeriesCategoryMapping">
			<value>Free TV Shows/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="buckeyeSeriesCategoryMapping">
			<value>TV Entertainment/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="tdsTelecomSeriesSeasonCategoryHDMapping">
			<value>TV Shows/I-K/Incredible Northern Vets HD|TV Shows/TV Network/Nat Geo Wild/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="tdsTelecomSeriesSeasonCategorySDMapping">
			<value>TV Shows/I-K/Incredible Northern Vets|TV Shows/TV Network/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="cSpireSeriesSeasonCategoryMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="epbSeriesSeasonCategoryHDMapping">
			<value>TV/Networks/Nat Geo Wild/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="epbSeriesSeasonCategorySDMapping">
			<value>TV/Networks/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="gciSeriesSeasonCategoryMapping">
			<value>TV/Networks/Nat Geo Wild/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="hbcSeriesSeasonCategoryHDMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets HD</value>
		</metadata>
		<metadata name="hbcSeriesSeasonCategorySDMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets</value>
		</metadata>
		<metadata name="hotwireSeriesSeasonCategoryHDMapping">
			<value>Networks on Demand/Nat Geo Wild/Incredible Northern Vets HD|Networks on Demand HD/Nat Geo Wild/Incredible Northern Vets HD</value>
		</metadata>
		<metadata name="hotwireSeriesSeasonCategorySDMapping">
			<value>Networks on Demand/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="mctvSeriesSeasonCategoryMapping">
			<value>TV Networks/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="mediacomSeriesSeasonCategoryMapping">
			<value>TV SHOWS A-Z/H-J/INCREDIBLE NORTHERN VETS|TV/TV NETWORKS/NAT GEO WILD/INCREDIBLE NORTHERN VETS</value>
		</metadata>
		<metadata name="sectvSeriesSeasonCategoryHDMapping">
			<value>SECV On Demand/Free On Demand A-Z/Nat Geo Wild HD</value>
		</metadata>
		<metadata name="sectvSeriesSeasonCategorySDMapping">
			<value>SECV On Demand/Free On Demand A-Z/Nat Geo Wild</value>
		</metadata>
		<metadata name="sectvLehighSeriesSeasonCategoryHDMapping">
			<value>Service Electric VOD/Free OnDemand A-Z/Nat Geo Wild HD/Incredible Northern Vets</value>
		</metadata>
		<metadata name="sectvLehighSeriesSeasonCategorySDMapping">
			<value>Service Electric VOD/Free OnDemand A-Z/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="sectvSpartaSeriesSeasonCategoryHDMapping">
			<value>VODlink Root/Service Electric/FREE/Cable TV Favorites/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="sectvSpartaSeriesSeasonCategorySDMapping">
			<value>VODlink Root/Service Electric/FREE/Cable TV Favorites/Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="tdsBroadbandSeriesSeasonCategoryMapping">
			<value>TV/TV Shows/G-I HD/Incredible Northern Vets|TV/TV Networks/Nat Geo Wild HD/Incredible Northern Vets</value>
		</metadata>
		<metadata name="vermontSeriesSeasonCategoryHDMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets HD</value>
		</metadata>
		<metadata name="vermontSeriesSeasonCategorySDMapping">
			<value>TV Shows/By Network/Nat Geo Wild/Northern Vets</value>
		</metadata>	
	</metadata_season>	
	<metadata_network>
		<metadata name="networkCValue">
			<value>C3</value>
		</metadata>
		<metadata name="networkDValue">
			<value>D4</value>
		</metadata>
		<metadata name="affiliateProductCode">
			<value>MOD</value>
		</metadata>
		<metadata name="affiliateProviderID">
			<value>natgeowild.com</value>
		</metadata>
		<metadata name="affiliateHDPCT">
			<value>NAT_GEO_WILD_HD</value>
		</metadata>
		<metadata name="affiliateHDPCT2">
			<value>NAT_GEO_WILD_C3_HD</value>
		</metadata>
		<metadata name="affiliateSDPCT">
			<value>NAT_GEO_WILD</value>
		</metadata>
		<metadata name="affiliateSDPCT2">
			<value>NAT_GEO_WILD_C3</value>
		</metadata>
		<metadata name="affiliateProviderSD">
			<value>NATGEOWILD</value>
		</metadata>
		<metadata name="affiliateProviderSDC3">
			<value>NATGEOWILD_C3</value>
		</metadata>
		<metadata name="affiliateProviderHD">
			<value>NATGEOWILD_HD</value>
		</metadata>
		<metadata name="affiliateProviderHDC3">
			<value>NATGEOWILD_C3_HD</value>
		</metadata>
		<metadata name="cMSLongFormFolderPath">
			<value>video_fep</value>
		</metadata>
		<metadata name="coxProvider">
			<value>NAT_GEO_WILD_HD</value>
		</metadata>
		<metadata name="coxProductCode">
			<value>FZHD</value>
		</metadata>
		<metadata name="mediacomProvider">
			<value>NAT_GEO_WILD_HD</value>
		</metadata>
		<metadata name="cMSShortFormFolderPath">
			<value>video</value>
		</metadata>
		<metadata name="cMSLongFormFolderPathAkamai">
			<value>video_fep</value>
		</metadata>
		<metadata name="cMSShortFormFolderPathAkamai">
			<value>video</value>
		</metadata>
		<metadata name="cablelabProvider">
			<value>NATGEO WILD</value>
		</metadata>
		<metadata name="cablelabProviderID">
			<value>natgeowild.com</value>
		</metadata>
		<metadata name="cablelabAssetID4LetterPrefix">
			<value>NGWC</value>
		</metadata>
		<metadata name="providerQAContact">
			<value><EMAIL></value>
		</metadata>
		<metadata name="huluNielsenWatermarkingDSRCID">
			<value>48527</value>
		</metadata>
		<metadata name="huluNielsenWatermarkingDSRCName">
			<value>VOD NatGeo Hulu</value>
		</metadata>
		<metadata name="huluNielsenWatermarkingSID0">
			<value>7903</value>
		</metadata>
		<metadata name="mVPDNielsenWatermarkingDSRCID">
			<value>26343</value>
		</metadata>
		<metadata name="mVPDNielsenWatermarkingDSRCName">
			<value>VOD NGW</value>
		</metadata>
		<metadata name="mVPDNielsenWatermarkingSID">
			<value>7900</value>
		</metadata>
		<metadata name="nielsenSVODDistributor">
			<value>NATGEO SVOD</value>
		</metadata>
		<metadata name="upLynkExternalID">
			<value>e6dbd32077b143a4b9cb024b791f2930</value>
		</metadata>
	</metadata_network>
	<metadata_series>
		<metadata name="affiliateSeriesTitleBrief">
			<value>Incredibl</value>
		</metadata>
		<metadata name="affiliateSeriesGenreCodes">
			<value>Documentary</value>
		</metadata>
		<metadata name="comscoreC6">
			<value>WVNI</value>
		</metadata>
		<metadata name="dATGGoPubFolder">
			<value>IncredibleNorthernVets</value>
		</metadata>
	</metadata_series>
	<metadata_package>
		<metadata name="episodeTitle">
			<value>The Cat Came Back</value>
		</metadata>
		<metadata name="productionNumber">
			<value>102</value>
		</metadata>
		<metadata name="tmsId">
			<value>EP055690200002</value>
		</metadata>
		<metadata name="keywords">
			<value>pets, indigenous people, 2020s, medical, rural, Canada, helping others, real life, contemporary, spin-off, uplifting, veterinarian, reality, animal, animals, veterinary care, workplace, wild animal, matter-of-fact, pet</value>
		</metadata>
		<metadata name="tVRating">
			<value>TV-PG</value>
		</metadata>
		<metadata name="trafficCode">
			<value>WVNI95162</value>
		</metadata>
		<metadata name="episodeNumber">
			<value>2</value>
		</metadata>
		<metadata name="affiliateSTBSunrise">
			<value>2025-02-09T00:00:00</value>
		</metadata>
		<metadata name="affiliateSTBSunset">
			<value>2025-03-15T23:59:59</value>
		</metadata>
		<metadata name="affiliateAdContentID">
			<value>10976545-U</value>
		</metadata>
		<metadata name="affiliateManifestID">
			<value>natgeowild.com:NGWC1097654500001101</value>
		</metadata>
		<metadata name="affiliateHDAssetID">
			<value>NGWC1097654500001201</value>
		</metadata>
		<metadata name="affiliateHDTitleID">
			<value>NGWC1097654500001202</value>
		</metadata>
		<metadata name="affiliateHDMovieID">
			<value>NGWC1097654500001203</value>
		</metadata>
		<metadata name="affiliateHDPosterID">
			<value>NGWC1097654500001204</value>
		</metadata>
		<metadata name="affiliateHDCCID">
			<value>NGWC1097654500001205</value>
		</metadata>
		<metadata name="affiliateBaseFilename">
			<value>IncredibleNorthernVe_S01E02_D4_10976545</value>
		</metadata>
		<metadata name="affiliateBaseFoldername">
			<value>10976545_00001</value>
		</metadata>
		<metadata name="affiliateBaseFoldernameSD">
			<value>10976545_SD_00001</value>
		</metadata>
		<metadata name="affiliateDeliveryList">
			<value>ATT Deluxe</value>
		</metadata>
		<metadata name="affiliateSDAssetID">
			<value>NGWC1097654500001101</value>
		</metadata>
		<metadata name="affiliateSDTitleID">
			<value>NGWC1097654500001102</value>
		</metadata>
		<metadata name="affiliateSDMovieID">
			<value>NGWC1097654500001103</value>
		</metadata>
		<metadata name="cablevisionAlticeDeliveryFolder">
			<value>Nat Geo Wild/Incredible Northern Vets</value>
		</metadata>
		<metadata name="affiliateSDPosterID">
			<value>NGWC1097654500001104</value>
		</metadata>
		<metadata name="affiliateSDCCID">
			<value>NGWC1097654500001105</value>
		</metadata>
		<metadata name="mCTVDeliveryFolder">
			<value>/TV Networks/Nat Geo Wild</value>
		</metadata>
	</metadata_package>
</master>