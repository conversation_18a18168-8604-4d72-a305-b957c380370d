import os
from collections import Ordered<PERSON>ict
from copy import deepcopy
from datetime import datetime

import xmltodict
from generated_report import DeepNestedDictComparator
from mvpd_affiliate_prep_5_1_1 import main
from utils.tasks.data_processing import convert_from_dict

from src.task_config import task_config_assembler
from src.tasks import TaskAffiliateAssembler, Tasks


class TestIntegrationCompare:

    def test_run_on_permise_file(self, get_files_to_test, root_path, output_path):
        out_base_path = root_path
        resolutions = "HD"
        main_path = get_files_to_test[0]
        main_files = get_files_to_test[1]
        date_format = datetime.now().strftime("%y_%m_%d")

        for test_file in main_files:
            file_path = f"{main_path}/{test_file}.xml"

            test_file_no_ext = os.path.splitext(test_file)[0]
            output_name = f"{out_base_path}{output_path}/on_permises/{test_file_no_ext}_{date_format}.xml"

            file = file_path
            output = output_name

            main(file, output, resolutions)

    def test_run_express_lane(
        self, global_variables, get_files_to_test, root_path, output_path
    ) -> None:
        """
        Initializes all the services and configurations and runs the flow
        according to the configuration obtained.
        """

        main_path = get_files_to_test[0]
        main_files = get_files_to_test[1]
        date_format = datetime.now().strftime("%y_%m_%d")

        for test_file in main_files:

            file_path = f"{main_path}/{test_file}.xml"

            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            prev = content

            _raw_data = xmltodict.parse(prev, dict_constructor=OrderedDict)

            event_settings_rules = global_variables.get("event_settings_rules")

            affiliates = (
                _raw_data.get("master", {})
                .get("affiliate_list", {})
                .get("affiliate", {})
            )

            # Ensure it's always a list
            if not isinstance(affiliates, list):
                affiliates = [affiliates]

            raw_data = deepcopy(_raw_data)

            filtered_affiliates = {}
            for k in affiliates:
                if k in event_settings_rules:
                    filtered_affiliates[k] = event_settings_rules[k]

            global_variables["raw_data"] = raw_data

            task_config_assambler = task_config_assembler(
                global_variables=global_variables,
                filtered_affiliates=filtered_affiliates,
            )

            global_variables["task_config"] = task_config_assambler

            affiliate_assambler = TaskAffiliateAssembler(
                global_variables=global_variables
            ).assemble()

            global_variables["affiliate_assambler"] = affiliate_assambler

            tasks = Tasks(global_variables)

            # STEP 1: Filter by Skip content
            tasks.skip_content()

            # STEP 2: Apply Category Rules
            tasks.apply_category_rules()

            # Step 3: Removed Attributes Rules
            tasks.removed_attributes()

            # Step 4: UPDATE ATTRIBUTES LOGIC
            tasks.update_attributes()

            # Step 5: REPLACE ATTRIBUTES LOGIC
            tasks.replace_attributes()

            # Step 6: Limit length
            tasks.limit_length()

            prev = convert_from_dict(affiliate_assambler, "xml")

            out_base_path = root_path
            test_file_no_ext = os.path.splitext(test_file)[0]

            output_name = f"{out_base_path}{output_path}/express_lane/{test_file_no_ext}_{date_format}.xml"

            with open(output_name, "w", encoding="utf-8") as f:
                f.write(prev)

    def test_compare_outputs_CrashCourseCuisinewi(
        self, get_output_files_to_test, root_path, output_report_path
    ) -> None:
        out_base_path = root_path
        output_name = f"{out_base_path}{output_report_path}"

        all_files_express_lane = get_output_files_to_test.get("all_files_express_lane")
        on_permises_target_dir = get_output_files_to_test.get("on_permises_target_dir")

        _express_lane = all_files_express_lane[1]
        _on_perm = on_permises_target_dir[1]

        with open(_express_lane, "r", encoding="utf-8") as f:
            _express_lane_read = f.read()

        _express_lane_content = xmltodict.parse(
            _express_lane_read, dict_constructor=OrderedDict
        )

        with open(_on_perm, "r", encoding="utf-8") as f:
            _on_perm_read = f.read()

        _on_perm_content = xmltodict.parse(_on_perm_read, dict_constructor=OrderedDict)

        if _express_lane and _on_perm_content:
            comparator = DeepNestedDictComparator(
                _express_lane_content, _on_perm_content, output_name
            )
            comparator.save_to_file(_express_lane)

    def test_compare_outputs_IncredibleNorthernVe(
        self, get_output_files_to_test, root_path, output_report_path
    ) -> None:
        out_base_path = root_path
        output_name = f"{out_base_path}{output_report_path}"

        all_files_express_lane = get_output_files_to_test.get("all_files_express_lane")
        on_permises_target_dir = get_output_files_to_test.get("on_permises_target_dir")

        _express_lane = all_files_express_lane[0]
        _on_perm = on_permises_target_dir[0]

        with open(_express_lane, "r", encoding="utf-8") as f:
            _express_lane_read = f.read()

        _express_lane_content = xmltodict.parse(
            _express_lane_read, dict_constructor=OrderedDict
        )

        with open(_on_perm, "r", encoding="utf-8") as f:
            _on_perm_read = f.read()

        _on_perm_content = xmltodict.parse(_on_perm_read, dict_constructor=OrderedDict)

        if _express_lane and _on_perm_content:
            comparator = DeepNestedDictComparator(
                _express_lane_content, _on_perm_content, output_name
            )
            comparator.save_to_file(_express_lane)
