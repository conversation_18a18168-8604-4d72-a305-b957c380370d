from deepdiff import DeepDiff


class DeepNestedDictComparator:
    def __init__(self, dict1, dict2, report_path="report_file.txt"):
        self.dict1 = self.normalize_dict(dict1)
        self.dict2 = self.normalize_dict(dict2)
        self.report_path = report_path
        ""

    def normalize_dict(self, dictionary):
        """
        Recursively sort the dictionary to ignore key order and list order while preserving structure.
        """
        if isinstance(dictionary, dict):
            return {k: self.normalize_dict(v) for k, v in sorted(dictionary.items())}
        elif isinstance(dictionary, list):
            # Sort the list based on each item to avoid order comparison issues.
            return [self.normalize_dict(item) for item in sorted(dictionary, key=str)]
        else:
            return dictionary

    def compare(self):
        """
        Perform a deep comparison of two nested dictionaries.

        Returns:
            dict: A dictionary with detailed differences:
                - 'added': Keys present in dict2 but not in dict1
                - 'removed': Keys present in dict1 but not in dict2
                - 'modified': Keys present in both but with different values
                - 'type_changes': Keys with the same path but different types
        """

        differences = DeepDiff(self.dict1, self.dict2, verbose_level=2)

        result = {
            "added": differences.get("dictionary_item_added", []),
            "removed": differences.get("dictionary_item_removed", []),
            "modified": [],
            "type_changes": differences.get("type_changes", []),
        }

        # Enhance the 'modified' section to include old and new values
        values_changed = differences.get("values_changed", {})
        for path, change in values_changed.items():

            mvpd = differences.t1.get("clients").get("rules").get("mvpd")

            result["modified"].append(
                {
                    "mvpd": mvpd,
                    "path": path,
                    "old_value": change.get("old_value"),
                    "new_value": change.get("new_value"),
                }
            )

        return result

    def save_to_file(self, relativa_path=""):
        """
        Save differences into a .txt file in a structured way.
        """
        differences = self.compare()
        _affiliates = set()  # Use set to collect unique clients
        client_data = {}  # Dictionary to group data by client

        with open(self.report_path, "a") as file:
            for change_type, changes in differences.items():
                for change in changes:
                    if change_type == "modified" and isinstance(change, dict):
                        mvpd = change.get("mvpd")
                        mvpd_list = mvpd if isinstance(mvpd, list) else [mvpd]
                        for i in mvpd_list:
                            client = i.get("@ascp_client") or i.get("@workgroup_name")
                            if client:
                                _affiliates.add(client)

            file.write(
                f"\n=== Comparison for: {relativa_path} ===\n Affiliates: {', '.join(sorted(_affiliates))}\n"
            )

            for change_type, changes in differences.items():
                file.write(f"\n=== {change_type.upper()} ===\n")
                if not changes:
                    file.write("None\n")
                else:
                    for change in changes:
                        if change_type == "modified" and isinstance(change, dict):
                            mvpd = change.get("mvpd")
                            mvpd_list = mvpd if isinstance(mvpd, list) else [mvpd]
                            for i in mvpd_list:
                                client = i.get("@ascp_client") or i.get(
                                    "@workgroup_name"
                                )
                                if client:
                                    _affiliates.add(client)
                                    if client not in client_data:
                                        client_data[client] = []
                                    client_data[client].append(
                                        {
                                            "relativa_path": relativa_path,
                                            "path": change["path"],
                                            "old_value": f"On permise Value: {change['old_value']}",
                                            "new_value": f"Reach Value: {change['new_value']}",
                                        }
                                    )
                                file.write(
                                    f"Affiliate: {client} | {change['path']} | On permise Value: {change['old_value']} | Reach Value: {change['new_value']}\n"
                                )
                        else:
                            file.write(f"{change}\n")
            file.write("\n" + "=" * 200 + "\n")

        return client_data
