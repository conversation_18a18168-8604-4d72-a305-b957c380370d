import argparse
import copy
import sys
from xml.dom.minidom import parseString

# from venv import logger
from xml.etree import ElementTree as ET
from xml.etree.ElementTree import Comment, Element, SubElement, tostring
from xml.sax.saxutils import escape


def prettify(x):
    try:
        reparsed = parseString(ET.tostring(x))
    except:
        # print(ET.tostring(x))
        print("ERROR trying to prettify")
    newx = "\n".join(
        [
            line
            for line in reparsed.toprettyxml(indent=" " * 2).split("\n")
            if line.strip()
        ]
    )
    return newx


def make_tar_element():
    base = """<changes>
    <format>TAR</format>
</changes>"""
    tar_xml = ET.fromstring(base)
    return tar_xml


def affiliate_ascp_adi(adi):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Product
    # Provider

    all_ams = adi_instance.findall(".//AMS")
    for this_ams in all_ams:
        this_ams.set("Product", "FZHD")
        this_ams.set("Provider", "FREEFORM")

    return adi_instance


def affiliate_ascp_conn_info():
    base = """<ascp>
            <host>*************</host>
            <port>33001</port>
            <username></username>
            <pwd_or_key></pwd_or_key>
            <authentication>Password</authentication>
            <target_rate>100000</target_rate>
            <target>/_UPLOADS/</target>
         </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_faspex_adi(adi):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Product
    # Provider
    # Categories

    all_ams = adi_instance.findall(".//AMS")
    for this_ams in all_ams:
        this_ams.set("Product", "FZHD")
        this_ams.set("Provider", "FREEFORM")

    # replace categories
    # print(prettify(adi_instance))
    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    cat1 = "TV Shows/Freeform/grown-ish HD/Season 5"
    cat2 = "TV Shows/grown-ish/Season 5"
    cat_elem1 = ET.fromstring(
        '<App_Data App="MOD" Name="Category" Value="TV Shows/Freeform/grown-ish HD/Season 5"/>'
    )
    cat_elem2 = ET.fromstring(
        '<App_Data App="MOD" Name="Category" Value="TV Shows/grown-ish/Season 5"/>'
    )
    cat_parent.append(cat_elem1)
    cat_parent.append(cat_elem2)

    return adi_instance


def affiliate_nochange_adi(adi):
    """
    Makes a copy of adi XML. Then returns it (no changes)
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)
    return adi_instance


def affiliate_dev_conn_info(affiliate_name):
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/_UPLOADS/{}</target>
             </ascp>""".format(
        affiliate_name
    )
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_default_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original

    return adi_instance


def affiliate_cablevision_altice_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))

    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='cablevisionAlticeMovieSDCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='cablevisionAlticeMovieHDCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='cablevisionAlticeSeriesSDCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='cablevisionAlticeSeriesHDCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    # add time to Air Date (because they wanted it)
    airdate_elem = adi_instance.find(".//App_Data[@Name='Original_Air_Date']")
    try:
        airdate_value = airdate_elem.attrib["Value"]
        airdate_value = airdate_value + "T00:00:00"
        airdate_elem.attrib["Value"] = airdate_value
    except:
        pass

    return adi_instance


def affiliate_cablevision_altice_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100M</target_rate>
                <target>/DATG_VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_alta_fiber_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='altaFiberMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='altaFiberMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='altaFiberSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='altaFiberSeriesCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_alta_fiber_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/storage/aspera/drop</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_armstrong_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='armstrongMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='armstrongMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='armstrongSeriesSeasonCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='armstrongSeriesSeasonCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    # they want Title length limited to 32 characters (even though Cablelab spec is 128 characters)
    all_title = adi_instance.findall(".//App_Data[@Name='Title']")

    for this_title in all_title:
        this_title_value = this_title.get("Value")
        if len(this_title_value) > 32:
            this_title.set("Value", this_title_value[:32])

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_armstrong_conn_info():
    base = """<ascp>
                <host>************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_astound_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='astoundMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='astoundMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='astoundSeriesSeasonCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='astoundSeriesSeasonCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_astound_conn_info():
    base = """<ascp>
                <host>***********</host>
                <port>22</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_att_conn_info():
    base = """<ascp>
                <host>aspera-live.deluxeone.com</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>300000</target_rate>
                <target>/ATT/STB</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_blueridge_conn_info():
    base = """<ascp>
                <host>************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/DATG_VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_bluestream_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='bluestreamMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='bluestreamMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='bluestreamSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='bluestreamSeriesCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_blue_stream_fiber_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_breezeline_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='breezelineMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='breezelineMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='breezelineSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='breezelineSeriesCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_breezeline_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/vod/abcipvod</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_buckeye_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original

    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='buckeyeMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='buckeyeMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='buckeyeSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='buckeyeSeriesCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    # Buckeye wants a lot of fields removed:
    # remove Ad_Content_ID field
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Ad_Content_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Ad_Content_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Season_Number
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Season_Number']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Season_Number']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Series_Name
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Series_Name']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Series_Name']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Episode_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Episode_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Episode_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Series_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Series_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Series_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove existing default categories
    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_buckeye_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_cspire_conn_info():
    base = """<ascp>
                <host>cspire-vod-prod.cspire.com</host>
                <port>22</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>40000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_cox_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace Provider and Product Code

    new_provider = metadata_network.find("./metadata[@name='coxProvider']/value").text
    new_product_code = metadata_network.find(
        "./metadata[@name='coxProductCode']/value"
    ).text
    all_ams = adi_instance.findall(".//AMS")
    for this_ams in all_ams:
        this_ams.set("Provider", new_provider)
        this_ams.set("Product", new_product_code)

    # replace PCT
    new_pct = metadata_network.find("./metadata[@name='affiliateHDPCT2']/value").text
    pct_element = adi_instance.find(
        "./Metadata/App_Data[@Name='Provider_Content_Tier']"
    )
    pct_element.set("Value", new_pct)

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='coxMovieCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='coxMovieCategoryHDMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='coxSeriesSeasonCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='coxSeriesSeasonCategoryHDMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")
    # new_cat_hd = new_cat_hd.split('|')
    # new_cat_list = new_cat_sd + new_cat_hd

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_cox_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key>/opt/aspera/var/config/orchestrator/certs/cox_asp-disney</pwd_or_key>
                <authentication>Public Key</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_csi_digital_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username>TBD</username>
                <pwd_or_key>TBD</pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/home/<USER>/target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_frontier_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_gci_conn_info():
    base = """<ascp>
                <host>***********</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>50000</target_rate>
                <target>/ingested</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_hiawatha_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>22</port>
                <username></username>
                <pwd_or_key>TBD</pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/mnt/vod_backup/fx_ngc</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_logic_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>50000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_mediacom_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True
    else:
        # mix of info - raise exception
        raise Exception("mixing of data between movie and series")

    # replace Provider
    new_provider = metadata_network.find(
        "./metadata[@name='mediacomProvider']/value"
    ).text
    all_ams = adi_instance.findall(".//AMS")
    for this_ams in all_ams:
        this_ams.set("Provider", new_provider)

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='mediacomMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='mediacomMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='mediacomSeriesSeasonCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='mediacomSeriesSeasonCategoryMapping']/value"
            ).text
    new_cat_list = new_cat.split("|")
    # new_cat_sd = new_cat_sd.split('|')
    # new_cat_hd = new_cat_hd.split('|')
    # new_cat_list = new_cat_sd + new_cat_hd

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_mediacom_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>50000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_mctv_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Remove any attributes not in Cablelab spec including:
    # Series_Name
    # Season_Number
    # TMS_Series_ID
    # TMS_Episode_ID
    # Ad_Content_ID
    # Original_Air_Date

    elem_remove_list = [
        "Series_Name",
        "Season_Number",
        "TMS_Series_ID",
        "TMS_Episode_ID",
        "Ad_Content_ID",
        "Original_Air_Date",
    ]

    for e in elem_remove_list:
        elem_parent = adi_instance.find(".//App_Data[@Name='{}']/..".format(e))
        all_elem = adi_instance.findall(".//App_Data[@Name='{}']".format(e))
        for this_elem in all_elem:
            elem_parent.remove(this_elem)

    # replace categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='mctvMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='mctvMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='mctvSeriesSeasonCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='mctvSeriesSeasonCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_mctv_conn_info():
    base = """<ascp>
                <host>************</host>
                <port>22</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/fxngc</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_ozarks_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_vermont_telephone_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/DATG_VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_verizon_stb_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True
    else:
        # mix of info - raise exception
        raise Exception("mixing of data between movie and series")

    # replace Provider and Product Code
    # new_provider = metadata_network.find("./metadata[@name='coxProvider']/value").text
    # new_product_code = metadata_network.find("./metadata[@name='coxProductCode']/value").text
    # all_ams = adi_instance.findall('.//AMS')
    # for this_ams in all_ams:
    #     this_ams.set('Provider', new_provider)
    #     this_ams.set('Product', new_product_code)
    #
    # # replace PCT
    # new_pct = metadata_network.find("./metadata[@name='coxPCT']/value").text
    # pct_element = adi_instance.find("./Metadata/App_Data[@Name='Provider_Content_Tier']")
    # pct_element.set('Value', new_pct)

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='verizonMovieCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='verizonMovieCategoryHDMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='verizonSeriesSeasonCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='verizonSeriesSeasonCategoryHDMapping']/value"
            ).text
    new_cat_list = new_cat.split("|")
    # new_cat_sd = new_cat_sd.split('|')
    # new_cat_hd = new_cat_hd.split('|')
    # new_cat_list = new_cat_sd + new_cat_hd

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_verizon_lab_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/DATG_VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_verizon_stb_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_vubiquity_wow_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='vubiquityWOWMovieCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='vubiquityWOWMovieCategoryHDMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='vubiquityWOWSeriesCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='vubiquityWOWSeriesCategoryHDMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    # WOW wants a lot of fields removed:
    # remove Ad_Content_ID field
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Ad_Content_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Ad_Content_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Season_Number
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Season_Number']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Season_Number']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Series_Name
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Series_Name']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Series_Name']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Episode_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Episode_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Episode_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Series_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Series_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Series_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_vubiquity_wow_conn_info():
    # TODO: missing login info
    base = """<ascp>
                <host>aspera.vubiquity.com</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/DATG_VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_mobitv_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='mobiMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='mobiMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='mobiMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='mobiSeriesCategoryMapping']/value"
            ).text
    new_cat_list = new_cat.split("|")
    # new_cat_sd = new_cat_sd.split('|')
    # new_cat_hd = new_cat_hd.split('|')
    # new_cat_list = new_cat_hd

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_mobitv_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_sectv_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='sectvMovieCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='sectvMovieCategoryHDMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='sectvSeriesSeasonCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='sectvSeriesSeasonCategoryHDMapping']/value"
            ).text
    new_cat_list = new_cat.split("|")
    # new_cat_sd = new_cat_sd.split('|')
    # new_cat_hd = new_cat_hd.split('|')
    # new_cat_list = new_cat_hd

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_sectv_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/DATG_VOD</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_tangerine_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # # replace Provider and Product Code
    # new_provider = metadata_network.find("./metadata[@name='coxProvider']/value").text
    # new_product_code = metadata_network.find("./metadata[@name='coxProductCode']/value").text
    # all_ams = adi_instance.findall('.//AMS')
    # for this_ams in all_ams:
    #     this_ams.set('Provider', new_provider)
    #     this_ams.set('Product', new_product_code)
    #
    # # replace PCT
    # new_pct = metadata_network.find("./metadata[@name='coxPCT']/value").text
    # pct_element = adi_instance.find("./Metadata/App_Data[@Name='Provider_Content_Tier']")
    # pct_element.set('Value', new_pct)

    # replace categories
    # print(prettify(adi_instance))
    # TODO: try statements!

    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='tangerineAlloMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='tangerineAlloMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='tangerineAlloSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='tangerineAlloSeriesCategoryMapping']/value"
            ).text
    new_cat_list = new_cat.split("|")
    # new_cat_sd = new_cat_sd.split('|')
    # new_cat_hd = new_cat_hd.split('|')
    # new_cat_list = new_cat_sd + new_cat_hd

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_tangerine_conn_info():
    base = """<ascp>
                <host>************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Password</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_tds_broadband_conn_info():
    base = """<ascp>
                <host>*************</host>
                <port>22</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Public Key</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_tds_telecom_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param metadata_season:
    :param metadata_network:
    :param metadata_movie:
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='tdsTelecomMovieCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='tdsTelecomMovieCategoryHDMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='tdsTelecomSeriesSeasonCategorySDMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='tdsTelecomSeriesSeasonCategoryHDMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_tds_telecom_conn_info():
    base = """<ascp>
                <host>************</host>
                <port>22</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Public Key</authentication>
                <target_rate>100000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_wow_detroit_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='wowDetroitMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='wowDetroitMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='wowDetroitSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='wowDetroitSeriesCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    # WOW wants a lot of fields removed:
    # remove Ad_Content_ID field
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Ad_Content_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Ad_Content_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Season_Number
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Season_Number']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Season_Number']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Series_Name
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Series_Name']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Series_Name']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Episode_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Episode_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Episode_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Series_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Series_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Series_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_wow_detroit_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Public Key</authentication>
                <target_rate>120000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def affiliate_wow_westpoint_adi(
    adi, metadata_movie, metadata_network, metadata_season, resolution
):
    """
    Makes a copy of adi XML. Then changes it according to pre-defined changes needed
    :param adi:
    :return:
    """
    adi_instance = copy.deepcopy(adi)  # copy to prevent changing the original
    # Changes needed for this affiliate:
    # Provider
    # Product Code
    # PCT
    # Categories
    if metadata_movie is None and metadata_season is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_season is None:
        is_movie = True

    # replace categories
    # print(prettify(adi_instance))
    if is_movie:
        if resolution == "SD":
            new_cat = metadata_movie.find(
                "./metadata[@name='wowWestpointMovieCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_movie.find(
                "./metadata[@name='wowWestpointMovieCategoryMapping']/value"
            ).text
    else:
        if resolution == "SD":
            new_cat = metadata_season.find(
                "./metadata[@name='wowWestpointSeriesCategoryMapping']/value"
            ).text
        else:
            new_cat = metadata_season.find(
                "./metadata[@name='wowWestpointSeriesCategoryMapping']/value"
            ).text

    new_cat_list = new_cat.split("|")

    # WOW wants a lot of fields removed:
    # remove Ad_Content_ID field
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Ad_Content_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Ad_Content_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Season_Number
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Season_Number']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Season_Number']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove Series_Name
    remove_me_parent = adi_instance.find(".//App_Data[@Name='Series_Name']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='Series_Name']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Episode_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Episode_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Episode_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    # remove TMS_Series_ID
    remove_me_parent = adi_instance.find(".//App_Data[@Name='TMS_Series_ID']/..")
    remove_me = adi_instance.findall(".//App_Data[@Name='TMS_Series_ID']")
    for this_elem in remove_me:
        remove_me_parent.remove(this_elem)

    cat_parent = adi_instance.find(".//App_Data[@Name='Category']/..")
    all_cat = adi_instance.findall(".//App_Data[@Name='Category']")
    for this_cat in all_cat:
        cat_parent.remove(this_cat)

    for this_new_cat in new_cat_list:
        new_cat_elem = ET.fromstring(
            '<App_Data App="MOD" Name="Category" Value="{}"/>'.format(
                escape(this_new_cat)
            )
        )
        cat_parent.append(new_cat_elem)

    return adi_instance


def affiliate_wow_west_point_conn_info():
    base = """<ascp>
                <host>**************</host>
                <port>33001</port>
                <username></username>
                <pwd_or_key></pwd_or_key>
                <authentication>Public Key</authentication>
                <target_rate>120000</target_rate>
                <target>/</target>
             </ascp>"""
    conn_xml = ET.fromstring(base)
    return conn_xml


def main(source_xml_path, output_xml_path, resolution):
    # parse out the ADI from starting file
    try:
        source_tree = ET.parse(source_xml_path)
        source_root = source_tree.getroot()
    except ET.ParseError as e:
        print("Problem parsing the Source XML. Error: {}".format(e))
        sys.exit(3)

    affiliate_list = []
    affiliate_list_elem = source_root.find("./affiliate_list")
    for this_aff in affiliate_list_elem:
        affiliate_list.append(this_aff.text)

    adi = source_root.find("./ADI")
    metadata_season = source_root.find("./metadata_season")  # only present for series
    metadata_network = source_root.find("./metadata_network")
    metadata_series = source_root.find("./metadata_series")  # only present for series
    metadata_movie = source_root.find("./metadata_movie")  # only present for movies

    network_name_elem = source_root.find("./package_info/network_name")
    try:
        network_name = network_name_elem.text
    except:
        network_name = ""
        print("Missing package_info/network_name")

    if metadata_movie is None and metadata_series is not None:
        is_movie = False
    elif metadata_movie is not None and metadata_series is None:
        is_movie = True

    # create new output XML
    output_root = Element("clients")
    rules_elem = SubElement(output_root, "rules")
    tar_elem = make_tar_element()

    # look for and work with each affiliate
    if "ASCP_TEST" in affiliate_list:
        ascp_adi = affiliate_ascp_adi(adi)
        ascp_conn = affiliate_ascp_conn_info()
        ascp_elem = SubElement(rules_elem, "mvpd")
        ascp_elem.set("ascp_client", "Affiliate_ASCP_TEST")  # for P2P
        ascp_elem.append(ascp_adi)
        ascp_elem.append(ascp_conn)

    if "FASPEX_TEST" in affiliate_list:
        faspex_adi = affiliate_faspex_adi(adi)
        faspex_elem = SubElement(rules_elem, "mvpd")
        faspex_elem.set(
            "workgroup_name", "MASS_FASPEX_TEST_WG"
        )  # workgroup_name for FASPEX
        faspex_elem.append(faspex_adi)

    if "Alta Fiber" in affiliate_list:
        # Alta Fiber does not want FXX or FXM content (per Eric Packer Dec 13, 2024)
        if network_name == "FXX" or network_name == "FXM":
            pass
        else:
            try:
                alta_fiber_adi = affiliate_alta_fiber_adi(
                    adi, metadata_movie, metadata_network, metadata_season, resolution
                )
                alta_fiber_conn = affiliate_alta_fiber_conn_info()
                alta_fiber_elem = SubElement(rules_elem, "mvpd")
                alta_fiber_elem.set("ascp_client", "Alta_Fiber_Workflow")
                alta_fiber_elem.append(alta_fiber_adi)
                alta_fiber_elem.append(alta_fiber_conn)
            except:
                print("error constructing Alta Fiber")

    if "Antietam" in affiliate_list:
        try:
            # Antietam is FASPEX
            antietam_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            # antietam_conn = affiliate_alta_fiber_conn_info() # connection not needed for FASPEX accounts
            antietam_elem = SubElement(rules_elem, "mvpd")
            antietam_elem.set(
                "workgroup_name", "Antietam_Cable_WG"
            )  # "workgroup_name" for FASPEX
            antietam_elem.append(antietam_adi)
            # antietam_elem.append(antietam_conn)# connection not needed for FASPEX accounts
        except:
            print("error constructing Antietam")

    if "Armstrong" in affiliate_list:
        # Armstrong does not want FXM content (per Eric Packer Jan 29, 2025)
        if network_name == "FXM":
            pass
        try:
            armstrong_adi = affiliate_armstrong_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            armstrong_conn = affiliate_armstrong_conn_info()
            armstrong_elem = SubElement(rules_elem, "mvpd")
            armstrong_elem.set("ascp_client", "Armstrong_Workflow")  #
            armstrong_elem.append(armstrong_adi)
            armstrong_elem.append(armstrong_conn)
        except:
            print("error constructing Armstrong")

    if "Astound (RCN)" in affiliate_list:
        try:
            astound_adi = affiliate_astound_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            astound_conn = affiliate_astound_conn_info()
            astound_elem = SubElement(rules_elem, "mvpd")
            astound_elem.set("ascp_client", "Astound_RCN_Workflow")  #
            astound_elem.append(astound_adi)
            astound_elem.append(astound_conn)
            astound_elem.append(tar_elem)
        except:
            print("error constructing Astound (RCN)")

    if "ATT Deluxe" in affiliate_list:
        try:
            att_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            att_conn = affiliate_att_conn_info()
            att_elem = SubElement(rules_elem, "mvpd")
            att_elem.set("ascp_client", "ATT_Deluxe_Workflow")  #
            att_elem.append(att_adi)
            att_elem.append(att_conn)
        except:
            print("error constructing ATT Deluxe")

    if "TDS Telecom" in affiliate_list:
        try:
            tds_telecom_adi = affiliate_tds_telecom_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            tds_telecom_conn = affiliate_tds_telecom_conn_info()
            tds_telecom_elem = SubElement(rules_elem, "mvpd")
            tds_telecom_elem.set("ascp_client", "TDS_Telecom_Workflow")  #
            tds_telecom_elem.append(tds_telecom_adi)
            tds_telecom_elem.append(tds_telecom_conn)
        except:
            print("error constructing TDS Telecom")

    if "Blue Ridge" in affiliate_list:
        try:
            blueridge_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            blueridge_conn = affiliate_blueridge_conn_info()
            blueridge_elem = SubElement(rules_elem, "mvpd")
            blueridge_elem.set("ascp_client", "Blue_Ridge_Workflow")  #
            blueridge_elem.append(blueridge_adi)
            blueridge_elem.append(blueridge_conn)
        except:
            print("error constructing Blue Ridge")

    if "BlueStream Fiber" in affiliate_list:
        try:
            bluestream_fiber_adi = affiliate_bluestream_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            bluestream_fiber_conn = affiliate_blue_stream_fiber_conn_info()
            bluestream_fiber_elem = SubElement(rules_elem, "mvpd")
            bluestream_fiber_elem.set("ascp_client", "BlueStream_Fiber_Workflow")  #
            bluestream_fiber_elem.append(bluestream_fiber_adi)
            bluestream_fiber_elem.append(bluestream_fiber_conn)
        except:
            print("error constructing BlueStream Fiber")

    if "Breezeline" in affiliate_list:
        try:
            breezeline_adi = affiliate_breezeline_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            breezeline_conn = affiliate_breezeline_conn_info()
            breezeline_elem = SubElement(rules_elem, "mvpd")
            breezeline_elem.set("ascp_client", "Breezeline_Workflow")  #
            breezeline_elem.append(breezeline_adi)
            breezeline_elem.append(breezeline_conn)
        except:
            print("error constructing Breezeline")

    if "Buckeye" in affiliate_list:
        try:
            buckeye_adi = affiliate_buckeye_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            buckeye_conn = affiliate_buckeye_conn_info()
            buckeye_elem = SubElement(rules_elem, "mvpd")
            buckeye_elem.set("ascp_client", "Buckeye_Workflow")  #
            buckeye_elem.append(buckeye_adi)
            buckeye_elem.append(buckeye_conn)
        except:
            print("error constructing Buckeye")

    if "Cablevision (Altice)" in affiliate_list:
        # Note: originally Altice said to put in a special folder. Now it appears otherwise
        # /master/metadata_package/metadata[@name='cablevisionAlticeDeliveryFolder']
        # affiliate_list = [] # /master/metadata_package/metadata/@name
        # cablevision_altice_delivery_folder_elem = source_root.find("./metadata_package/metadata[@name='cablevisionAlticeDeliveryFolder']/value")
        # try:
        #     cablevision_altice_delivery_folder = '/' + cablevision_altice_delivery_folder_elem.text
        # except:
        #     cablevision_altice_delivery_folder = ''

        # for this_aff in affiliate_list_elem:
        #     affiliate_list.append(this_aff.text)
        try:
            altice_adi = affiliate_cablevision_altice_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            altice_conn = affiliate_cablevision_altice_conn_info()
            altice_elem = SubElement(rules_elem, "mvpd")
            altice_elem.set("ascp_client", "Cablevision_Altice_Workflow")
            altice_elem.append(altice_adi)
            altice_elem.append(altice_conn)
            altice_elem.append(tar_elem)
        except:
            print("error constructing Cablevision Altice")

    if "C-Spire" in affiliate_list:
        try:
            cspire_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            cspire_conn = affiliate_cspire_conn_info()
            cspire_elem = SubElement(rules_elem, "mvpd")
            cspire_elem.set("ascp_client", "C-Spire_Workflow")
            cspire_elem.append(cspire_adi)
            cspire_elem.append(cspire_conn)
        except:
            print("error constructing C-Spire")

    if "Cinergy Metronet" in affiliate_list:
        try:
            # Cinergy is FASPEX
            cinergy_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            cinergy_elem = SubElement(rules_elem, "mvpd")
            cinergy_elem.set("workgroup_name", "Cinergy_Metronet_Workflow")
            cinergy_elem.append(cinergy_adi)
        except:
            print("error constructing Cinergy Metronet")

    if "ClaroTV" in affiliate_list:
        if network_name == "FXX":
            pass
        else:
            try:
                # ClaroTV is FASPEX
                claro_adi = affiliate_default_adi(
                    adi, metadata_movie, metadata_network, metadata_season, resolution
                )
                claro_elem = SubElement(rules_elem, "mvpd")
                claro_elem.set("workgroup_name", "ClaroTV_WG")
                claro_elem.append(claro_adi)
            except:
                print("error constructing ClaroTV")

    if "Cox" in affiliate_list:
        try:
            cox_adi = affiliate_cox_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            cox_conn = affiliate_cox_conn_info()
            cox_elem = SubElement(rules_elem, "mvpd")
            cox_elem.set("ascp_client", "COX_Workflow")
            cox_elem.append(cox_adi)
            cox_elem.append(cox_conn)
            cox_elem.append(tar_elem)
        except:
            print("error constructing Cox")

    if "CSI Digital" in affiliate_list:
        try:
            csi_digital_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            csi_digital_conn = affiliate_csi_digital_conn_info()
            csi_digital_elem = SubElement(rules_elem, "mvpd")
            csi_digital_elem.set("ascp_client", "CSI_Digital_Workflow")
            csi_digital_elem.append(csi_digital_adi)
            csi_digital_elem.append(csi_digital_conn)
        except:
            print("error constructing CSI Digital")

    if "EPB" in affiliate_list:
        # EPB is FASPEX
        try:
            elec_power_board_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            # elec_power_board_conn = affiliate_elec_power_board_conn_info()
            elec_power_board_elem = SubElement(rules_elem, "mvpd")
            elec_power_board_elem.set("workgroup_name", "Electric_Power_Board_WG")
            elec_power_board_elem.append(elec_power_board_adi)
            # elec_power_board_elem.append(elec_power_board_conn)
        except:
            print("error constructing Electric Power Board (EPB)")

    if "Frontier" in affiliate_list:
        try:
            frontier_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            frontier_conn = affiliate_frontier_conn_info()
            frontier_elem = SubElement(rules_elem, "mvpd")
            frontier_elem.set("ascp_client", "Frontier_Workflow")
            frontier_elem.append(frontier_adi)
            frontier_elem.append(frontier_conn)
        except:
            print("error constructing Frontier")

    if "GCI" in affiliate_list:
        try:
            gci_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            gci_conn = affiliate_gci_conn_info()
            gci_elem = SubElement(rules_elem, "mvpd")
            gci_elem.set("ascp_client", "GCI_Workflow")
            gci_elem.append(gci_adi)
            gci_elem.append(gci_conn)
        except:
            print("error constructing GCI")

    if "HBC" in affiliate_list:
        try:
            hiawatha_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            hiawatha_conn = affiliate_hiawatha_conn_info()
            hiawatha_elem = SubElement(rules_elem, "mvpd")
            hiawatha_elem.set("ascp_client", "HBC_Workflow")
            hiawatha_elem.append(hiawatha_adi)
            hiawatha_elem.append(hiawatha_conn)
        except:
            print("error constructing HBC (Hiawatha)")

    if "Hotwire" in affiliate_list:
        # Hotwire is FASPEX
        try:
            hotwire_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            # hotwire_conn = affiliate_hotwire_conn_info()
            hotwire_elem = SubElement(rules_elem, "mvpd")
            hotwire_elem.set("workgroup_name", "Hotwire_Communications_WG")
            hotwire_elem.append(hotwire_adi)
            # hotwire_elem.append(elec_power_board_conn)
        except:
            print("error constructing Hotwire")

    if "Logic" in affiliate_list:
        try:
            logic_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            logic_conn = affiliate_logic_conn_info()
            logic_elem = SubElement(rules_elem, "mvpd")
            logic_elem.set("ascp_client", "Logic_Workflow")
            logic_elem.append(logic_adi)
            logic_elem.append(logic_conn)
        except:
            print("error constructing Logic")

    if "MCTV" in affiliate_list:
        try:
            # MCTV has a custom delivery subfolder
            # Update 12/2/2024: no they don't have a custom delivery folder.
            # mctv_delivery_folder_elem = source_root.find("./metadata_package/metadata[@name='mCTVDeliveryFolder']/value")
            # try:
            #     mctv_delivery_folder = mctv_delivery_folder_elem.text
            # except:
            #     mctv_delivery_folder = ''
            # mctv_adi = affiliate_default_adi(adi, metadata_movie, metadata_network, metadata_season, resolution)

            # Update 12/3/2024: MCTV will reject any XML that has attributes not defined in Cablelab spec.
            mctv_adi = affiliate_mctv_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            mctv_conn = affiliate_mctv_conn_info()
            mctv_elem = SubElement(rules_elem, "mvpd")
            mctv_elem.set("ascp_client", "MCTV_Workflow")
            mctv_elem.append(mctv_adi)
            mctv_elem.append(mctv_conn)
        except:
            print("error constructing MCTV")

    if "Mediacom" in affiliate_list:
        try:
            mediacom_adi = affiliate_mediacom_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            mediacom_conn = affiliate_mediacom_conn_info()
            mediacom_elem = SubElement(rules_elem, "mvpd")
            mediacom_elem.set("ascp_client", "Mediacom_Workflow")
            mediacom_elem.append(mediacom_adi)
            mediacom_elem.append(mediacom_conn)
        except:
            print("error constructing Mediacom")

    if "MobiTV" in affiliate_list:
        try:
            mobi_adi = affiliate_mobitv_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            mobi_conn = affiliate_mobitv_conn_info()
            mobi_elem = SubElement(rules_elem, "mvpd")
            mobi_elem.set("ascp_client", "MobiTV_Workflow")
            mobi_elem.append(mobi_adi)
            mobi_elem.append(mobi_conn)
        except:
            print("error constructing MobiTV")

    if "OzarksGo" in affiliate_list:
        try:
            ozarks_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            ozarks_conn = affiliate_ozarks_conn_info()
            ozarks_elem = SubElement(rules_elem, "mvpd")
            ozarks_elem.set("ascp_client", "OzarksGo_Workflow")
            ozarks_elem.append(ozarks_adi)
            ozarks_elem.append(ozarks_conn)
        except:
            print("error constructing OzarksGo")

    if "SECTV LehighValley" in affiliate_list:
        # SECTV LehighValley is FASPEX
        try:
            sectv_lehigh_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            # sectv_lehigh_conn = affiliate_hotwire_conn_info()
            sectv_lehigh_elem = SubElement(rules_elem, "mvpd")
            sectv_lehigh_elem.set("workgroup_name", "SECTV_LehighValley_WG")
            sectv_lehigh_elem.append(sectv_lehigh_adi)
            # sectv_elem.append(sectv_conn)
        except:
            print("error constructing SECTV LehighValley")

    if "SECTV Sparta NJ" in affiliate_list:
        # SECTV Sparta NJ is FASPEX
        try:
            sectv_sparta_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            # sectv_sparta_conn = affiliate_sectv_sparta_conn_info()
            sectv_sparta_elem = SubElement(rules_elem, "mvpd")
            sectv_sparta_elem.set("workgroup_name", "SECTV_Sparta_WG")
            sectv_sparta_elem.append(sectv_sparta_adi)
            # sectv_sparta_elem.append(sectv_sparta_conn)
        except:
            print("error constructing SECTV Sparta NJ")

    if "SECTV" in affiliate_list:
        try:
            sectv_adi = affiliate_sectv_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            sectv_conn = affiliate_sectv_conn_info()
            sectv_elem = SubElement(rules_elem, "mvpd")
            sectv_elem.set("ascp_client", "SECTV_WG")
            sectv_elem.append(sectv_adi)
            sectv_elem.append(sectv_conn)
        except:
            print("error constructing SECTV")

    if "Tangerine (Allo)" in affiliate_list:
        try:
            tangerine_adi = affiliate_tangerine_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            tangerine_conn = affiliate_tangerine_conn_info()
            tangerine_elem = SubElement(rules_elem, "mvpd")
            tangerine_elem.set("ascp_client", "Tangerine_Allo_Workflow")
            tangerine_elem.append(tangerine_adi)
            tangerine_elem.append(tangerine_conn)
        except:
            print("error constructing Tangerine Allo")

    if "TDS Broadband" in affiliate_list:
        try:
            tds_broadband_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            tds_broadband_conn = affiliate_tds_broadband_conn_info()
            tds_broadband_elem = SubElement(rules_elem, "mvpd")
            tds_broadband_elem.set("ascp_client", "TDS_Broadband_Workflow")
            tds_broadband_elem.append(tds_broadband_adi)
            tds_broadband_elem.append(tds_broadband_conn)
        except:
            print("error constructing TDS Broadband")

    if "Verizon Lab" in affiliate_list:
        # Verizon Lab uses same ADI as Verizon STB
        try:
            verizon_lab_adi = affiliate_verizon_stb_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            verizon_lab_conn = affiliate_verizon_lab_conn_info()
            verizon_lab_elem = SubElement(rules_elem, "mvpd")
            verizon_lab_elem.set("ascp_client", "Verizon_Lab_Workflow")
            verizon_lab_elem.append(verizon_lab_adi)
            verizon_lab_elem.append(verizon_lab_conn)
        except:
            print("error constructing Verizon Lab")

    if "Verizon STB" in affiliate_list:
        try:
            verizon_stb_adi = affiliate_verizon_stb_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            verizon_stb_conn = affiliate_verizon_stb_conn_info()
            verizon_stb_elem = SubElement(rules_elem, "mvpd")
            verizon_stb_elem.set("ascp_client", "Verizon_STB_Workflow")
            verizon_stb_elem.append(verizon_stb_adi)
            verizon_stb_elem.append(verizon_stb_conn)
        except:
            print("error constructing Verizon STB")

    if "Vermont Telephone" in affiliate_list:
        try:
            vermont_adi = affiliate_default_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            vermont_conn = affiliate_vermont_telephone_conn_info()
            vermont_elem = SubElement(rules_elem, "mvpd")
            vermont_elem.set("ascp_client", "Vermont_Telephone_Workflow")
            vermont_elem.append(vermont_adi)
            vermont_elem.append(vermont_conn)
        except:
            print("error constructing Vermont Telephone")

    if "Vubiquity WOW" in affiliate_list:
        try:
            # one for ASCP
            vubiquity_adi = affiliate_vubiquity_wow_adi(
                adi, metadata_movie, metadata_network, metadata_season, resolution
            )
            vubiquity_conn = affiliate_vubiquity_wow_conn_info()
            vubiquity_elem = SubElement(rules_elem, "mvpd")
            vubiquity_elem.set("ascp_client", "Vubiquity_WOW_Workflow")
            vubiquity_elem.append(vubiquity_adi)
            vubiquity_elem.append(vubiquity_conn)
        except:
            print("error constructing Vubiquity WOW (ASCP)")

    if "WOW Detroit" in affiliate_list:
        # per Eric Packer - 4/28/2025
        # WOW Detroit does not take FXM, FXX, or NGW, only NG and FX (HD and SD)
        if (
            network_name == "FXX"
            or network_name == "FXM"
            or network_name == "Nat Geo Wild"
        ):
            pass
        else:
            try:
                # WOW Detroit is now P2P (ascp)
                wow_adi = affiliate_wow_detroit_adi(
                    adi, metadata_movie, metadata_network, metadata_season, resolution
                )
                wow_detroit_conn = affiliate_wow_detroit_conn_info()
                wow_detroit_elem = SubElement(rules_elem, "mvpd")
                wow_detroit_elem.set("ascp_client", "WideOpenWest_Detroit_Workflow")
                wow_detroit_elem.append(wow_adi)
                wow_detroit_elem.append(wow_detroit_conn)
            except:
                print("error constructing WOW Detroit (ASCP)")

    if "WOW West Point" in affiliate_list:
        # per Eric Packer - 4/28/2025
        # WOW West Point does not take FXM, FXX, or NGW, only NG and FX (HD and SD)
        if (
            network_name == "FXX"
            or network_name == "FXM"
            or network_name == "Nat Geo Wild"
        ):
            pass
        else:
            try:
                # WOW West Point is now P2P (ascp)
                wow_adi = affiliate_wow_westpoint_adi(
                    adi, metadata_movie, metadata_network, metadata_season, resolution
                )
                wow_west_point_conn = affiliate_wow_west_point_conn_info()
                wow_west_point_elem = SubElement(rules_elem, "mvpd")
                wow_west_point_elem.set(
                    "ascp_client", "WideOpenWest_WestPoint_Workflow"
                )
                wow_west_point_elem.append(wow_adi)
                wow_west_point_elem.append(wow_west_point_conn)
            except:
                print("error constructing WOW West Point (ASCP)")

    prettyXMLString = prettify(output_root)
    with open(output_xml_path, "w", encoding="utf-8") as f:
        f.write(prettyXMLString)
