import os

import pytest


@pytest.fixture
def root_path():
    return os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))


@pytest.fixture
def get_files_to_test(root_path) -> list:
    project_base_path = root_path
    target_dir = os.path.join(project_base_path, "affiliates", "files", "input")

    all_files = [
        os.path.splitext(f)[0]
        for f in os.listdir(target_dir)
        if os.path.isfile(os.path.join(target_dir, f))
    ]

    return [target_dir, all_files]


@pytest.fixture
def dir_path(root_path):
    project_base_path = root_path

    def _build_path(dir_name: str) -> str:
        return os.path.join(
            project_base_path, "affiliates", "files", "output", dir_name
        )

    return _build_path


@pytest.fixture
def get_output_files_to_test(dir_path) -> dict:
    express_lane_target_dir = dir_path("express_lane")
    on_permises_target_dir = dir_path("on_permises")

    all_files_express_lane = [
        os.path.join(express_lane_target_dir, f)
        for f in os.listdir(express_lane_target_dir)
        if os.path.isfile(os.path.join(express_lane_target_dir, f))
    ]

    all_files_on_permises = [
        os.path.join(on_permises_target_dir, f)
        for f in os.listdir(on_permises_target_dir)
        if os.path.isfile(os.path.join(on_permises_target_dir, f))
    ]

    return {
        "all_files_express_lane": all_files_express_lane,
        "on_permises_target_dir": all_files_on_permises,
    }


@pytest.fixture
def output_report_path() -> str:
    return "/affiliates/files/output/report.txt"


@pytest.fixture
def output_path() -> str:
    return "/affiliates/files/output"
