from utils.LoggerService import logger_service
from utils.tasks.affiliates.helper import Affiliate<PERSON>asksHelper
from utils.tasks.affiliates.nested_obj import GlomObj


class TaskLogic:
    def __init__(self, global_variables):
        raw_data = global_variables.get("raw_data")
        self.task_config = global_variables.get("task_config")
        self.config_rules = global_variables.get("config_rules")
        self.affiliate_assambler = global_variables.get("affiliate_assambler")

        self.glom_obj = GlomObj()
        self.helper = AffiliateTasksHelper(raw_data)

    def skip_content(self) -> None:
        # STEP 1: Skip content
        logger_service.info("STEP 1: Skip content")
        try:
            for affiliates, values in self.task_config.get("affiliates").items():

                applied_rules = values.get("applied_rules", {})
                skip_affiliate_rule = applied_rules.get("skip_affiliate_rule")
                delivery_name = values.get("delivery_name")

                mvpd_list = (
                    self.affiliate_assambler.get("clients", {})
                    .get("rules", {})
                    .get("mvpd", [])
                )

                for matched_affiliate in mvpd_list[:]:
                    if (
                        matched_affiliate.get("@ascp_client") == delivery_name
                        or matched_affiliate.get("@workgroup_name") == delivery_name
                    ):

                        if skip_affiliate_rule:
                            logger_service.debug(
                                "The given affiliate will be skipped from current process networkname -> %s",
                                affiliates,
                            )
                            mvpd_list.remove(matched_affiliate)

        except Exception as e:
            logger_service.error(e)
            raise

    def apply_all_rules_category(self) -> None:
        # STEP 2: Apply Category Rules
        logger_service.info("STEP 2: Apply Category Rules")
        try:
            for _, values in self.task_config.get("affiliates", {}).items():

                mvpd_list = (
                    self.affiliate_assambler.get("clients", {})
                    .get("rules", {})
                    .get("mvpd", [])
                )

                delivery_name = values.get("delivery_name")
                applied_rules = values.get("applied_rules", {})

                category = applied_rules.get("split_rules", None)
                movie = applied_rules.get("apply_category_rules", {}).get("movie")
                season = applied_rules.get("apply_category_rules", {}).get("season")
                none_category = applied_rules.get("apply_category_rules", {}).get(
                    "category"
                )

                if category is None or category == []:
                    logger_service.info(
                        "No category to apply on data for Affiliate: %s", delivery_name
                    )
                    continue  # This skips to the next affiliate

                for matched_affiliate in mvpd_list[:]:
                    if (
                        matched_affiliate.get("@ascp_client") == delivery_name
                        or matched_affiliate.get("@workgroup_name") == delivery_name
                    ):
                        config_rules = self.config_rules.get("rules_category")
                        path_split = config_rules.get("path_split")
                        key_split = config_rules.get("key_split")
                        value_split = config_rules.get("value_split")

                        if none_category is None and None in movie and None in season:
                            logger_service.info(
                                "Skipping affiliate, no category/movie/season data, affiliate will be removed: %s",
                                delivery_name,
                            )
                            mvpd_list.remove(matched_affiliate)

                        else:
                            app_data_list = self.glom_obj.glom_obj(
                                matched_affiliate, path_split
                            )

                            # Remove existing Category entries
                            existing_indexes = [
                                index
                                for index, item in enumerate(app_data_list)
                                if item.get(key_split) == value_split
                            ]
                            for i in sorted(existing_indexes, reverse=True):
                                self.glom_obj.glom_delete_obj(
                                    data=matched_affiliate, path=path_split, index=i
                                )

                            # Add new category values
                            if category:
                                for _category in category:
                                    app_data_list.append(_category)

                            logger_service.info(
                                "For gvien affiliate: %s apply the next category/(es): %s",
                                delivery_name,
                                category,
                            )

        except Exception as e:
            logger_service.error(e)
            raise

    def removed_attributes(self) -> None:
        logger_service.info("Step 3: Removed Attributes Rules ")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                removed_attributes = values.get("remove_attributes")

                if removed_attributes:

                    keys = removed_attributes.get("keys")
                    path = removed_attributes.get("path")
                    filter_key = removed_attributes.get("filter_key")

                    for matched_affiliate in (
                        self.affiliate_assambler.get("clients", {})
                        .get("rules", {})
                        .get("mvpd", [])
                    ):

                        if (
                            matched_affiliate.get("@ascp_client") == delivery_name
                            or matched_affiliate.get("@workgroup_name") == delivery_name
                        ):

                            for k in keys:
                                app_data_list = self.glom_obj.glom_obj(
                                    matched_affiliate, path
                                )

                                title_index = next(
                                    (
                                        index
                                        for index, item in enumerate(app_data_list)
                                        if item.get(filter_key) == k
                                    ),
                                    None,
                                )

                                # If found, update the value
                                if title_index is not None:
                                    self.glom_obj.glom_delete_obj(
                                        data=matched_affiliate,
                                        path=path,
                                        index=title_index,
                                    )
                                    logger_service.info(
                                        "Removed Attributes for given affiliate: %s Attributes Removed: %s --> %s",
                                        delivery_name,
                                        path,
                                        keys,
                                    )
        except Exception as e:
            logger_service.error(e)
            raise

    def update_attributes(self) -> None:
        # Step 4: UPDATE ATTRIBUTES LOGIC
        logger_service.info("Step 4:  UPDATE ATTRIBUTES LOGIC")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                update_attributes = values.get("update_attributes")

                if update_attributes:
                    for update_attribute in update_attributes:

                        value_filter = update_attribute.get("value_filter")
                        key_filter = update_attribute.get("key_value_filter")
                        value_update = update_attribute.get("value_update")

                        for matched_affiliate in (
                            self.affiliate_assambler.get("clients", {})
                            .get("rules", {})
                            .get("mvpd", [])
                        ):

                            flatten_rules = self.helper.flatten_rules_helper(
                                matched_affiliate
                            )

                            if (
                                matched_affiliate.get("@ascp_client") == delivery_name
                                or matched_affiliate.get("@workgroup_name")
                                == delivery_name
                            ):
                                filter_sibling_tuples_by_value = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )
                                extract_single_path_by_key = (
                                    flatten_rules.extract_tuple_value(
                                        filter_sibling_tuples_by_value, key_filter
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        filter_sibling_tuples_by_value,
                                        f"{extract_single_path_by_key}{value_update}",
                                        key_filter,
                                    )
                                )
                                extract_specific_tuple = (
                                    flatten_rules.extract_specific_tuple(
                                        replace_tuples_value, key_filter
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, extract_specific_tuple
                                )
                                logger_service.info(
                                    "Update Attributes for given affiliate: %s Attributes Updated: %s",
                                    delivery_name,
                                    extract_specific_tuple,
                                )

        except Exception as e:
            logger_service.error(e)
            raise

    def replace_attributes(self) -> None:
        # Step 5: REPLACE ATTRIBUTES LOGIC
        logger_service.info("Step 5:  REPLACE ATTRIBUTES LOGIC")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                replace_attributes = values.get("replace_attributes")

                if replace_attributes:
                    for replace_attribute in replace_attributes:

                        key_value_filter = replace_attribute.get("key_value_filter")
                        value_filter = replace_attribute.get("value_filter")
                        filter_by = replace_attribute.get("filter_by")

                        for matched_affiliate in (
                            self.affiliate_assambler.get("clients", {})
                            .get("rules", {})
                            .get("mvpd", [])
                        ):

                            flatten_rules = self.helper.flatten_rules_helper(
                                matched_affiliate
                            )

                            flatten_raw_data_rules = (
                                self.helper.flatten_raw_data_rules_helper()
                            )

                            if (
                                matched_affiliate.get("@ascp_client") == delivery_name
                                or matched_affiliate.get("@workgroup_name")
                                == delivery_name
                            ):

                                by_key_value_filter = flatten_raw_data_rules.filter_sibling_tuples_by_value(
                                    key_value_filter
                                )
                                by_value_filter = (
                                    flatten_rules.filter_single_path_by_key(
                                        value_filter
                                    )
                                )

                                extract_single_path_by_key = (
                                    flatten_raw_data_rules.extract_tuple_value(
                                        by_key_value_filter, "value"
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        by_value_filter,
                                        extract_single_path_by_key,
                                        value_filter,
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )

                                logger_service.info(
                                    "Update Attributes for given affiliate: %s Attributes Updated: %s",
                                    delivery_name,
                                    replace_tuples_value,
                                )

                                by_value = flatten_raw_data_rules.filter_sibling_tuples_by_value(
                                    key_value_filter
                                )
                                filter_single_path_by_value = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )

                                extract_single_path_by_key = (
                                    flatten_raw_data_rules.extract_tuple_value(
                                        by_value, "value"
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        filter_single_path_by_value,
                                        extract_single_path_by_key,
                                        filter_by,
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )

                                logger_service.info(
                                    "Update Attributes for given affiliate: %s Attributes Updated: %s",
                                    delivery_name,
                                    replace_tuples_value,
                                )

        except Exception as e:
            logger_service.error(e)
            raise

    def limit_length(self) -> None:
        logger_service.info("Step 6: LIMIT LENGTH")

        try:
            for _, values in self.task_config.get("affiliates").items():

                limit_length = values.get("limit_length", {})
                value_filter = limit_length.get("value_filter")
                key_filter = limit_length.get("key_filter")
                limit_length_validation = limit_length.get("validation")
                delivery_name = values.get("delivery_name")

                if limit_length:

                    for matched_affiliate in (
                        self.affiliate_assambler.get("clients", {})
                        .get("rules", {})
                        .get("mvpd", [])
                    ):

                        flatten_rules = self.helper.flatten_rules_helper(
                            matched_affiliate
                        )

                        if (
                            matched_affiliate.get("@ascp_client") == delivery_name
                            or matched_affiliate.get("@workgroup_name") == delivery_name
                        ):

                            if limit_length_validation:

                                length = limit_length.get("length")

                                search_by = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )

                                extract_value = flatten_rules.extract_tuple_value(
                                    search_by, key_filter
                                )
                                extract_specific_tuple = (
                                    flatten_rules.extract_specific_tuple(
                                        search_by, key_filter
                                    )
                                )

                                value_limit_length = extract_value[: int(length)]
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        extract_specific_tuple, value_limit_length
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )
                                logger_service.info(
                                    "Limit Lnegth Attribute for given affiliate: %s Attributes: %s",
                                    delivery_name,
                                    replace_tuples_value,
                                )

        except Exception as e:
            logger_service.error(e)
            raise
