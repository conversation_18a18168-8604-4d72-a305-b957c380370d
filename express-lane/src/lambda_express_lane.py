from utils.config import settings_config
from utils.LoggerService import logger_service
from utils.schemas.dynamodb.express_lane import ExpressLaneConfig
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .express_lane_flow import express_lane_flow


def lambda_handler(event: dict, _: str) -> None:
    """
    Initializes all the services and configurations and runs the flow
    according to the configuration obtained.
    """

    logger_service.info("************ IZMA / Running Lambda Express Lane ************")
    logger_service.info(" IZMA / Running Lambda Express Lane with args: %s", event)

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    event_settings = settings_service.initialize("express-lane")
    config: ExpressLaneConfig = event_settings.get_config()  # type: ignore
    status_table_name = event_settings.get_status_table()
    secrets_manager = config.secrets_manager

    secrets_manager_utils = SecretsManagerUtils()
    secrets_managerUtils = secrets_manager_utils.get_secret_value(secrets_manager)

    s3_event_info = event.get("Records")[0]["s3"]
    file_name_from_s3 = s3_event_info.get("object", {}).get("key")
    bucket_name = s3_event_info.get("bucket", {}).get("name")

    rules = event_settings.configuration_rules.rules
    config_rules = rules.get("config_rules")

    status_tracker = StatusTrackerSchema(
        client_id=file_name_from_s3, landing_bucket=bucket_name
    )
    process_status_tracker = ProcessStatusTracker(status_table_name=status_table_name)

    process_status_tracker.create_initial_status(create_item=status_tracker)

    global_variables = {
        "id": status_tracker.client_id,
        "landing_bucket": bucket_name,
        "destination_bucket": config.destination_bucket,
        "output_filename": config.output_filename,
        "file_name_from_s3": file_name_from_s3,
        "secrets_manager": secrets_managerUtils,
        "timezone": config.timezone,
        "event_settings_rules": rules,
        "config_rules": config_rules,
    }

    logger_service.info(
        "Izma started with args: s3_event_info: %s, status_table_name: %s",
        s3_event_info,
        status_table_name,
    )

    try:
        process_status_tracker.mark_as_running(client_id=file_name_from_s3)

        express_lane_flow(global_variables=global_variables)

        process_status_tracker.mark_as_completed(client_id=file_name_from_s3)

    except BaseException as error:
        error_message = f"{error}"
        logger_service.error(
            "IZMA ERROR: %s -- event settings: %s",
            error_message,
            event_settings.event_project_name,
        )
        process_status_tracker.mark_as_failed(
            client_id=file_name_from_s3, error_message=error_message
        )

    logger_service.info("IZMA / Lambda Express Lane finished")


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "s3": {
                        "object": {
                            "key": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
                        },
                        "bucket": {"name": "express-lane-test"},
                    }
                }
            ]
        },
        "",
    )
