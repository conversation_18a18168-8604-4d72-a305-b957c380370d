import copy

from utils.extract import extract
from utils.load import load
from utils.LoggerService import logger_service

from .task_logic import TaskLogic


class TaskAffiliateAssembler:
    def __init__(self, global_variables: dict):
        self.raw_data = global_variables.get("raw_data")
        self.secrets = global_variables.get("secrets_manager")
        self.task_config = global_variables.get("task_config")
        self.adi_data = copy.deepcopy(self.raw_data.get("master", {}).get("ADI"))

    def assemble(self) -> dict:
        obj = {"mvpd": []}

        for _, values in self.task_config.get("affiliates", {}).items():
            delivery_name = values.get("delivery_name")
            faspex = values.get("faspex")
            tar = values.get("tar")

            if faspex:
                logger_service.info("Adding Faspex mvpd: %s", delivery_name)
                obj["mvpd"].append(
                    {
                        "@workgroup_name": delivery_name,
                        "ADI": copy.deepcopy(self.adi_data),
                    }
                )
            else:
                mvpd_block = {
                    "@ascp_client": delivery_name,
                    "ADI": copy.deepcopy(self.adi_data),
                    "ascp": self.secrets.get(delivery_name),
                }
                if tar:
                    mvpd_block["changes"] = {"format": "TAR"}
                    logger_service.info("Adding TAR mvpd: %s", delivery_name)
                else:
                    logger_service.info("Adding ASCP mvpd: %s", delivery_name)
                obj["mvpd"].append(mvpd_block)

        return {"clients": {"rules": obj}}


class TaskExtractLoad:

    def extract_obj(self, file_name_from_s3, landing_bucket) -> dict:
        logger_service.info("Running Task: Extract Obj")

        task_def = {}

        task_def["extract"] = {
            "task": "s3_extract",
            "params": {
                "key": file_name_from_s3,
                "bucket": landing_bucket,
            },
        }
        prev = extract(task_def)

        return prev

    def load_obj(
        self, key, body, destination_bucket, output_filename, timezone
    ) -> dict:
        logger_service.info("Running Task: Load Obj")

        task_def = {}

        task_def["load"] = {
            "task": "s3_upload",
            "params": {
                "Key": key,
                "Bucket": destination_bucket,
                "Body": body,
                "filename_pattern": output_filename,
                "timezone": timezone,
            },
        }

        load(task_def)

        return {"message": "load susccessfull"}


class Tasks:
    def __init__(self, global_variables) -> None:
        self.task_logic = TaskLogic(global_variables)

    def skip_content(self) -> None:
        logger_service.info("Running Task: skip_content")
        self.task_logic.skip_content()

    def apply_category_rules(self) -> None:
        logger_service.info("Running Task: apply_category_rules")
        self.task_logic.apply_all_rules_category()

    def removed_attributes(self) -> None:
        logger_service.info("Running Task: removed_attributes")
        self.task_logic.removed_attributes()

    def update_attributes(self) -> None:
        logger_service.info("Running Task: update_attributes")
        self.task_logic.update_attributes()

    def replace_attributes(self) -> None:
        logger_service.info("Running Task: replace_attributes")
        self.task_logic.replace_attributes()

    def limit_length(self) -> None:
        logger_service.info("Running Task: limit_length")
        self.task_logic.limit_length()
