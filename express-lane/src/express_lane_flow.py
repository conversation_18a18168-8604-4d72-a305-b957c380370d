from collections import OrderedDict
from copy import deepcopy

import xmltodict
from utils.LoggerService import logger_service
from utils.tasks.data_processing import convert_from_dict

from .task_config import task_config_assembler
from .tasks import TaskAffiliateAssembler, TaskExtractLoad, Tasks


def express_lane_flow(global_variables: dict) -> None:
    """
    Initializes all the services and configurations and runs the flow
    according to the configuration obtained.
    """

    logger_service.info("************ IZMA / Running Lambda Express Lane ************")

    event_settings_rules = global_variables.get("event_settings_rules")
    landing_bucket = global_variables.get("landing_bucket")
    destination_bucket = global_variables.get("destination_bucket")
    output_filename = global_variables.get("output_filename")
    timezone = global_variables.get("timezone")
    file_name_from_s3 = global_variables.get("file_name_from_s3")

    extract_task_load = TaskExtractLoad()

    prev = extract_task_load.extract_obj(file_name_from_s3, landing_bucket)

    _raw_data = xmltodict.parse(prev, dict_constructor=OrderedDict)

    affiliates = (
        _raw_data.get("master", {}).get("affiliate_list", {}).get("affiliate", {})
    )

    # Ensure it's always a list
    if not isinstance(affiliates, list):
        affiliates = [affiliates]

    raw_data = deepcopy(_raw_data)

    filtered_affiliates = {
        k: event_settings_rules[k] for k in affiliates if k in event_settings_rules
    }

    global_variables["raw_data"] = raw_data

    task_config_assambler = task_config_assembler(
        global_variables=global_variables, filtered_affiliates=filtered_affiliates
    )

    global_variables["task_config"] = task_config_assambler

    affiliate_assambler = TaskAffiliateAssembler(
        global_variables=global_variables
    ).assemble()

    global_variables["affiliate_assambler"] = affiliate_assambler

    tasks = Tasks(global_variables)

    # STEP 1: Filter by Skip content
    tasks.skip_content()

    # STEP 2: Apply Category Rules
    tasks.apply_category_rules()

    # Step 3: Removed Attributes Rules
    tasks.removed_attributes()

    # Step 4: UPDATE ATTRIBUTES LOGIC
    tasks.update_attributes()

    # Step 5: REPLACE ATTRIBUTES LOGIC
    tasks.replace_attributes()

    # Step 6: Limit length
    tasks.limit_length()

    prev = convert_from_dict(affiliate_assambler, "xml")

    file_name = file_name_from_s3[:-4]
    file_key = f"{file_name}_delivery.xml"
    file_obj = f"results/{file_key}"

    # Task Load Object
    extract_task_load.load_obj(
        key=file_obj,
        body=prev,
        destination_bucket=destination_bucket,
        output_filename=output_filename,
        timezone=timezone,
    )

    logger_service.info("IZMA / Lambda Express Lane finished")
