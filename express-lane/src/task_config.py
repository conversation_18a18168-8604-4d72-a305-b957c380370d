from collections import OrderedDict
from typing import Dict, List, Union

from utils.LoggerService import logger_service
from utils.tasks.affiliates.nested_obj import FlattenObj, FlattenRules, GlomObj


class TaskConfig:
    def __init__(self, raw_data, filtered_affiliates: Dict):
        self.glom_obj = GlomObj()
        self._flatten_obj = FlattenObj(raw_data)
        self.flatten_data = self._flatten_obj.flatten_structure_obj()
        self.flatten_rules = FlattenRules(self.flatten_data)
        self.raw_data = raw_data
        self.obj = {}
        self.filtered_affiliates = filtered_affiliates

        # Initialize task config once
        self.task_config = self._task_config()

    def initialize(
        self,
        network_name_path,
        filter_value,
        key_value,
        metadata_movie_path,
        metadata_season_path,
    ):
        self._rule_skip_content(network_name_path)
        self._rule_filter_category(filter_value, key_value)
        self._apply_category_rules(metadata_movie_path, metadata_season_path)
        self._split_rules()

        config_task = self.task_config

        return config_task

    def _task_config(self) -> Dict:
        try:
            # Copy affiliates dict so we don't modify original filtered_affiliates directly
            affiliates_copy = {}

            for affiliate, data in self.filtered_affiliates.items():
                # Defensive copy if needed, or assume data is dict
                affiliate_data = data.copy() if isinstance(data, dict) else {}

                # Add applied_rules key to each affiliate dict
                affiliate_data["applied_rules"] = {
                    "skip_affiliate_rule": False,
                    "filter_category_rule": [],
                    "apply_category_rules": {},
                    "split_rules": {},
                }

                affiliates_copy[affiliate] = affiliate_data

            # Build root-level dict with is_hd key and affiliates nested
            task_config = {
                "is_hd": "",  # root-level new string key
                "affiliates": affiliates_copy,
            }

            return task_config

        except Exception as e:
            logger_service.error("Unexpected error in _task_config: %s", e)
            raise

    def _rule_skip_content(self, network_name_path: str) -> Dict:
        logger_service.info(
            "Filter by Skip content by network_name_path: %s", network_name_path
        )
        network_name_value = self.glom_obj.glom_obj(
            self.raw_data, network_name_path, default=None
        )

        for _, config in self.task_config["affiliates"].items():
            skip_content = config.get("skip_content", {})
            should_skip = skip_content.get(
                "validation"
            ) is True and network_name_value in skip_content.get("values_skip", [])
            config["applied_rules"]["skip_affiliate_rule"] = should_skip

        return self.task_config

    def _rule_filter_category(self, key_value: str, filter_value: str) -> str:
        # STEP 2: Filter by if HD or SD
        logger_service.info(
            "Filter by HD or SD content by key_value: %s -- filter_value: %s",
            key_value,
            filter_value,
        )

        _by_value = self.flatten_rules.filter_sibling_tuples_by_value(key_value)
        _result_value = self.flatten_rules.extract_tuple_value(_by_value, filter_value)

        self.task_config["is_hd"] = _result_value

        category_keys = {
            "Y": ["HD_movies_category", "HD_series_category"],
            "N": ["SD_movies_category", "SD_series_category"],
        }.get(_result_value, [])

        # Update each affiliate
        for _, data in self.task_config.get("affiliates", {}).items():
            value_list = [
                v for k, v in data.items() if k in category_keys and v not in ("", None)
            ]

            if "applied_rules" not in data:
                data["applied_rules"] = {}

            data["applied_rules"]["filter_category_rule"] = value_list

    def _apply_category_rules(
        self,
        metadata_movie_path: str,
        metadata_season_path: str,
    ) -> List:
        logger_service.info("Apply Category Rules  - selected_categories:")

        target_category_metadata = {}

        for key, data in self.task_config.get("affiliates", {}).items():
            value_list = data.get("applied_rules").get("filter_category_rule")
            metadata_movie_value = self.glom_obj.glom_obj(
                self.raw_data, metadata_movie_path, default=[]
            )
            metadata_season_value = self.glom_obj.glom_obj(
                self.raw_data, metadata_season_path, default=[]
            )

            if value_list:
                filtered_metadata_movie_value = self.glom_obj.glom_filter_by_list(
                    metadata_movie_value, value_list
                )
                filtered_metadata_season_value = self.glom_obj.glom_filter_by_list(
                    metadata_season_value, value_list
                )

                # Add [None] only if BOTH are empty
                if (
                    not filtered_metadata_movie_value
                    and not filtered_metadata_season_value
                ):
                    filtered_metadata_movie_value = [None]
                    filtered_metadata_season_value = [None]
            else:
                filtered_metadata_movie_value = []
                filtered_metadata_season_value = []

            # New logic: if one list is empty, print message and add key 'category'
            if not filtered_metadata_movie_value or not filtered_metadata_season_value:
                if not filtered_metadata_movie_value:
                    logger_service.info(
                        f"For key '{key}': filtered_metadata_movie_value is empty."
                    )
                if not filtered_metadata_season_value:
                    logger_service.info(
                        f"For key '{key}': filtered_metadata_season_value is empty."
                    )

                # Decide which list to keep (non-empty one), or combine both if you want
                # Here I will pick the non-empty list, or [None] if both empty
                combined_list = (
                    filtered_metadata_movie_value
                    or filtered_metadata_season_value
                    or [None]
                )

                raw_path = self.flatten_rules.filter_sibling_tuples_by_value("Category")

                data["applied_rules"]["apply_category_rules"] = {
                    "category": combined_list,
                    "category_raw_path": raw_path,
                }
            else:
                # Both lists are non-empty, keep both keys
                data["applied_rules"]["apply_category_rules"] = {
                    "category": None,
                    "movie": filtered_metadata_movie_value,
                    "season": filtered_metadata_season_value,
                }

        return target_category_metadata

    def _transform_split_value(self, item: Union[str, dict]) -> List[OrderedDict]:
        if isinstance(item, str):
            return [
                OrderedDict(
                    [("@App", "MOD"), ("@Name", "Category"), ("@Value", part.strip())]
                )
                for part in item.split("|")
            ]
        return [item]

    def _split_rules(self) -> Dict:
        logger_service.info("Split Rules - target_metadata:")
        for _, inner_dict in self.task_config.get("affiliates", {}).items():
            category_value = (
                inner_dict.get("applied_rules")
                .get("apply_category_rules", {})
                .get("category")
            )
            if isinstance(category_value, list):
                apply_split_rules = [
                    result
                    for item in category_value
                    for result in self._transform_split_value(item)
                    if result and any(result.values())
                ]

                inner_dict["applied_rules"]["split_rules"] = apply_split_rules


def task_config_assembler(global_variables, filtered_affiliates):
    raw_data = global_variables.get("raw_data")
    config_rules = global_variables.get("config_rules").get("task_config")
    network_name_path = config_rules.get("network_name_path")
    filter_value = config_rules.get("filter_value")
    key_value = config_rules.get("key_value")
    metadata_movie_path = config_rules.get("metadata_movie_path")
    metadata_season_path = config_rules.get("metadata_season_path")

    task_config = TaskConfig(raw_data=raw_data, filtered_affiliates=filtered_affiliates)

    return task_config.initialize(
        network_name_path,
        filter_value,
        key_value,
        metadata_movie_path,
        metadata_season_path,
    )
