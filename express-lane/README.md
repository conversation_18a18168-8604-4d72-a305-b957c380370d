# express-lane
full documentation:
https://confluence.disney.com/pages/viewpage.action?spaceKey=REACH&title=Code+Documentation+Express+Lane

## Getting started

## Architecture
```
|nielsen-iris-integration/
|   └──express-lane/
|       ├── src/
|           └── __init__.py
|           └── lambda_express_lane.py
|           └── lambda_express_flow.py
│           └── application
|               └── services
|                   └── __init__.py
|                   └── delete_tag_or_attributes.py
|           ├── tests
|               └── __init__.py
|               └── conftest.py
|                   └── __init__.py
|               └── unit
|                   └── __init__.py
|                   └── conftest.py
|                   └── test_dynamodb.py
|                   └── test_delete_tag_or_attributes.py
|                   └── test_s3.py 
|       ├── README.md
|   shared_layer/   
```
## Run AWS
- upload the desire XML file into S3 (S3 will trigger the lambda) and the Lambda will be trigger

## Run locally
- ./express-lane
    - python lambda_express_lane.py
    Note:
        -  changed key in __name__ == "__main__" for desire S3 bucket

- ./express-lane
     - pytest

## Test and Deploy
- ./shared_layer
    - pip install -e .
    - pytest

- ./express-lane
     - pytest

## Tools
- Python 3.12
- Boto3
- AWS
- Pycharm CE
- SAM CLI

## How to generate your AWS Secrets?

### Step 1: Sign in to the AWS Management Console

1. Open the AWS Management Console at [AWS Console](https://aws.amazon.com/console/).
2. Sign in with your credentials.

### Step 2: Navigate to IAM Service

1. In the AWS Management Console, search for "IAM" in the services search bar and open the **IAM Dashboard**.

### Step 3: Create or Select an IAM User

1. In the left navigation pane, choose **Users**.
2. You can either create a new user or select an existing user for whom you want to generate access keys.

#### To Create a New User:

1. Click **Add user**.
2. Enter a **User name**.
3. Dont check the box "Provide user access to the AWS Management Console - optional".
4. Click **Next: Permissions** to attach necessary permissions directly or assign the user to a group with the appropriate permissions.
5. Select Attach policies directly.
6. You can select the policies according to the lambda necessities.
7. You can select at the first time AWSLambda_FullAccess
8. Click **Next**
9. Click **Create User**


### Step 4: Generate Access Keys

1. Select the user name from the list to open the user’s summary page.
2. Go to the **Security credentials** tab.
3. In the **Access keys** section, click **Create access key**.
4. In **Use case** select **Local code**
5. Check the box "I understand the above recommendation and want to proceed to create an access key."
6. Click **Next**
7. Click **Create Access Key**
8. In the dialog that appears, click **Show** to see the **Access Key ID** and **Secret Access Key**.

## ENVIRONMENT VARIABLES
### Define de following env variables in you .env (local):
- **STAGE**: The stage where you are running this (production, development or localyournamelastname)
- **AWS_ACCESS_KEY_ID**: Your aws secrete
- **AWS_SECRET_ACCESS_KEY**: Your aws access key
- **AWS_DEFAULT_REGION**: Your aws region

