Globals:
  Function:
    Runtime: python3.12
    Timeout: 900
    MemorySize: 128
    Layers:
      - !Ref UtilsLayer

Resources:
  UtilsLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: UtilsLayer
      Description: A shared layer for utility functions
      ContentUri: shared_layer/
      CompatibleRuntimes:
        - python3.12
    Metadata:
      BuildMethod: makefile

  AkamaiFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: akamai
      Handler: src/akamai_linode_lambda.lambda_handler
      CodeUri: akamai_linode/
      Description: Akamai Lambda Function
      Role: !Sub arn:aws:iam::${AWS::AccountId}:role/tacdev

  BebanjoFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: bebanjo
      Handler: src/bebanjo_lambda.lambda_handler
      CodeUri: bebanjo/
      Description: Bebanjo Lambda Function
      Role: !Sub arn:aws:iam::${AWS::AccountId}:role/tacdev

  ExpressLaneFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: express
      Handler: src/lambda_express_lane.lambda_handler
      CodeUri: express-lane/
      Description: ExpressLane Lambda Function
      Role: !Sub arn:aws:iam::${AWS::AccountId}:role/tacdev

  Gam_ConnectorFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: gam_connector
      Handler: src/lambda_gam_connector.lambda_handler
      CodeUri: gam_connector/
      Description: Gam_Connector Lambda Function
      Role: !Sub arn:aws:iam::${AWS::AccountId}:role/tacdev
