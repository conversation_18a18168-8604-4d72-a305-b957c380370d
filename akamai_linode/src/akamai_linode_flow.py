from typing import Any, Optional

from utils.extract import plain_extract
from utils.flow_manager import FlowManager
from utils.schemas.lambdas import EventSchema
from utils.task_handlers import TaskExtractData, TaskLinodeLoad


def flow(event: EventSchema, global_variables: Optional[dict[str, Any]]) -> Any:
    """
    Lambda function moving files between buckets.
    Takes a JSON file from S3 bucket, and saves it as YAML.

    Args:
        event (Dict): AWS Lambda event containing S3 trigger information
        global_variables (Optional[Dict]): AWS Lambda context
    """

    global_variables["connector"] = plain_extract

    _flow = FlowManager(
        name="akamai_linode",
        tasks=[TaskExtractData(), TaskLinodeLoad()],
        event=event,
        global_variables=global_variables,
    )
    return _flow()
