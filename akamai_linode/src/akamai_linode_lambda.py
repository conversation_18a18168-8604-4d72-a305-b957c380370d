from typing import Any

from utils.config import settings_config
from utils.LoggerService import logger_service
from utils.RetryHandler import <PERSON><PERSON><PERSON>and<PERSON>
from utils.schemas.dynamodb.akamai import AkamaiConfig
from utils.schemas.lambdas import EventSchema
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .akamai_linode_flow import flow


def lambda_handler(event: dict[str, Any], _: str) -> None:
    """
    Lambda function moving files between buckets.
    Takes a JSON file from S3 bucket, and saves it as YAML.
    """

    logger_service.info("************ IZMA / lambda akamai linode started ************")
    logger_service.info("IZMA / lambda akamai linode with args: \n %s", event)

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    event_settings = settings_service.initialize("akamai-linode")
    config: AkamaiConfig = event_settings.get_config()  # type: ignore
    status_table_name = event_settings.get_status_table()

    event_object = EventSchema(**event)  # type: ignore
    file_name_from_s3 = event_object.get_file_name_from_s3()
    bucket_name = event_object.get_bucket_name()

    status_tracker = StatusTrackerSchema(
        client_id=file_name_from_s3, landing_bucket=bucket_name
    )
    process_status_tracker = ProcessStatusTracker(status_table_name)
    process_status_tracker.create_initial_status(create_item=status_tracker)

    global_variables: dict[str, str | AkamaiConfig] = {
        "landing_bucket": bucket_name,
        "destination_bucket": config.destination_bucket,
        "file_name": file_name_from_s3,
        "secret_key": config.secret_key,
        "config": config,
    }

    try:
        process_status_tracker.mark_as_running(file_name_from_s3)

        flow(event_object, global_variables)

        process_status_tracker.mark_as_completed(file_name_from_s3)

    except BaseException as error:
        logger_service.error(
            "IZMA ERROR: %s, event_project: %s",
            error,
            event_settings.event_project_name,
        )
        error_message = f"{error}"
        process_status_tracker.mark_as_failed(file_name_from_s3, error_message)

        retry_handler = RetryHandler(event_settings.retries)
        retry_handler.execute(flow, event_settings, global_variables)

    logger_service.info("IZMA / lambda akamai linode finished")


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "s3": {
                        "object": {
                            "key": "12491353_2160_3840_30fps.mp4",
                        },
                        "bucket": {"name": "reach-elements-sbx"},
                    }
                }
            ]
        },
        "",
    )
